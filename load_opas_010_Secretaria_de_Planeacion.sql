-- CARGA OPAs PARA Secretaria de Planeacion (45 OPAs)
BEGIN;

INSERT INTO opas (code, name, description, dependency_id, subdependency_id, is_active)
SELECT v.code, v.name, v.description, d.id, s.id, true
FROM (VALUES
  ('1', 'Plan anticorrupcion', 'OPA 1: Plan anticorrupcion', '010', '010'),
  ('2', 'Información referente a patrimonio de predios en el municipio', 'OPA 2: Información referente a patrimonio de predios en el municipio', '010', '010'),
  ('1', 'Documentación y procedimientos relacionados con el SISBEN (Encuesta, inclusion en la base, retiro)', 'OPA 1: Documentación y procedimientos relacionados con el SISBEN (Encuesta, inclusion en la base, retiro)', '010', '011'),
  ('2', 'Solicitud de cartografias del municipio', 'OPA 2: Solicitud de cartografias del municipio', '010', '011'),
  ('3', 'Certificado de nomenclatura', 'OPA 3: Certificado de nomenclatura', '010', '011'),
  ('4', 'Georreferenciación', 'OPA 4: Georreferenciación', '010', '011'),
  ('5', 'Documentación y procedimientos relacionados con Estratificación', 'OPA 5: Documentación y procedimientos relacionados con Estratificación', '010', '011'),
  ('1', 'Plan operativo anual de inversiones', 'OPA 1: Plan operativo anual de inversiones', '010', '012'),
  ('2', 'Información respecto a las politicas publicas del municipio', 'OPA 2: Información respecto a las politicas publicas del municipio', '010', '012'),
  ('3', 'Regalias', 'OPA 3: Regalias', '010', '012'),
  ('4', 'Informacion del plan de desarrollo general (lo especifico puede dirigirse a la dependencia requerida)', 'OPA 4: Informacion del plan de desarrollo general (lo especifico puede dirigirse a la dependencia requerida)', '010', '012'),
  ('1', 'Concepto de uso de suelos', 'OPA 1: Concepto de uso de suelos', '010', '013'),
  ('2', 'Conceptos de no riesgo', 'OPA 2: Conceptos de no riesgo', '010', '013'),
  ('3', 'Conceptos de reserva vial', 'OPA 3: Conceptos de reserva vial', '010', '013'),
  ('4', 'Certificado de riesgos de predios', 'OPA 4: Certificado de riesgos de predios', '010', '013'),
  ('5', 'Concepto de norma urbanistica', 'OPA 5: Concepto de norma urbanistica', '010', '013'),
  ('6', 'Certificado de desafectación de Plusvalía, Participación en Plusvalia, Cancelación anotación en certificado de tradición', 'OPA 6: Certificado de desafectación de Plusvalía, Participación en Plusvalia, Cancelación anotación en certificado de tradición', '010', '013'),
  ('7', 'Inquietudes respecto al Plan de Ordenamiento Territorial (documentación, cartografía)', 'OPA 7: Inquietudes respecto al Plan de Ordenamiento Territorial (documentación, cartografía)', '010', '013'),
  ('8', 'Formulación, radicación o ajustes a proyectos de plan parcial', 'OPA 8: Formulación, radicación o ajustes a proyectos de plan parcial', '010', '013'),
  ('9', 'Legalización urbanística de asentamientos humanos', 'OPA 9: Legalización urbanística de asentamientos humanos', '010', '013'),
  ('10', 'Concepto- Certificado de paramentacion', 'OPA 10: Concepto- Certificado de paramentacion', '010', '013'),
  ('1', 'Permiso de rompimiento de vías o intervención de espacio publico', 'OPA 1: Permiso de rompimiento de vías o intervención de espacio publico', '010', '014'),
  ('2', 'Prorroga de intervención de espacio público', 'OPA 2: Prorroga de intervención de espacio público', '010', '014'),
  ('3', 'Todo lo relacionado con Licencias Urbanísticas', 'OPA 3: Todo lo relacionado con Licencias Urbanísticas', '010', '014'),
  ('4', 'Correción, revalidación y prorroga de licencia urbanistica', 'OPA 4: Correción, revalidación y prorroga de licencia urbanistica', '010', '014'),
  ('5', 'Radicación Documentos para el inicio de actividades de enajenación de ventas de vivienda', 'OPA 5: Radicación Documentos para el inicio de actividades de enajenación de ventas de vivienda', '010', '014'),
  ('6', 'Registro de enajenación', 'OPA 6: Registro de enajenación', '010', '014'),
  ('7', 'Matricula del arrendador', 'OPA 7: Matricula del arrendador', '010', '014'),
  ('8', 'Ajuste de cotas y areas', 'OPA 8: Ajuste de cotas y areas', '010', '014'),
  ('9', 'Aprobacion de planos de propiedad horizontal', 'OPA 9: Aprobacion de planos de propiedad horizontal', '010', '014'),
  ('10', 'Aprobacion de piscinas', 'OPA 10: Aprobacion de piscinas', '010', '014'),
  ('11', 'Autorizacion para el movimiento de tierras', 'OPA 11: Autorizacion para el movimiento de tierras', '010', '014'),
  ('12', 'Copia certificada de planos (del 2015 en adelante)', 'OPA 12: Copia certificada de planos (del 2015 en adelante)', '010', '014'),
  ('13', 'Licencia de localizacion de equipamiento de espacio publico', 'OPA 13: Licencia de localizacion de equipamiento de espacio publico', '010', '014'),
  ('14', 'Cancelación matricula del arrendador', 'OPA 14: Cancelación matricula del arrendador', '010', '014'),
  ('15', 'Control de constructoras e inmobiliarias', 'OPA 15: Control de constructoras e inmobiliarias', '010', '014'),
  ('16', 'Estados Financieros constructoras', 'OPA 16: Estados Financieros constructoras', '010', '014'),
  ('17', 'Correción, revalidación y prorroga de licencia urbanistica', 'OPA 17: Correción, revalidación y prorroga de licencia urbanistica', '010', '014'),
  ('18', 'Constancia ejecutoria (2024 en adelante)', 'OPA 18: Constancia ejecutoria (2024 en adelante)', '010', '014'),
  ('1', 'Todo lo concerniente con servicios públicos domiciliarios (energía eléctrica, telefonía pública básica conmutada, telefonía móvil rural, y distribución de gas combustible ley 142)', 'OPA 1: Todo lo concerniente con servicios públicos domiciliarios (energía eléctrica, telefonía pública básica conmutada, telefonía móvil rural, y distribución de gas combustible ley 142)', '010', '015'),
  ('2', 'Normas, regulacion  del centro de acopio y residuos aprovechables', 'OPA 2: Normas, regulacion  del centro de acopio y residuos aprovechables', '010', '015'),
  ('3', 'Traslado a empresas de energía de casos de arboles que se cruzan con red electrica y generan riesgo', 'OPA 3: Traslado a empresas de energía de casos de arboles que se cruzan con red electrica y generan riesgo', '010', '015'),
  ('4', 'Alumbrado publico  (Remitir facturas de alumbrado público)', 'OPA 4: Alumbrado publico  (Remitir facturas de alumbrado público)', '010', '015'),
  ('5', 'Solicitudes de paz y salvo de los permisos de intervención. Estos deberán radicarse como derecho de petición.', 'OPA 5: Solicitudes de paz y salvo de los permisos de intervención. Estos deberán radicarse como derecho de petición.', '010', '015'),
  ('6', 'Infraestructura para antenas de telecomunicaciones: Estos deben radicarse como Autorización como infraestructura de Telecomunicaciones.', 'OPA 6: Infraestructura para antenas de telecomunicaciones: Estos deben radicarse como Autorización como infraestructura de Telecomunicaciones.', '010', '015')
) AS v(code, name, description, dep_code, subdep_code)
JOIN dependencies d ON d.code = v.dep_code
LEFT JOIN subdependencies s ON s.dependency_id = d.id AND s.code = v.subdep_code
WHERE NOT EXISTS (
    SELECT 1 FROM opas o 
    WHERE o.code = v.code 
    AND o.dependency_id = d.id 
    AND (o.subdependency_id = s.id OR (o.subdependency_id IS NULL AND s.id IS NULL))
);

-- Verificar carga
SELECT 
    'Secretaria de Planeacion' as dependencia,
    COUNT(*) as opas_cargados
FROM opas o
JOIN dependencies d ON o.dependency_id = d.id
WHERE d.code = '010';

COMMIT;
