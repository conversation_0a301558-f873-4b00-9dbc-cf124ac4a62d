-- CARGA OPAs PARA Despacho Alcalde (42 OPAs)
BEGIN;

INSERT INTO opas (code, name, description, dependency_id, subdependency_id, is_active)
SELECT v.code, v.name, v.description, d.id, s.id, true
FROM (VALUES
  ('1', 'Si por solicitud del ciudadano exige la radicacion al alcalde, se debe indicar en el campo de observaciones "Radicado al despacho por solicitud del ciudadano"', 'OPA 1: Si por solicitud del ciudadano exige la radicacion al alcalde, se debe indicar en el campo de observaciones "Radicado al despacho por solicitud del ciudadano"', '000', '000'),
  ('2', 'Las solicitudes de citas al Alcalde y hojas de vida que requieran ser conocidas por el Alcalde, al momento de radicar seleccionar el tipo documental "REMISIÓN DE DOCUMENTOS".', 'OPA 2: Las solicitudes de citas al Alcalde y hojas de vida que requieran ser conocidas por el Alcalde, al momento de radicar seleccionar el tipo documental "REMISIÓN DE DOCUMENTOS".', '000', '000'),
  ('3', 'Las comunicaciones que se reciban de las IAS (Contraloria, Procuraduria, Fiscalia, etc) se deben radicar directamente al Despacho del Alcalde.', 'OPA 3: Las comunicaciones que se reciban de las IAS (Contraloria, Procuraduria, Fiscalia, etc) se deben radicar directamente al Despacho del Alcalde.', '000', '000'),
  ('4', 'Toma de juramento para nacionalidad colombiana', 'OPA 4: Toma de juramento para nacionalidad colombiana', '000', '000'),
  ('5', 'TEMAS RELACIONADOS CON EL RIO BOGOTÁ, Sentencia del rio Bogotá, temas relacionados con el rio Bogotá (debe ir con copia a defensa judicial)', 'OPA 5: TEMAS RELACIONADOS CON EL RIO BOGOTÁ, Sentencia del rio Bogotá, temas relacionados con el rio Bogotá (debe ir con copia a defensa judicial)', '000', '000'),
  ('6', 'Temas relacionados con la Planta de Tratamiento de Aguas Residuales (PTAR)', 'OPA 6: Temas relacionados con la Planta de Tratamiento de Aguas Residuales (PTAR)', '000', '000'),
  ('1', 'Certificado de residencia', 'OPA 1: Certificado de residencia', '000', '001'),
  ('2', 'Concepto juridico de las situaciones administrativas de la entidad', 'OPA 2: Concepto juridico de las situaciones administrativas de la entidad', '000', '001'),
  ('3', 'Inscripción de la propiedad horizontal', 'OPA 3: Inscripción de la propiedad horizontal', '000', '001'),
  ('4', 'Inscripción o cambio del representante legal y/o revisor fiscal de la propiedad horizontal -certificación personería juridica', 'OPA 4: Inscripción o cambio del representante legal y/o revisor fiscal de la propiedad horizontal -certificación personería juridica', '000', '001'),
  ('5', 'Registro de extinción de la propiedad horizontal', 'OPA 5: Registro de extinción de la propiedad horizontal', '000', '001'),
  ('6', 'Certificado de registros de marca', 'OPA 6: Certificado de registros de marca', '000', '001'),
  ('7', 'Solicitudes de información relacionada con proyectos de acuerdo', 'OPA 7: Solicitudes de información relacionada con proyectos de acuerdo', '000', '001'),
  ('8', 'Solicitudes de información relacionada con decretos', 'OPA 8: Solicitudes de información relacionada con decretos', '000', '001'),
  ('9', 'Para el trámite: "Inscripción de la propiedad horizontal": únicamente asociar las solicitudes que cumplan con los criterios y anexos del formulario " 1. INSCRIPCIÓN Y CERTIFICACIÓN DE PROPIEDAD HORIZONTAL" .', 'OPA 9: Para el trámite: "Inscripción de la propiedad horizontal": únicamente asociar las solicitudes que cumplan con los criterios y anexos del formulario " 1. INSCRIPCIÓN Y CERTIFICACIÓN DE PROPIEDAD HORIZONTAL" .', '000', '001'),
  ('10', 'Para el trámite "Inscripción o cambio del representante legal y/o revisor fiscal de la propiedad horizontal", asociar las solicitudes que cumplan con los criterios y anexos del formulario para los puntos  "2.ACTUALIZACIÓN POR CAMBIO DE REPRESENTACIÓN LEGAL Y/O REVISOR FISCAL; 3.ACTUALIZACIÓN DE FECHA; 4.REGISTRO DE EXTINCIÓN DE PROPIEDAD HORIZONTAL; 5. FOTOCOPIA DE LA CERTIFICACIÓN".', 'OPA 10: Para el trámite "Inscripción o cambio del representante legal y/o revisor fiscal de la propiedad horizontal", asociar las solicitudes que cumplan con los criterios y anexos del formulario para los puntos  "2.ACTUALIZACIÓN POR CAMBIO DE REPRESENTACIÓN LEGAL Y/O REVISOR FISCAL; 3.ACTUALIZACIÓN DE FECHA; 4.REGISTRO DE EXTINCIÓN DE PROPIEDAD HORIZONTAL; 5. FOTOCOPIA DE LA CERTIFICACIÓN".', '000', '001'),
  ('1', 'Informes para la contraloria, procuraduria relacionada con la ejecución de contratos', 'OPA 1: Informes para la contraloria, procuraduria relacionada con la ejecución de contratos', '000', '002'),
  ('2', 'lo que llega despues de la vigencia 2016 en adelante es con la oficina de contratación, las vigencias de años anteriores con archivo general', 'OPA 2: lo que llega despues de la vigencia 2016 en adelante es con la oficina de contratación, las vigencias de años anteriores con archivo general', '000', '002'),
  ('3', 'Solicitud de información o certificados de contratos ejecutados cuando se desconozca el supervisor solo las personas que han laborado con la oficina de contratación. es por cada dependencia.', 'OPA 3: Solicitud de información o certificados de contratos ejecutados cuando se desconozca el supervisor solo las personas que han laborado con la oficina de contratación. es por cada dependencia.', '000', '002'),
  ('1', 'Demandas dirigidas a la Alcaldía Municipal', 'OPA 1: Demandas dirigidas a la Alcaldía Municipal', '000', '003'),
  ('2', 'Procesos judiciales', 'OPA 2: Procesos judiciales', '000', '003'),
  ('3', 'Providencias', 'OPA 3: Providencias', '000', '003'),
  ('4', 'Conciliaciones dirigidas a la Alcaldía Municipal', 'OPA 4: Conciliaciones dirigidas a la Alcaldía Municipal', '000', '003'),
  ('5', 'Negociaciones de deudas por insolvencia economica, con el tipo documental Proceso Judicial', 'OPA 5: Negociaciones de deudas por insolvencia economica, con el tipo documental Proceso Judicial', '000', '003'),
  ('6', 'Accion de repeticion por parte de autoridades', 'OPA 6: Accion de repeticion por parte de autoridades', '000', '003'),
  ('7', 'Tutelas - se radica a la dependencia responsable y a defensa judicial para conocimiento', 'OPA 7: Tutelas - se radica a la dependencia responsable y a defensa judicial para conocimiento', '000', '003'),
  ('8', 'Solicitud de informes de la Agencia Nacional de Defensa Judicial', 'OPA 8: Solicitud de informes de la Agencia Nacional de Defensa Judicial', '000', '003'),
  ('1', 'Informes de auditoria remitidos por contraloria, Contaduria General de la República, SIAOBSERVA, SIACONTRALORIA', 'OPA 1: Informes de auditoria remitidos por contraloria, Contaduria General de la República, SIAOBSERVA, SIACONTRALORIA', '000', '004'),
  ('2', 'Planes de mejoramiento de auditoria  externas', 'OPA 2: Planes de mejoramiento de auditoria  externas', '000', '004'),
  ('3', 'Requerimiento con entidades externas de control (contraloria)', 'OPA 3: Requerimiento con entidades externas de control (contraloria)', '000', '004'),
  ('4', 'Todas las comunicaciones dirigidas por la CONTRALORIA independientemente del tema', 'OPA 4: Todas las comunicaciones dirigidas por la CONTRALORIA independientemente del tema', '000', '004'),
  ('5', 'Plan Anticorrupción y de Atención al Ciudadano', 'OPA 5: Plan Anticorrupción y de Atención al Ciudadano', '000', '004'),
  ('6', 'Indice de transparencia activa- ITA', 'OPA 6: Indice de transparencia activa- ITA', '000', '004'),
  ('1', 'Solicitudes de servicios de internet', 'OPA 1: Solicitudes de servicios de internet', '000', '005'),
  ('2', 'Prestamo de los Puntos Vive Digital', 'OPA 2: Prestamo de los Puntos Vive Digital', '000', '005'),
  ('3', 'Politica de seguridad digital y manejo de la información', 'OPA 3: Politica de seguridad digital y manejo de la información', '000', '005'),
  ('4', 'Convenios y proyectos informáticos de la alcaldía y el municipio', 'OPA 4: Convenios y proyectos informáticos de la alcaldía y el municipio', '000', '005'),
  ('5', 'Sistemas de información', 'OPA 5: Sistemas de información', '000', '005'),
  ('6', 'Conectividad en internet del municipio', 'OPA 6: Conectividad en internet del municipio', '000', '005'),
  ('1', 'Solicitudes relacionadas con la administracion o contenidos de la pagina web y redes sociales de la alcaldia', 'OPA 1: Solicitudes relacionadas con la administracion o contenidos de la pagina web y redes sociales de la alcaldia', '000', '006'),
  ('2', 'Uso de imagen institucional', 'OPA 2: Uso de imagen institucional', '000', '006'),
  ('3', 'Relaciones publicas del alcalde con eventos externos', 'OPA 3: Relaciones publicas del alcalde con eventos externos', '000', '006')
) AS v(code, name, description, dep_code, subdep_code)
JOIN dependencies d ON d.code = v.dep_code
LEFT JOIN subdependencies s ON s.dependency_id = d.id AND s.code = v.subdep_code
WHERE NOT EXISTS (
    SELECT 1 FROM opas o 
    WHERE o.code = v.code 
    AND o.dependency_id = d.id 
    AND (o.subdependency_id = s.id OR (o.subdependency_id IS NULL AND s.id IS NULL))
);

-- Verificar carga
SELECT 
    'Despacho Alcalde' as dependencia,
    COUNT(*) as opas_cargados
FROM opas o
JOIN dependencies d ON o.dependency_id = d.id
WHERE d.code = '000';

COMMIT;
