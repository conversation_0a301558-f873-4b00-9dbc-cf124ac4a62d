-- LOTE 2 DE CARGA DE OPAs
BEGIN;


-- load_opas_040_Secretaria_de_Hacienda.sql (109 OPAs)
INSERT INTO opas (code, name, description, dependency_id, subdependency_id, is_active)
SELECT v.code, v.name, v.description, d.id, s.id, true
FROM (VALUES
  ('1', 'Racionalización de gasto', 'OPA 1: Racionalización de gasto', '040', '040'),
  ('2', 'Financiación de programas de inversión', 'OPA 2: Financiación de programas de inversión', '040', '040'),
  ('3', 'Estado de cuenta deudores', 'OPA 3: Estado de cuenta deudores', '040', '040'),
  ('4', 'Oficios DIAN', 'OPA 4: Oficios DIAN', '040', '040'),
  ('5', 'Oficios Contaduria General', 'OPA 5: Oficios Contaduria General', '040', '040'),
  ('6', 'Solicitud de avaluo catastral', 'OPA 6: Solicitud de avaluo catastral', '040', '040'),
  ('7', 'Remisión de sucesión', 'OPA 7: Remisión de sucesión', '040', '040'),
  ('8', 'Exención., devolución y compensación impuesto predial', 'OPA 8: Exención., devolución y compensación impuesto predial', '040', '040'),
  ('9', 'Proceso cobro coactivo', 'OPA 9: Proceso cobro coactivo', '040', '040'),
  ('10', 'Remision de recibos de pago del impuestro predial que están vinculados a cobros coactivos.', 'OPA 10: Remision de recibos de pago del impuestro predial que están vinculados a cobros coactivos.', '040', '040'),
  ('11', 'Recurso de reconsideración y reposición impuesto predial', 'OPA 11: Recurso de reconsideración y reposición impuesto predial', '040', '040'),
  ('12', 'Acuerdos de pago impuesto predial', 'OPA 12: Acuerdos de pago impuesto predial', '040', '040'),
  ('13', 'Solictud prescripción cartera impuesto predial', 'OPA 13: Solictud prescripción cartera impuesto predial', '040', '040'),
  ('14', 'Peticiones generales internas y externas sobre el impuesto predial', 'OPA 14: Peticiones generales internas y externas sobre el impuesto predial', '040', '040'),
  ('15', 'Reporte Categorías CUIPO y Sistema General de Regalías (SGR) Vigencia 2023 - Plataforma CHIP.', 'OPA 15: Reporte Categorías CUIPO y Sistema General de Regalías (SGR) Vigencia 2023 - Plataforma CHIP.', '040', '040'),
  ('16', 'Boletín de Deudores Morosos del Estado - BDME', 'OPA 16: Boletín de Deudores Morosos del Estado - BDME', '040', '040'),
  ('17', 'Facturas de Impuesto predial, cuando son de Cobro Coactivo o procesos', 'OPA 17: Facturas de Impuesto predial, cuando son de Cobro Coactivo o procesos', '040', '040'),
  ('18', 'Mutacion de inmuebles', 'OPA 18: Mutacion de inmuebles', '040', '040'),
  ('1', 'Solicitud de certificado de libertad (VUR)', 'OPA 1: Solicitud de certificado de libertad (VUR)', '040', '041'),
  ('2', 'Certificado de paz y salvo impuestos hacienda
A partir del mes de agosto de 2023, las notarías podrán acceder a consultar la paz y salvo en línea a través de la ventanilla única de registro VUR. Por lo cual no es necesario acercarse a la Secretaría de hacienda; a excepción de aquellos que tengan anotación de plusvalía en el certificado de tradición y libertad o en caso de que VUR no esté en funcionamiento podrá realizar la solicitud a la Secretaría de Hacienda".  Solicitudes por parte de los ciu', 'OPA 2: Certificado de paz y salvo impuestos hacienda
A partir del mes de agosto de 2023, las notarías podrán acceder a consultar la paz y salvo en línea a través de la ventanilla única de registro VUR. Por lo cual no es necesario acercarse a la Secretaría de hacienda; a excepción de aquellos que tengan anotación de plusvalía en el certificado de tradición y libertad o en caso de que VUR no esté en funcionamiento podrá realizar la solicitud a la Secretaría de Hacienda".  Solicitudes por parte de los ciudadanos de Paz y Salvos y/o valorización enviar "AL CORREO DE RENTAS"   como  "ORIENTACIÓN E INFORMACIÓN"', '040', '041'),
  ('3', 'Exencion de impuestos (Predial e Industria y Comercio)', 'OPA 3: Exencion de impuestos (Predial e Industria y Comercio)', '040', '041'),
  ('4', 'Devolución y/o compensación de pagos en exceso y pagos de lo no debido', 'OPA 4: Devolución y/o compensación de pagos en exceso y pagos de lo no debido', '040', '041'),
  ('5', 'Solicitud de Avaluo o de información catastral por parte de Inspecciones', 'OPA 5: Solicitud de Avaluo o de información catastral por parte de Inspecciones', '040', '041'),
  ('6', 'Sucesiones de Notaria', 'OPA 6: Sucesiones de Notaria', '040', '041'),
  ('7', 'Sobretasa municipal o distrital a la gasolina motor', 'OPA 7: Sobretasa municipal o distrital a la gasolina motor', '040', '041'),
  ('8', 'Corrección de errores e inconsistencias en declaraciones y recibos de pago', 'OPA 8: Corrección de errores e inconsistencias en declaraciones y recibos de pago', '040', '041'),
  ('9', 'Impuesto sobre el servicio de alumbrado público', 'OPA 9: Impuesto sobre el servicio de alumbrado público', '040', '041'),
  ('10', 'Pago voluntario anticipado', 'OPA 10: Pago voluntario anticipado', '040', '041'),
  ('11', 'Afectación de predios con cobro de plusvalía', 'OPA 11: Afectación de predios con cobro de plusvalía', '040', '041'),
  ('12', 'Incentivos por discapacidad', 'OPA 12: Incentivos por discapacidad', '040', '041'),
  ('13', 'Procesos de fiscalización', 'OPA 13: Procesos de fiscalización', '040', '041'),
  ('14', 'Quitar el anticipo del año siguiente', 'OPA 14: Quitar el anticipo del año siguiente', '040', '041'),
  ('15', 'Devoluciones', 'OPA 15: Devoluciones', '040', '041'),
  ('16', 'Exención del impuesto de espectáculos públicos', 'OPA 16: Exención del impuesto de espectáculos públicos', '040', '041'),
  ('17', 'Exención del impuesto de industria y comercio', 'OPA 17: Exención del impuesto de industria y comercio', '040', '041'),
  ('18', 'Impuesto de espectaculos publicos', 'OPA 18: Impuesto de espectaculos publicos', '040', '041'),
  ('19', 'Acuerdos de pago', 'OPA 19: Acuerdos de pago', '040', '041'),
  ('20', 'Facilidades de pago para los deudores de obligaciones', 'OPA 20: Facilidades de pago para los deudores de obligaciones', '040', '041'),
  ('21', 'INFORMACIÓN Y ORIENTACIÓN (CASI TODO ICA Y RETEICA)', 'OPA 21: INFORMACIÓN Y ORIENTACIÓN (CASI TODO ICA Y RETEICA)', '040', '041'),
  ('22', 'Claves de acceso', 'OPA 22: Claves de acceso', '040', '041'),
  ('23', 'Normatividad (vencimiento y calendario tributario)', 'OPA 23: Normatividad (vencimiento y calendario tributario)', '040', '041'),
  ('24', 'Presentación y liquidación de pago', 'OPA 24: Presentación y liquidación de pago', '040', '041'),
  ('25', 'Cambio de representante legal, cambio de contador, cambio de revisor fiscal, cambio de actividades, cambio de dirección, cambio de razón social, cambio de fecha de inicio de actividades, cambio de dígito de verificación', 'OPA 25: Cambio de representante legal, cambio de contador, cambio de revisor fiscal, cambio de actividades, cambio de dirección, cambio de razón social, cambio de fecha de inicio de actividades, cambio de dígito de verificación', '040', '041'),
  ('26', 'Requisitos para registrar un establecimiento', 'OPA 26: Requisitos para registrar un establecimiento', '040', '041'),
  ('27', 'Quitar avisos y tableros', 'OPA 27: Quitar avisos y tableros', '040', '041'),
  ('28', 'Presentar información exogena (Sólo se deben radicar los casos especiales de exógenas, como Derechos de Petición)', 'OPA 28: Presentar información exogena (Sólo se deben radicar los casos especiales de exógenas, como Derechos de Petición)', '040', '041'),
  ('29', 'Paz y Salvo - Estado de cuenta (Industria y comercio)', 'OPA 29: Paz y Salvo - Estado de cuenta (Industria y comercio)', '040', '041'),
  ('30', 'Estado del establecimiento (Activo o Inactivo)', 'OPA 30: Estado del establecimiento (Activo o Inactivo)', '040', '041'),
  ('31', 'Cerrar RIC (Cancelación de establecimiento)', 'OPA 31: Cerrar RIC (Cancelación de establecimiento)', '040', '041'),
  ('32', 'Certificación de registro (Qué datos hay de un establecimiento)', 'OPA 32: Certificación de registro (Qué datos hay de un establecimiento)', '040', '041'),
  ('33', 'Cómo inscribirse', 'OPA 33: Cómo inscribirse', '040', '041'),
  ('34', 'liquidación de impuestos', 'OPA 34: liquidación de impuestos', '040', '041'),
  ('35', 'cobro de impuestos', 'OPA 35: cobro de impuestos', '040', '041'),
  ('36', 'rentas municipales', 'OPA 36: rentas municipales', '040', '041'),
  ('37', 'tarifas tasas', 'OPA 37: tarifas tasas', '040', '041'),
  ('38', 'contribuciones', 'OPA 38: contribuciones', '040', '041'),
  ('39', 'evasión tributaria', 'OPA 39: evasión tributaria', '040', '041'),
  ('40', 'contribuyentes morosos', 'OPA 40: contribuyentes morosos', '040', '041'),
  ('41', 'politica fiscal', 'OPA 41: politica fiscal', '040', '041'),
  ('42', 'calendario tributario', 'OPA 42: calendario tributario', '040', '041'),
  ('43', 'descuentos de impuestos', 'OPA 43: descuentos de impuestos', '040', '041'),
  ('44', 'recibos de pago', 'OPA 44: recibos de pago', '040', '041'),
  ('45', 'Impuesto al degüello de ganado mayor', 'OPA 45: Impuesto al degüello de ganado mayor', '040', '041'),
  ('46', 'Impuesto de industria y comercio y su complementario de avisos y tableros', 'OPA 46: Impuesto de industria y comercio y su complementario de avisos y tableros', '040', '041'),
  ('47', 'Medios magneticos', 'OPA 47: Medios magneticos', '040', '041'),
  ('48', 'Solicitud liquidacion credito FOES', 'OPA 48: Solicitud liquidacion credito FOES', '040', '041'),
  ('49', 'Reorganizacion de sociedades', 'OPA 49: Reorganizacion de sociedades', '040', '041'),
  ('50', 'Recuperar credenciales de acceso', 'OPA 50: Recuperar credenciales de acceso', '040', '041'),
  ('51', 'Orientación e información presentación de exogenas', 'OPA 51: Orientación e información presentación de exogenas', '040', '041'),
  ('52', 'Facturas de Impuesto predial', 'OPA 52: Facturas de Impuesto predial', '040', '041'),
  ('53', 'Paz y salvos de Impuesto predial', 'OPA 53: Paz y salvos de Impuesto predial', '040', '041'),
  ('54', 'Actualización de Registro de Información Tributaria  RIT', 'OPA 54: Actualización de Registro de Información Tributaria  RIT', '040', '041'),
  ('1', 'presupuesto del municipio', 'OPA 1: presupuesto del municipio', '040', '042'),
  ('2', 'distribución de recursos', 'OPA 2: distribución de recursos', '040', '042'),
  ('3', 'apoyar presupuesto entidades descentralizadas', 'OPA 3: apoyar presupuesto entidades descentralizadas', '040', '042'),
  ('4', 'contabilidad del municipio', 'OPA 4: contabilidad del municipio', '040', '042'),
  ('5', 'consolidar contabilidad de las instituciones educativas', 'OPA 5: consolidar contabilidad de las instituciones educativas', '040', '042'),
  ('6', 'consolidar contabilidad de consejo y personería municipal', 'OPA 6: consolidar contabilidad de consejo y personería municipal', '040', '042'),
  ('7', 'estados financieros', 'OPA 7: estados financieros', '040', '042'),
  ('8', 'registro contable de inventarios', 'OPA 8: registro contable de inventarios', '040', '042'),
  ('9', 'reporte de riesgo - contingencias', 'OPA 9: reporte de riesgo - contingencias', '040', '042'),
  ('10', 'endeudamiento', 'OPA 10: endeudamiento', '040', '042'),
  ('11', 'inversiones del municipio', 'OPA 11: inversiones del municipio', '040', '042'),
  ('12', 'programa anualizado mensualizado de caja', 'OPA 12: programa anualizado mensualizado de caja', '040', '042'),
  ('13', 'cuentas por pagar', 'OPA 13: cuentas por pagar', '040', '042'),
  ('14', 'reservas', 'OPA 14: reservas', '040', '042'),
  ('15', 'sistema de información financiera', 'OPA 15: sistema de información financiera', '040', '042'),
  ('16', 'ingresos y gastos', 'OPA 16: ingresos y gastos', '040', '042'),
  ('17', 'marco fiscal de mediano plazo', 'OPA 17: marco fiscal de mediano plazo', '040', '042'),
  ('18', 'disponibilidad presupuestal CDP', 'OPA 18: disponibilidad presupuestal CDP', '040', '042'),
  ('19', 'FONPET', 'OPA 19: FONPET', '040', '042'),
  ('20', 'Formulario Unico Territorial FUT', 'OPA 20: Formulario Unico Territorial FUT', '040', '042'),
  ('21', 'impacto fiscal', 'OPA 21: impacto fiscal', '040', '042'),
  ('22', 'Nuevo!  Solicitud de Certificado de Retenciones de IVA, ICA, ESTAMPILLAS, entre otros,   de Personas Naturales o Juridicas  que haya celebrado contratos con el Municipio', 'OPA 22: Nuevo!  Solicitud de Certificado de Retenciones de IVA, ICA, ESTAMPILLAS, entre otros,   de Personas Naturales o Juridicas  que haya celebrado contratos con el Municipio', '040', '042'),
  ('1', 'recaudo', 'OPA 1: recaudo', '040', '043'),
  ('2', 'pagos', 'OPA 2: pagos', '040', '043'),
  ('3', 'cuentas bancarias', 'OPA 3: cuentas bancarias', '040', '043'),
  ('4', 'cheques', 'OPA 4: cheques', '040', '043'),
  ('5', 'giros', 'OPA 5: giros', '040', '043'),
  ('6', 'descuentos tributarios', 'OPA 6: descuentos tributarios', '040', '043'),
  ('7', 'leasing financiero', 'OPA 7: leasing financiero', '040', '043'),
  ('8', 'cobro coactivo', 'OPA 8: cobro coactivo', '040', '043'),
  ('9', 'cobro persuasivo', 'OPA 9: cobro persuasivo', '040', '043'),
  ('10', 'pago nómina de funcionarios', 'OPA 10: pago nómina de funcionarios', '040', '043'),
  ('11', 'descuentos a contratistas, Certificados de retencion a contratistas', 'OPA 11: descuentos a contratistas, Certificados de retencion a contratistas', '040', '043'),
  ('12', 'Conciliación de operaciones recíprocas', 'OPA 12: Conciliación de operaciones recíprocas', '040', '043'),
  ('13', 'FONSET', 'OPA 13: FONSET', '040', '043'),
  ('14', 'demora en pagos', 'OPA 14: demora en pagos', '040', '043'),
  ('15', 'Procesos de Embargo de creditos entre terceros', 'OPA 15: Procesos de Embargo de creditos entre terceros', '040', '043')
) AS v(code, name, description, dep_code, subdep_code)
JOIN dependencies d ON d.code = v.dep_code
LEFT JOIN subdependencies s ON s.dependency_id = d.id AND s.code = v.subdep_code
WHERE NOT EXISTS (
    SELECT 1 FROM opas o 
    WHERE o.code = v.code 
    AND o.dependency_id = d.id 
    AND (o.subdependency_id = s.id OR (o.subdependency_id IS NULL AND s.id IS NULL))
);


-- load_opas_050_Secretaria_de_Obras_Publicas.sql (39 OPAs)
INSERT INTO opas (code, name, description, dependency_id, subdependency_id, is_active)
SELECT v.code, v.name, v.description, d.id, s.id, true
FROM (VALUES
  ('1', 'Banco de materiales', 'OPA 1: Banco de materiales', '050', '050'),
  ('2', 'demolición de muros', 'OPA 2: demolición de muros', '050', '050'),
  ('3', 'señalización y aislamiento de obras', 'OPA 3: señalización y aislamiento de obras', '050', '050'),
  ('4', 'Poda de Parques y manzanas institucionales', 'OPA 4: Poda de Parques y manzanas institucionales', '050', '050'),
  ('5', 'materialización de sanción demolición', 'OPA 5: materialización de sanción demolición', '050', '050'),
  ('6', 'Indicador de Producción de Obras Civiles IPOC del DANE', 'OPA 6: Indicador de Producción de Obras Civiles IPOC del DANE', '050', '050'),
  ('1', 'Infrastructura vial y obras generales del municipio.', 'OPA 1: Infrastructura vial y obras generales del municipio.', '050', '051'),
  ('2', 'Coordinación y gestión de redes de servicios públicos', 'OPA 2: Coordinación y gestión de redes de servicios públicos', '050', '051'),
  ('3', 'Red vial del municipio.', 'OPA 3: Red vial del municipio.', '050', '051'),
  ('4', 'Mantenimiento de edificios públicos', 'OPA 4: Mantenimiento de edificios públicos', '050', '051'),
  ('5', 'Adecuación de edificios publicos', 'OPA 5: Adecuación de edificios publicos', '050', '051'),
  ('6', 'Supervisión de obras de infrastructura', 'OPA 6: Supervisión de obras de infrastructura', '050', '051'),
  ('7', 'interventorias de obras de infrastructura', 'OPA 7: interventorias de obras de infrastructura', '050', '051'),
  ('8', 'Metodologías del sistema de concesión de obras o servicios', 'OPA 8: Metodologías del sistema de concesión de obras o servicios', '050', '051'),
  ('9', 'Infraestructura comunitaria y asociativa', 'OPA 9: Infraestructura comunitaria y asociativa', '050', '051'),
  ('10', 'Materiales usados en proyectos', 'OPA 10: Materiales usados en proyectos', '050', '051'),
  ('11', 'Informes de estado de las obras', 'OPA 11: Informes de estado de las obras', '050', '051'),
  ('12', 'obras inconclusas', 'OPA 12: obras inconclusas', '050', '051'),
  ('13', 'Administración de maquinaria, equipo y herramienta menor del municipio', 'OPA 13: Administración de maquinaria, equipo y herramienta menor del municipio', '050', '051'),
  ('14', 'Adquisición de repuestos y/o herramienta menor para la maQuinaria del municipio', 'OPA 14: Adquisición de repuestos y/o herramienta menor para la maQuinaria del municipio', '050', '051'),
  ('15', 'Novedades y mantenimiento preventivo de maquinaria', 'OPA 15: Novedades y mantenimiento preventivo de maquinaria', '050', '051'),
  ('16', 'Suministro y consumos para maquinaria', 'OPA 16: Suministro y consumos para maquinaria', '050', '051'),
  ('17', 'Andenes', 'OPA 17: Andenes', '050', '051'),
  ('18', 'mantenimiento de estructura puentes', 'OPA 18: mantenimiento de estructura puentes', '050', '051'),
  ('19', 'visitas', 'OPA 19: visitas', '050', '051'),
  ('20', 'Intervención vía', 'OPA 20: Intervención vía', '050', '051'),
  ('21', 'ajuste de fachadas', 'OPA 21: ajuste de fachadas', '050', '051'),
  ('22', 'reparaciones locativas(lamparas, baños, infiltraciones)', 'OPA 22: reparaciones locativas(lamparas, baños, infiltraciones)', '050', '051'),
  ('23', 'Informe sobre estado de bienes de interes cultural', 'OPA 23: Informe sobre estado de bienes de interes cultural', '050', '051'),
  ('1', 'Etapa precontractual y ejecución de contratos de la dependencia', 'OPA 1: Etapa precontractual y ejecución de contratos de la dependencia', '050', '052'),
  ('2', 'Estudios técnicos diseño y estructura de pavimientos', 'OPA 2: Estudios técnicos diseño y estructura de pavimientos', '050', '052'),
  ('3', 'Estudios de suelos y geotécnicos', 'OPA 3: Estudios de suelos y geotécnicos', '050', '052'),
  ('4', 'Diseño red vial del municipio', 'OPA 4: Diseño red vial del municipio', '050', '052'),
  ('5', 'Estudios y diseños de las obras', 'OPA 5: Estudios y diseños de las obras', '050', '052'),
  ('6', 'Documentación obras', 'OPA 6: Documentación obras', '050', '052'),
  ('7', 'Estudios proyecto de integracion regional capital., Bogotá - Cundinamarca', 'OPA 7: Estudios proyecto de integracion regional capital., Bogotá - Cundinamarca', '050', '052'),
  ('1', 'Sistema de valorización', 'OPA 1: Sistema de valorización', '050', '053'),
  ('2', 'Liquidación de valorización', 'OPA 2: Liquidación de valorización', '050', '053'),
  ('3', 'Atención a contribuyentes en temas de valorización', 'OPA 3: Atención a contribuyentes en temas de valorización', '050', '053')
) AS v(code, name, description, dep_code, subdep_code)
JOIN dependencies d ON d.code = v.dep_code
LEFT JOIN subdependencies s ON s.dependency_id = d.id AND s.code = v.subdep_code
WHERE NOT EXISTS (
    SELECT 1 FROM opas o 
    WHERE o.code = v.code 
    AND o.dependency_id = d.id 
    AND (o.subdependency_id = s.id OR (o.subdependency_id IS NULL AND s.id IS NULL))
);


-- load_opas_060_Secretaria_de_Desarrollo_Social.sql (57 OPAs)
INSERT INTO opas (code, name, description, dependency_id, subdependency_id, is_active)
SELECT v.code, v.name, v.description, d.id, s.id, true
FROM (VALUES
  ('1', 'Consejo municipal de politica fiscal', 'OPA 1: Consejo municipal de politica fiscal', '060', '060'),
  ('2', 'Lucha contra la pobreza extrema', 'OPA 2: Lucha contra la pobreza extrema', '060', '060'),
  ('3', 'Temas realacionados con voluntariados', 'OPA 3: Temas realacionados con voluntariados', '060', '060'),
  ('4', 'Grupos vulnerables', 'OPA 4: Grupos vulnerables', '060', '060'),
  ('5', 'Préstamo de la Biblioteca HOQABIGA', 'OPA 5: Préstamo de la Biblioteca HOQABIGA', '060', '060'),
  ('6', 'Comunicaciones dirigidas a la Gestora Social', 'OPA 6: Comunicaciones dirigidas a la Gestora Social', '060', '060'),
  ('1', 'solicitud de espacios para procesos juveniles', 'OPA 1: solicitud de espacios para procesos juveniles', '060', '061'),
  ('2', 'Sistema nacional de información sobre la juventud', 'OPA 2: Sistema nacional de información sobre la juventud', '060', '061'),
  ('3', 'Bienestar integral de los y las jóvenes', 'OPA 3: Bienestar integral de los y las jóvenes', '060', '061'),
  ('4', 'Derechos de las juventudes', 'OPA 4: Derechos de las juventudes', '060', '061'),
  ('5', 'Plataforma municipal de juventudes', 'OPA 5: Plataforma municipal de juventudes', '060', '061'),
  ('6', 'Gobierno municipal estudiantil', 'OPA 6: Gobierno municipal estudiantil', '060', '061'),
  ('7', 'Consejo municipal de juventud', 'OPA 7: Consejo municipal de juventud', '060', '061'),
  ('8', 'Jovenes en accion exclusivo de esta dirección', 'OPA 8: Jovenes en accion exclusivo de esta dirección', '060', '061'),
  ('1', 'Acciones de responsabilidad social empresarial, academica y gubernamental', 'OPA 1: Acciones de responsabilidad social empresarial, academica y gubernamental', '060', '062'),
  ('2', 'programas y proyectos mujer y género', 'OPA 2: programas y proyectos mujer y género', '060', '062'),
  ('3', 'concejo consultivo de la mujer', 'OPA 3: concejo consultivo de la mujer', '060', '062'),
  ('4', 'Programas y proyectos infancia', 'OPA 4: Programas y proyectos infancia', '060', '062'),
  ('5', 'Programas y proyectos adolescencia', 'OPA 5: Programas y proyectos adolescencia', '060', '062'),
  ('6', 'Programas y proyectos familia', 'OPA 6: Programas y proyectos familia', '060', '062'),
  ('7', 'Formación de familias', 'OPA 7: Formación de familias', '060', '062'),
  ('8', 'Pautas de crianza', 'OPA 8: Pautas de crianza', '060', '062'),
  ('9', 'Programas y proyectos adulto mayor', 'OPA 9: Programas y proyectos adulto mayor', '060', '062'),
  ('10', 'Uso del tiempo libre adulto mayor', 'OPA 10: Uso del tiempo libre adulto mayor', '060', '062'),
  ('11', 'Adulto mayor en estado de abandono', 'OPA 11: Adulto mayor en estado de abandono', '060', '062'),
  ('12', 'Beneficencia', 'OPA 12: Beneficencia', '060', '062'),
  ('13', 'Colombia mayor', 'OPA 13: Colombia mayor', '060', '062'),
  ('14', 'Familias en acción', 'OPA 14: Familias en acción', '060', '062'),
  ('15', 'Programas y proyectos población con discapacidad, población indigena, etnias', 'OPA 15: Programas y proyectos población con discapacidad, población indigena, etnias', '060', '062'),
  ('16', 'actualizacion de sistemas de información de la población frente al desarrollo humano y social', 'OPA 16: actualizacion de sistemas de información de la población frente al desarrollo humano y social', '060', '062'),
  ('17', 'Politícas sociales de envejecimiento, seguridad alimentaria y nutricional', 'OPA 17: Politícas sociales de envejecimiento, seguridad alimentaria y nutricional', '060', '062'),
  ('18', 'Jardines sociales', 'OPA 18: Jardines sociales', '060', '062'),
  ('19', 'Centro de Desarrollo Infantil', 'OPA 19: Centro de Desarrollo Infantil', '060', '062'),
  ('20', 'Madres Gestantes', 'OPA 20: Madres Gestantes', '060', '062'),
  ('21', 'Desnutrición de niños', 'OPA 21: Desnutrición de niños', '060', '062'),
  ('22', 'Ingreso solidario', 'OPA 22: Ingreso solidario', '060', '062'),
  ('23', 'Vigilancia y control Hogar Geriatrico San Rafael', 'OPA 23: Vigilancia y control Hogar Geriatrico San Rafael', '060', '062'),
  ('24', 'INFORMACIÓN SOBRE RENTA CIUDADANA', 'OPA 24: INFORMACIÓN SOBRE RENTA CIUDADANA', '060', '062'),
  ('25', 'Certificado de habitante de calle', 'OPA 25: Certificado de habitante de calle', '060', '062'),
  ('26', 'Abandono adulto mayor y vulnerabilidad(Derecho de petición)', 'OPA 26: Abandono adulto mayor y vulnerabilidad(Derecho de petición)', '060', '062'),
  ('1', 'Desarrollo cultural , acceso a los bienes y servicios de cultura', 'OPA 1: Desarrollo cultural , acceso a los bienes y servicios de cultura', '060', '063'),
  ('2', 'prestamo de trajes- presentaciones artisticas', 'OPA 2: prestamo de trajes- presentaciones artisticas', '060', '063'),
  ('3', 'Sistema de información Cultural', 'OPA 3: Sistema de información Cultural', '060', '063'),
  ('4', 'incentivo de la libre creación cultural', 'OPA 4: incentivo de la libre creación cultural', '060', '063'),
  ('5', 'Declaratoria y manejo de bienes de interés cultural', 'OPA 5: Declaratoria y manejo de bienes de interés cultural', '060', '063'),
  ('6', 'Asesoría cultural a instituciones del municipio', 'OPA 6: Asesoría cultural a instituciones del municipio', '060', '063'),
  ('7', 'Escuela de técnica de artes del Municipio', 'OPA 7: Escuela de técnica de artes del Municipio', '060', '063'),
  ('8', 'Biblioteca y su red', 'OPA 8: Biblioteca y su red', '060', '063'),
  ('9', 'Consejos de cultura y sistema nacional de cultura', 'OPA 9: Consejos de cultura y sistema nacional de cultura', '060', '063'),
  ('10', 'Estimulos y premios para el reconocimiento de artistas y autores.', 'OPA 10: Estimulos y premios para el reconocimiento de artistas y autores.', '060', '063'),
  ('11', 'Registro de bienes de interes cultural', 'OPA 11: Registro de bienes de interes cultural', '060', '063'),
  ('12', 'Protección y conservación del patrimonio cultural', 'OPA 12: Protección y conservación del patrimonio cultural', '060', '063'),
  ('13', 'oferta artistica', 'OPA 13: oferta artistica', '060', '063'),
  ('14', 'eventos artisticos', 'OPA 14: eventos artisticos', '060', '063'),
  ('15', 'prestamo de tarima', 'OPA 15: prestamo de tarima', '060', '063'),
  ('16', 'Permisos  para pintura de murales', 'OPA 16: Permisos  para pintura de murales', '060', '063'),
  ('17', 'Madres Gestantes', 'OPA 17: Madres Gestantes', '060', '063')
) AS v(code, name, description, dep_code, subdep_code)
JOIN dependencies d ON d.code = v.dep_code
LEFT JOIN subdependencies s ON s.dependency_id = d.id AND s.code = v.subdep_code
WHERE NOT EXISTS (
    SELECT 1 FROM opas o 
    WHERE o.code = v.code 
    AND o.dependency_id = d.id 
    AND (o.subdependency_id = s.id OR (o.subdependency_id IS NULL AND s.id IS NULL))
);


-- Verificar carga del lote 2
SELECT 
    'LOTE 2 COMPLETADO' as estado,
    COUNT(*) as total_opas_en_lote
FROM opas o
JOIN dependencies d ON o.dependency_id = d.id
WHERE d.code IN ('040', '050', '060');

COMMIT;
