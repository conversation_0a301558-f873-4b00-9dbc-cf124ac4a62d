-- CARGA OPAs PARA Descentralizados (14 OPAs)
BEGIN;

INSERT INTO opas (code, name, description, dependency_id, subdependency_id, is_active)
SELECT v.code, v.name, v.description, d.id, s.id, true
FROM (VALUES
  ('1', 'Orden de Archivo NUC, Archivo de una denuncia enviado por la fiscalia', 'OPA 1: Orden de Archivo NUC, Archivo de una denuncia enviado por la fiscalia', '200', '204'),
  ('1', 'Confirmar a que PTAR corresponde la conexion de acueducto y/o alcantarillado de un predio', 'OPA 1: Confirmar a que PTAR corresponde la conexion de acueducto y/o alcantarillado de un predio', '200', '203'),
  ('2', 'Viabilidad de servicios públicos(acueducto y alcantarillado)', 'OPA 2: Viabilidad de servicios públicos(acueducto y alcantarillado)', '200', '203'),
  ('3', 'Poda de Césped Área Urbana', 'OPA 3: Poda de Césped Área Urbana', '200', '203'),
  ('4', 'Acuerdos de pago, instalación y todo lo concerniente a los medidores de agua', 'OPA 4: Acuerdos de pago, instalación y todo lo concerniente a los medidores de agua', '200', '203'),
  ('1', 'Actualizacion del predio y/o rectificacion catastral  bien sea nombre del propietario, area o linderos', 'OPA 1: Actualizacion del predio y/o rectificacion catastral  bien sea nombre del propietario, area o linderos', '200', '212'),
  ('1', 'Sesiones urbanísticas en suelo rural y suburbano en el municipio de Chía.', 'OPA 1: Sesiones urbanísticas en suelo rural y suburbano en el municipio de Chía.', '200', '201'),
  ('2', 'Desistimiento de compra proyectos de vivienda VIS(PROYECTOS DE LA ADMINISTRACIÓN)', 'OPA 2: Desistimiento de compra proyectos de vivienda VIS(PROYECTOS DE LA ADMINISTRACIÓN)', '200', '201'),
  ('3', 'Préstamos auditorio ZEA MAYS, Casona Santa Rita y Terminal de Transporte', 'OPA 3: Préstamos auditorio ZEA MAYS, Casona Santa Rita y Terminal de Transporte', '200', '201'),
  ('1', 'Mantenimiento, administracion peticion de prestamo de los parques: 
Unidad deportiva parque paraiso 
Unidad deportiva Bojaca
Unidad deportiva Rio Frio 
Unidad deportiva La Lorena
Unidad deportiva Parque El Cedro
Unidad deportiva Samaria 
Unidad deportiva el Campincito 
Unidad deportiva coliseo de la Luna- sede administrativa canchas de tenis zona urbana
Unidad deportiva concha acustica 
Unidad deportiva el eden 
Unidad deportiva La Balsa
Unidad deportiva Nispero
Unidad deportiva La Fagua
Unidad ', 'OPA 1: Mantenimiento, administracion peticion de prestamo de los parques: 
Unidad deportiva parque paraiso 
Unidad deportiva Bojaca
Unidad deportiva Rio Frio 
Unidad deportiva La Lorena
Unidad deportiva Parque El Cedro
Unidad deportiva Samaria 
Unidad deportiva el Campincito 
Unidad deportiva coliseo de la Luna- sede administrativa canchas de tenis zona urbana
Unidad deportiva concha acustica 
Unidad deportiva el eden 
Unidad deportiva La Balsa
Unidad deportiva Nispero
Unidad deportiva La Fagua
Unidad deportiva La Libertad
Unidad deportiva Lagos de Chia
Campo de Golf
ESCENARIO CULTURAL JUMBO', '200', '202'),
  ('2', 'Escuelas de formacion deportiva', 'OPA 2: Escuelas de formacion deportiva', '200', '202'),
  ('3', 'Deporte social comunitario: Servicio social, juegos comunales', 'OPA 3: Deporte social comunitario: Servicio social, juegos comunales', '200', '202'),
  ('1', 'Retiro de panal de abejas', 'OPA 1: Retiro de panal de abejas', '200', '215'),
  ('1', 'Emite concepto de Plan de Emergencia y Contingencia para eventos masivos y no masivos                                                                                                             correo <EMAIL>', 'OPA 1: Emite concepto de Plan de Emergencia y Contingencia para eventos masivos y no masivos                                                                                                             correo <EMAIL>', '200', '214')
) AS v(code, name, description, dep_code, subdep_code)
JOIN dependencies d ON d.code = v.dep_code
LEFT JOIN subdependencies s ON s.dependency_id = d.id AND s.code = v.subdep_code
WHERE NOT EXISTS (
    SELECT 1 FROM opas o 
    WHERE o.code = v.code 
    AND o.dependency_id = d.id 
    AND (o.subdependency_id = s.id OR (o.subdependency_id IS NULL AND s.id IS NULL))
);

-- Verificar carga
SELECT 
    'Descentralizados' as dependencia,
    COUNT(*) as opas_cargados
FROM opas o
JOIN dependencies d ON o.dependency_id = d.id
WHERE d.code = '200';

COMMIT;
