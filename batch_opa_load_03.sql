-- LOTE 3 DE CARGA DE OPAs
BEGIN;


-- load_opas_070_Secretaria_de_Educacion.sql (69 OPAs)
INSERT INTO opas (code, name, description, dependency_id, subdependency_id, is_active)
SELECT v.code, v.name, v.description, d.id, s.id, true
FROM (VALUES
  ('1', 'Se creó el tipo documental "COMUNICACIONES PARA SEC. EDUCACIÓN", el cual se debe seleccionar cuando lleguen derechos de petición o cualquier comunicación excepto tutelas para la Secretaría de Educación.', 'OPA 1: Se creó el tipo documental "COMUNICACIONES PARA SEC. EDUCACIÓN", el cual se debe seleccionar cuando lleguen derechos de petición o cualquier comunicación excepto tutelas para la Secretaría de Educación.', '070', '070'),
  ('1', 'Inspección, vigilancia y control instituciones educativas publicas', 'OPA 1: Inspección, vigilancia y control instituciones educativas publicas', '070', '071'),
  ('2', 'Inspección, vigilancia y control instituciones educativas privadas', 'OPA 2: Inspección, vigilancia y control instituciones educativas privadas', '070', '071'),
  ('3', 'Inspección, vigilancia y control entidades sin animo de lucro con fines educativos', 'OPA 3: Inspección, vigilancia y control entidades sin animo de lucro con fines educativos', '070', '071'),
  ('4', 'Inspección, vigilancia y control asociación de padres de familia', 'OPA 4: Inspección, vigilancia y control asociación de padres de familia', '070', '071'),
  ('5', 'Personerias juridicas instituciones educativas', 'OPA 5: Personerias juridicas instituciones educativas', '070', '071'),
  ('6', 'Investigaciones a establecimientos de educación formal', 'OPA 6: Investigaciones a establecimientos de educación formal', '070', '071'),
  ('7', 'Investigaciones instituciones de formación para el trabajo y desarrollo humano', 'OPA 7: Investigaciones instituciones de formación para el trabajo y desarrollo humano', '070', '071'),
  ('8', 'Investigaciones a entidades sin animo de lucro con fines educativos', 'OPA 8: Investigaciones a entidades sin animo de lucro con fines educativos', '070', '071'),
  ('9', 'Investigaciones a asociación de padres de familia', 'OPA 9: Investigaciones a asociación de padres de familia', '070', '071'),
  ('10', 'Costos educativos', 'OPA 10: Costos educativos', '070', '071'),
  ('11', 'Sanciones instituciones educativas', 'OPA 11: Sanciones instituciones educativas', '070', '071'),
  ('12', 'Actualización proyectos educativos institucionales', 'OPA 12: Actualización proyectos educativos institucionales', '070', '071'),
  ('13', 'Normas de convivencia escolar', 'OPA 13: Normas de convivencia escolar', '070', '071'),
  ('14', 'Seguimiento jornada escolar', 'OPA 14: Seguimiento jornada escolar', '070', '071'),
  ('15', 'Seguimiento jornada laboral establecimientos educativos oficiales', 'OPA 15: Seguimiento jornada laboral establecimientos educativos oficiales', '070', '071'),
  ('16', 'Sistemas de infomarmación sobre instituciones educativas', 'OPA 16: Sistemas de infomarmación sobre instituciones educativas', '070', '071'),
  ('17', 'Cierre de establecimientos educativos', 'OPA 17: Cierre de establecimientos educativos', '070', '071'),
  ('1', 'Procesos de aprendizaje', 'OPA 1: Procesos de aprendizaje', '070', '072'),
  ('2', 'Fortalecimiento de la educación', 'OPA 2: Fortalecimiento de la educación', '070', '072'),
  ('3', 'Procesos evaluativos', 'OPA 3: Procesos evaluativos', '070', '072'),
  ('4', 'Estrategias y metodologias', 'OPA 4: Estrategias y metodologias', '070', '072'),
  ('5', 'Programas de formación docente', 'OPA 5: Programas de formación docente', '070', '072'),
  ('6', 'Actualización docente', 'OPA 6: Actualización docente', '070', '072'),
  ('7', 'Integración de población campesina al sistema educativo', 'OPA 7: Integración de población campesina al sistema educativo', '070', '072'),
  ('8', 'Integración de población con talentos excepcionales al sistema educativo', 'OPA 8: Integración de población con talentos excepcionales al sistema educativo', '070', '072'),
  ('9', 'Integración de adultos al sistema educativo', 'OPA 9: Integración de adultos al sistema educativo', '070', '072'),
  ('10', 'Integración de población rural al sistema educativo', 'OPA 10: Integración de población rural al sistema educativo', '070', '072'),
  ('11', 'Integración de población con limitaciones al sistema educativo', 'OPA 11: Integración de población con limitaciones al sistema educativo', '070', '072'),
  ('12', 'Modelos de educación técnica', 'OPA 12: Modelos de educación técnica', '070', '072'),
  ('13', 'Modelos de educación tecnológica', 'OPA 13: Modelos de educación tecnológica', '070', '072'),
  ('14', 'Articulación con educación media', 'OPA 14: Articulación con educación media', '070', '072'),
  ('15', 'Articulación con educación superior', 'OPA 15: Articulación con educación superior', '070', '072'),
  ('16', 'Planes de mejoramiento de las instituciones educativas', 'OPA 16: Planes de mejoramiento de las instituciones educativas', '070', '072'),
  ('17', 'Calidad de la educación', 'OPA 17: Calidad de la educación', '070', '072'),
  ('18', 'Estrategias de acceso y permanencia', 'OPA 18: Estrategias de acceso y permanencia', '070', '072'),
  ('19', 'Creditos educación superior (FOES)', 'OPA 19: Creditos educación superior (FOES)', '070', '072'),
  ('20', 'Plan educativo institucional', 'OPA 20: Plan educativo institucional', '070', '072'),
  ('21', 'Programa de alimentación escolar  (PAE)', 'OPA 21: Programa de alimentación escolar  (PAE)', '070', '072'),
  ('22', 'Mantenimiento instituciones educativas oficiales', 'OPA 22: Mantenimiento instituciones educativas oficiales', '070', '072'),
  ('23', 'Trasporte escolar', 'OPA 23: Trasporte escolar', '070', '072'),
  ('24', 'Dotación escolar', 'OPA 24: Dotación escolar', '070', '072'),
  ('25', 'El sistema integrado de matrícula SIMAT', 'OPA 25: El sistema integrado de matrícula SIMAT', '070', '072'),
  ('1', 'Estados financieros instituciones educativas oficiales', 'OPA 1: Estados financieros instituciones educativas oficiales', '070', '073'),
  ('2', 'Atención al ciudadano de la secretaría de educación', 'OPA 2: Atención al ciudadano de la secretaría de educación', '070', '073'),
  ('3', 'Archivo de la secretaría de educación', 'OPA 3: Archivo de la secretaría de educación', '070', '073'),
  ('4', 'Reclutamiento docentes', 'OPA 4: Reclutamiento docentes', '070', '073'),
  ('5', 'Concursos docentes', 'OPA 5: Concursos docentes', '070', '073'),
  ('6', 'Selección docentes', 'OPA 6: Selección docentes', '070', '073'),
  ('7', 'Inducción docentes', 'OPA 7: Inducción docentes', '070', '073'),
  ('8', 'Formación docentes', 'OPA 8: Formación docentes', '070', '073'),
  ('9', 'Evaluación de docentes', 'OPA 9: Evaluación de docentes', '070', '073'),
  ('10', 'Salud ocupacional docentes', 'OPA 10: Salud ocupacional docentes', '070', '073'),
  ('11', 'Bienestar docentes', 'OPA 11: Bienestar docentes', '070', '073'),
  ('12', 'Capacitación docentes', 'OPA 12: Capacitación docentes', '070', '073'),
  ('13', 'Reinducción docentes', 'OPA 13: Reinducción docentes', '070', '073'),
  ('14', 'Escalafon docentes', 'OPA 14: Escalafon docentes', '070', '073'),
  ('15', 'Carrera administrativa docentes', 'OPA 15: Carrera administrativa docentes', '070', '073'),
  ('16', 'Información de personal docente', 'OPA 16: Información de personal docente', '070', '073'),
  ('17', 'Historias laborales docentes', 'OPA 17: Historias laborales docentes', '070', '073'),
  ('18', 'Certificación de personal docentes', 'OPA 18: Certificación de personal docentes', '070', '073'),
  ('19', 'Planta docente', 'OPA 19: Planta docente', '070', '073'),
  ('20', 'Novedades nómina', 'OPA 20: Novedades nómina', '070', '073'),
  ('21', 'Cesantias docentes', 'OPA 21: Cesantias docentes', '070', '073'),
  ('22', 'Embargos docentes', 'OPA 22: Embargos docentes', '070', '073'),
  ('23', 'Libranzas docentes', 'OPA 23: Libranzas docentes', '070', '073'),
  ('24', 'Liquidación nómina docente y administrativa de las instituciones educativas oficiales', 'OPA 24: Liquidación nómina docente y administrativa de las instituciones educativas oficiales', '070', '073'),
  ('25', 'Prestaciones sociales docentes', 'OPA 25: Prestaciones sociales docentes', '070', '073'),
  ('26', 'Sindicato docentes', 'OPA 26: Sindicato docentes', '070', '073')
) AS v(code, name, description, dep_code, subdep_code)
JOIN dependencies d ON d.code = v.dep_code
LEFT JOIN subdependencies s ON s.dependency_id = d.id AND s.code = v.subdep_code
WHERE NOT EXISTS (
    SELECT 1 FROM opas o 
    WHERE o.code = v.code 
    AND o.dependency_id = d.id 
    AND (o.subdependency_id = s.id OR (o.subdependency_id IS NULL AND s.id IS NULL))
);


-- load_opas_080_Secretaria_de_Salud.sql (38 OPAs)
INSERT INTO opas (code, name, description, dependency_id, subdependency_id, is_active)
SELECT v.code, v.name, v.description, d.id, s.id, true
FROM (VALUES
  ('1', 'Contribución solidaria- SISBEN', 'OPA 1: Contribución solidaria- SISBEN', '080', '080'),
  ('2', 'Aseguramiento, afilicaciones regimen contributivo y subsidiado', 'OPA 2: Aseguramiento, afilicaciones regimen contributivo y subsidiado', '080', '080'),
  ('3', 'Sistema General de Seguridad Social en Salud. Afiliacion a EPS, Sistema de afiliacion transaccional - SAT', 'OPA 3: Sistema General de Seguridad Social en Salud. Afiliacion a EPS, Sistema de afiliacion transaccional - SAT', '080', '080'),
  ('4', 'Abandono adulto mayor y vulnerabilidad(Derecho de petición)', 'OPA 4: Abandono adulto mayor y vulnerabilidad(Derecho de petición)', '080', '080'),
  ('5', 'Licencia de exhumación, inhumación, cremacion y traslado de cadáveres', 'OPA 5: Licencia de exhumación, inhumación, cremacion y traslado de cadáveres', '080', '080'),
  ('6', 'Vigilancia a hogares geriatricos(excepción Hogar San Rafael que le correponde a Accion social)', 'OPA 6: Vigilancia a hogares geriatricos(excepción Hogar San Rafael que le correponde a Accion social)', '080', '080'),
  ('7', 'PQRS sistema de seguridad social en salud (Los reclamos o quejas por la mala prestación de servicios de salud debe radicarse como derecho de petición y no como reclamo).', 'OPA 7: PQRS sistema de seguridad social en salud (Los reclamos o quejas por la mala prestación de servicios de salud debe radicarse como derecho de petición y no como reclamo).', '080', '080'),
  ('1', 'Novedades y vigilancia epídemiologicas, infecciones', 'OPA 1: Novedades y vigilancia epídemiologicas, infecciones', '080', '081'),
  ('2', 'Vacunacion a humanos. PAI  Plan Ampliado de Vacunación', 'OPA 2: Vacunacion a humanos. PAI  Plan Ampliado de Vacunación', '080', '081'),
  ('3', 'Comité de estadisticas vitales', 'OPA 3: Comité de estadisticas vitales', '080', '081'),
  ('4', 'Registro Único de Afiliados  RUAF, MANGO, SIVIGILA, RIPS', 'OPA 4: Registro Único de Afiliados  RUAF, MANGO, SIVIGILA, RIPS', '080', '081'),
  ('5', 'Visita a IPS municipales. Riesgos asociados a IPS y EPS en el municipio por capacidad instalada y panorama de riesgo.', 'OPA 5: Visita a IPS municipales. Riesgos asociados a IPS y EPS en el municipio por capacidad instalada y panorama de riesgo.', '080', '081'),
  ('6', 'Analisis de situacion en salud', 'OPA 6: Analisis de situacion en salud', '080', '081'),
  ('7', 'Politicas de salud pública (Hábitos saludables, poblaciones vulnerables, enfermedades transmisibles, enfermedades no transmisibles, salud sexual y reproductiva; salud mental, salud ambiental, salud nutricional, vacunación)', 'OPA 7: Politicas de salud pública (Hábitos saludables, poblaciones vulnerables, enfermedades transmisibles, enfermedades no transmisibles, salud sexual y reproductiva; salud mental, salud ambiental, salud nutricional, vacunación)', '080', '081'),
  ('8', 'Plan Decenal de Salud Pública.', 'OPA 8: Plan Decenal de Salud Pública.', '080', '081'),
  ('9', 'Plan de Intervenciones Colectivas PIC', 'OPA 9: Plan de Intervenciones Colectivas PIC', '080', '081'),
  ('10', 'Prevención y promoción  gestantes.', 'OPA 10: Prevención y promoción  gestantes.', '080', '081'),
  ('11', 'AIEPI  Atención en enfermedades prevalentes en la infancia.', 'OPA 11: AIEPI  Atención en enfermedades prevalentes en la infancia.', '080', '081'),
  ('12', 'Enfermedades huerfanas', 'OPA 12: Enfermedades huerfanas', '080', '081'),
  ('13', 'Rutas integrales de atencion en salud', 'OPA 13: Rutas integrales de atencion en salud', '080', '081'),
  ('14', 'REPS- Registro Especial de Prestadores de Servicios de Salud', 'OPA 14: REPS- Registro Especial de Prestadores de Servicios de Salud', '080', '081'),
  ('15', 'Certificado de discapacidad', 'OPA 15: Certificado de discapacidad', '080', '081'),
  ('1', 'Concepto sanitario, Alimentos sanos y seguros, seguridad quimica, esfermedades trasmitidas por alimentos ETAS', 'OPA 1: Concepto sanitario, Alimentos sanos y seguros, seguridad quimica, esfermedades trasmitidas por alimentos ETAS', '080', '082'),
  ('2', 'Eventos transmisibles de origen zoonótico, enfermedades transmitidas por animales', 'OPA 2: Eventos transmisibles de origen zoonótico, enfermedades transmitidas por animales', '080', '082'),
  ('3', 'Protocolos de bioseguridad', 'OPA 3: Protocolos de bioseguridad', '080', '082'),
  ('4', 'Verificación de estándares de habilitación de los establecimientos dispensadores de medicamentos', 'OPA 4: Verificación de estándares de habilitación de los establecimientos dispensadores de medicamentos', '080', '082'),
  ('5', 'Proceso de inspección, vigilancia y control, IVC', 'OPA 5: Proceso de inspección, vigilancia y control, IVC', '080', '082'),
  ('6', 'Control de población canina y felina.', 'OPA 6: Control de población canina y felina.', '080', '082'),
  ('7', 'Vigilancia del agua para consumo humano: control en puntos de vertimiento de agua domestica,', 'OPA 7: Vigilancia del agua para consumo humano: control en puntos de vertimiento de agua domestica,', '080', '082'),
  ('8', 'Radiación electromagnética.', 'OPA 8: Radiación electromagnética.', '080', '082'),
  ('9', 'Esterilizacion y vacunacion antirrabica canina y felina.', 'OPA 9: Esterilizacion y vacunacion antirrabica canina y felina.', '080', '082'),
  ('10', 'Capacitacion sobre tenencia responsable de mascotas', 'OPA 10: Capacitacion sobre tenencia responsable de mascotas', '080', '082'),
  ('11', 'Control de Plagas (artrópodos y roedores) moscos, mosquitos para predios publicos realizan fumigación. Cuando se traten de denuncias relacionadas con predios privados se hace una visita para verificar', 'OPA 11: Control de Plagas (artrópodos y roedores) moscos, mosquitos para predios publicos realizan fumigación. Cuando se traten de denuncias relacionadas con predios privados se hace una visita para verificar', '080', '082'),
  ('12', 'Visitas a Establecimientos y Empresas expendedoras o aplicadores de sustancia químicas', 'OPA 12: Visitas a Establecimientos y Empresas expendedoras o aplicadores de sustancia químicas', '080', '082'),
  ('13', 'Visita a establecimientos de preparación y comercialización  de alimentos y transporte de alimentos.', 'OPA 13: Visita a establecimientos de preparación y comercialización  de alimentos y transporte de alimentos.', '080', '082'),
  ('14', 'Registro de ejemplares caninos potencialmente peligrosos', 'OPA 14: Registro de ejemplares caninos potencialmente peligrosos', '080', '082'),
  ('15', 'Maltrato animal de gatos y perros unicamente (Si es una denuncia radicarlo como proceso policivo a Resolución de Conflictos).', 'OPA 15: Maltrato animal de gatos y perros unicamente (Si es una denuncia radicarlo como proceso policivo a Resolución de Conflictos).', '080', '082'),
  ('16', 'Adopcion canina y felina, albergue municipal', 'OPA 16: Adopcion canina y felina, albergue municipal', '080', '082')
) AS v(code, name, description, dep_code, subdep_code)
JOIN dependencies d ON d.code = v.dep_code
LEFT JOIN subdependencies s ON s.dependency_id = d.id AND s.code = v.subdep_code
WHERE NOT EXISTS (
    SELECT 1 FROM opas o 
    WHERE o.code = v.code 
    AND o.dependency_id = d.id 
    AND (o.subdependency_id = s.id OR (o.subdependency_id IS NULL AND s.id IS NULL))
);


-- load_opas_090_Secretaria_para_el_Desarrollo_Economico.sql (29 OPAs)
INSERT INTO opas (code, name, description, dependency_id, subdependency_id, is_active)
SELECT v.code, v.name, v.description, d.id, s.id, true
FROM (VALUES
  ('1', 'General', 'OPA 1: General', '090', '090'),
  ('1', 'Administracion plaza de mercado, vivero (centro de investigacion y desarrollo y tecnologia agropecuaria), Planta de Sacrificio y Faenado', 'OPA 1: Administracion plaza de mercado, vivero (centro de investigacion y desarrollo y tecnologia agropecuaria), Planta de Sacrificio y Faenado', '090', '091'),
  ('2', 'Centro de bienestar animal y COSO municipal', 'OPA 2: Centro de bienestar animal y COSO municipal', '090', '091'),
  ('3', 'Plan estrategico agropecuario, estadisticas agricolas', 'OPA 3: Plan estrategico agropecuario, estadisticas agricolas', '090', '091'),
  ('4', 'Asistencia tecnica rural para el sector agropecuario:  animales para consumo humano(pollos, conejos, codornices, lombrices, patos, piscos, pavos, vacas, cerdos)', 'OPA 4: Asistencia tecnica rural para el sector agropecuario:  animales para consumo humano(pollos, conejos, codornices, lombrices, patos, piscos, pavos, vacas, cerdos)', '090', '091'),
  ('5', 'Huertas urbanas', 'OPA 5: Huertas urbanas', '090', '091'),
  ('6', 'Seguridad y sostenibilidad alimentaria, plantulas y semillas de arboles frutales, hortalizas. *Semillas de plantas de produccion (arboles aromaticos (cidron, laurel, tomillo, plantulas como lechuga, cilantro)', 'OPA 6: Seguridad y sostenibilidad alimentaria, plantulas y semillas de arboles frutales, hortalizas. *Semillas de plantas de produccion (arboles aromaticos (cidron, laurel, tomillo, plantulas como lechuga, cilantro)', '090', '091'),
  ('7', 'Cadenas productivas y asociatividad empresarial, economia campesina', 'OPA 7: Cadenas productivas y asociatividad empresarial, economia campesina', '090', '091'),
  ('8', 'Programa Chia Emprende : Proyectos productivos de victimas, emprendimiento, ferias de emprendimiento, encuentros empresariales', 'OPA 8: Programa Chia Emprende : Proyectos productivos de victimas, emprendimiento, ferias de emprendimiento, encuentros empresariales', '090', '091'),
  ('9', 'Agencia publica de empleo: Desarrollo de competencias laborales, hojas de vida', 'OPA 9: Agencia publica de empleo: Desarrollo de competencias laborales, hojas de vida', '090', '091'),
  ('10', 'Bancarizacion y generacion de lineas de credito', 'OPA 10: Bancarizacion y generacion de lineas de credito', '090', '091'),
  ('11', 'Asistencia tecnica empresarial, proyectos de incubacion', 'OPA 11: Asistencia tecnica empresarial, proyectos de incubacion', '090', '091'),
  ('12', 'Solicitud de certificacion de actrividades agricolas predios  dedicados a actividades agrícolas y ganaderas', 'OPA 12: Solicitud de certificacion de actrividades agricolas predios  dedicados a actividades agrícolas y ganaderas', '090', '091'),
  ('13', 'Abandono o maltrato de especies mayores- pecuarias: (Vacas, cerdos, caballos, etc)', 'OPA 13: Abandono o maltrato de especies mayores- pecuarias: (Vacas, cerdos, caballos, etc)', '090', '091'),
  ('14', 'Impuesto al degüello de ganado mayor', 'OPA 14: Impuesto al degüello de ganado mayor', '090', '091'),
  ('15', 'Informacion general para apertura de establecimientos comerciales en el municipio', 'OPA 15: Informacion general para apertura de establecimientos comerciales en el municipio', '090', '091'),
  ('16', 'Solicitud de apoyo economico por reactivacion economica', 'OPA 16: Solicitud de apoyo economico por reactivacion economica', '090', '091'),
  ('1', 'Plan de desarrollo Turistico, proyectos de desarrollo turistico', 'OPA 1: Plan de desarrollo Turistico, proyectos de desarrollo turistico', '090', '092'),
  ('2', 'Fondo Nacional de Turismo- FONTUR', 'OPA 2: Fondo Nacional de Turismo- FONTUR', '090', '092'),
  ('3', 'Consejo municipal de Turismo, Consejo regional de seguridad turistica, policia de turismo', 'OPA 3: Consejo municipal de Turismo, Consejo regional de seguridad turistica, policia de turismo', '090', '092'),
  ('4', 'Festival Chia Gastronomica, ferias gastronomicas', 'OPA 4: Festival Chia Gastronomica, ferias gastronomicas', '090', '092'),
  ('5', 'Infraestructura turistica, señalizacion turistica', 'OPA 5: Infraestructura turistica, señalizacion turistica', '090', '092'),
  ('6', 'Vigilancia y control de prestadores de servicios turisticos frente al servicio (hoteles, agencias de viajes, guias turisticos, plataformas de comercializacion en linea, booking, Airbnb, apartamentos alquilados por dias), formalizacion turistica, superintendencia de industria y comercio en relacion con el turismo', 'OPA 6: Vigilancia y control de prestadores de servicios turisticos frente al servicio (hoteles, agencias de viajes, guias turisticos, plataformas de comercializacion en linea, booking, Airbnb, apartamentos alquilados por dias), formalizacion turistica, superintendencia de industria y comercio en relacion con el turismo', '090', '092'),
  ('7', 'Inventario de atractivos turisticos, rutas turisticas', 'OPA 7: Inventario de atractivos turisticos, rutas turisticas', '090', '092'),
  ('8', 'Zonas de desarrollo turistico prioritario', 'OPA 8: Zonas de desarrollo turistico prioritario', '090', '092'),
  ('9', 'Campañas de mercadeo y promocion turistica', 'OPA 9: Campañas de mercadeo y promocion turistica', '090', '092'),
  ('10', 'Capacitaciones para el sector turistico', 'OPA 10: Capacitaciones para el sector turistico', '090', '092'),
  ('11', 'Punto de informacion turistica', 'OPA 11: Punto de informacion turistica', '090', '092'),
  ('12', 'Registro nacional de turismo', 'OPA 12: Registro nacional de turismo', '090', '092')
) AS v(code, name, description, dep_code, subdep_code)
JOIN dependencies d ON d.code = v.dep_code
LEFT JOIN subdependencies s ON s.dependency_id = d.id AND s.code = v.subdep_code
WHERE NOT EXISTS (
    SELECT 1 FROM opas o 
    WHERE o.code = v.code 
    AND o.dependency_id = d.id 
    AND (o.subdependency_id = s.id OR (o.subdependency_id IS NULL AND s.id IS NULL))
);


-- Verificar carga del lote 3
SELECT 
    'LOTE 3 COMPLETADO' as estado,
    COUNT(*) as total_opas_en_lote
FROM opas o
JOIN dependencies d ON o.dependency_id = d.id
WHERE d.code IN ('070', '080', '090');

COMMIT;
