-- CARGA OPAs PARA Secretaria de Movilidad (49 OPAs)
BEGIN;

INSERT INTO opas (code, name, description, dependency_id, subdependency_id, is_active)
SELECT v.code, v.name, v.description, d.id, s.id, true
FROM (VALUES
  ('1', 'Tutelas dirigidas a la Secretaria de Movilidad', 'OPA 1: Tutelas dirigidas a la Secretaria de Movilidad', '110', '110'),
  ('2', 'Invitaciones externas a eventos del Secretario de despacho', 'OPA 2: Invitaciones externas a eventos del Secretario de despacho', '110', '110'),
  ('3', 'Requerimientos de contratacion de la secretaria', 'OPA 3: Requerimientos de contratacion de la secretaria', '110', '110'),
  ('4', 'Requerimientos desde el Concejo Municipal', 'OPA 4: Requerimientos desde el Concejo Municipal', '110', '110'),
  ('1', 'Requerimientos respecto al funcionamiento o novedades con el transporte publico del municipio', 'OPA 1: Requerimientos respecto al funcionamiento o novedades con el transporte publico del municipio', '110', '111'),
  ('2', 'Quejas y reclamos relacionados con el servicio y operación de transporte público', 'OPA 2: Quejas y reclamos relacionados con el servicio y operación de transporte público', '110', '111'),
  ('3', 'Prescripciones de comparendos del 2006 hacia atrás', 'OPA 3: Prescripciones de comparendos del 2006 hacia atrás', '110', '111'),
  ('4', 'Desvinculaciones de vehiculos de empresas transportaras (chatarrización)', 'OPA 4: Desvinculaciones de vehiculos de empresas transportaras (chatarrización)', '110', '111'),
  ('5', 'Capacidad transportadora', 'OPA 5: Capacidad transportadora', '110', '111'),
  ('6', 'Solicitudes de audiencias por comparendos', 'OPA 6: Solicitudes de audiencias por comparendos', '110', '111'),
  ('7', 'Suspension, cancelacion, devolucion de licencias de conduccion por embriaguez', 'OPA 7: Suspension, cancelacion, devolucion de licencias de conduccion por embriaguez', '110', '111'),
  ('8', 'Impugnacion de comparendos', 'OPA 8: Impugnacion de comparendos', '110', '111'),
  ('9', 'Expedientes disciplinarios', 'OPA 9: Expedientes disciplinarios', '110', '111'),
  ('10', 'Trámite de servicio publico de pasajeros', 'OPA 10: Trámite de servicio publico de pasajeros', '110', '111'),
  ('11', 'Paraderos de transporte publico del municipio', 'OPA 11: Paraderos de transporte publico del municipio', '110', '111'),
  ('12', 'Pruebas de alcoholemia', 'OPA 12: Pruebas de alcoholemia', '110', '111'),
  ('13', 'Copias de procesos de impugnación de comparendos dentro de procesos disciplinarios', 'OPA 13: Copias de procesos de impugnación de comparendos dentro de procesos disciplinarios', '110', '111'),
  ('14', 'Quejas y reclamos relacionados con la prestación de servicio al ciudadano por parte de la Union Temporal Circulemos de Chia - UT o quejas y reclamos relacionadas con trámites vehiculares.', 'OPA 14: Quejas y reclamos relacionados con la prestación de servicio al ciudadano por parte de la Union Temporal Circulemos de Chia - UT o quejas y reclamos relacionadas con trámites vehiculares.', '110', '111'),
  ('15', 'LOS IMPUESTOS DE VEHÍCULOS SE RECAUDAN DIRECTAMENTE EN LA GOBERNACIÓN DE CUNDINAMARCA EN BOGOTÁ', 'OPA 15: LOS IMPUESTOS DE VEHÍCULOS SE RECAUDAN DIRECTAMENTE EN LA GOBERNACIÓN DE CUNDINAMARCA EN BOGOTÁ', '110', '111'),
  ('1', 'Campañas y capacitaciones de seguridad vial', 'OPA 1: Campañas y capacitaciones de seguridad vial', '110', '112'),
  ('2', 'Agentes de transito', 'OPA 2: Agentes de transito', '110', '112'),
  ('3', 'Solicitudes de plan de manejo de transito PMT- Prorroga', 'OPA 3: Solicitudes de plan de manejo de transito PMT- Prorroga', '110', '112'),
  ('4', 'SOLICTUD DE PRORROGA para plan de manejo de transito PMT- Se  genera numero nuevo de radicado y se asocia al numero de radicado con el que se hizo la solictud inicalmente.', 'OPA 4: SOLICTUD DE PRORROGA para plan de manejo de transito PMT- Se  genera numero nuevo de radicado y se asocia al numero de radicado con el que se hizo la solictud inicalmente.', '110', '112'),
  ('5', 'Solicitudes de reductores de velocidad', 'OPA 5: Solicitudes de reductores de velocidad', '110', '112'),
  ('6', 'Solicitudes relacionadas con el funcionamiento o necesidades de semaforos', 'OPA 6: Solicitudes relacionadas con el funcionamiento o necesidades de semaforos', '110', '112'),
  ('7', 'Reportes de accidentes de transito', 'OPA 7: Reportes de accidentes de transito', '110', '112'),
  ('8', 'Permisos de circulacion vehicular por restricciones (¿novedades dia sin carro?)', 'OPA 8: Permisos de circulacion vehicular por restricciones (¿novedades dia sin carro?)', '110', '112'),
  ('9', 'Señalizacion de vias (¿requerimientos de mantenimiento??, instalacion de señalizacion informativa (hoteles, restaurantes, parqueaderos, hospitales, etc?', 'OPA 9: Señalizacion de vias (¿requerimientos de mantenimiento??, instalacion de señalizacion informativa (hoteles, restaurantes, parqueaderos, hospitales, etc?', '110', '112'),
  ('10', 'Plan Maestro de tránsito', 'OPA 10: Plan Maestro de tránsito', '110', '112'),
  ('11', 'Límite de tarifa de parqueadero', 'OPA 11: Límite de tarifa de parqueadero', '110', '112'),
  ('12', 'Información plan local de seguridad vial', 'OPA 12: Información plan local de seguridad vial', '110', '112'),
  ('13', 'Solicitud de Restricción de carga en Chía 3,4 toneladas o más. Decreto 279 de 2024.
UNICAMENTE: Empresas ubicadas en Chia, conductores o propietarios de vehiculos que residen en Chia. 

Para el caso de empresas registradas en Bogotá, pero que tienen una sede o sucursal en Chía, pueden transitar con el ultimo recibo de pago del ICA y adicional, el formato de remision de la empresa, donde conste la direccion de Chia donde va a ser entregado o recogido el producto.', 'OPA 13: Solicitud de Restricción de carga en Chía 3,4 toneladas o más. Decreto 279 de 2024.
UNICAMENTE: Empresas ubicadas en Chia, conductores o propietarios de vehiculos que residen en Chia. 

Para el caso de empresas registradas en Bogotá, pero que tienen una sede o sucursal en Chía, pueden transitar con el ultimo recibo de pago del ICA y adicional, el formato de remision de la empresa, donde conste la direccion de Chia donde va a ser entregado o recogido el producto.', '110', '112'),
  ('1', 'Inmovilizacion de vehiculos', 'OPA 1: Inmovilizacion de vehiculos', '110', '113'),
  ('2', 'Salidas de patios', 'OPA 2: Salidas de patios', '110', '113'),
  ('3', 'Prescripciones de comparendos del 2007 en adelante', 'OPA 3: Prescripciones de comparendos del 2007 en adelante', '110', '113'),
  ('4', 'Tramites con placas o certificados de tradicion', 'OPA 4: Tramites con placas o certificados de tradicion', '110', '113'),
  ('5', 'Traspasos, levantamiento de prendas y cancelacion de matriculas', 'OPA 5: Traspasos, levantamiento de prendas y cancelacion de matriculas', '110', '113'),
  ('6', 'Derechos de peticion de prescripciones, descargue de comparendos de las plataformas HQ RUNT- TRANSITO CHIA', 'OPA 6: Derechos de peticion de prescripciones, descargue de comparendos de las plataformas HQ RUNT- TRANSITO CHIA', '110', '113'),
  ('7', 'Embargos, levantamiento o inscripcion de medidas cautelares con referencia a vehiculos - 
DERECHO DE PETICIÓN procede cuando viene directamente del juzgado
REMISION DE DOCUMENTOS. Cuando viene directamente del peticionario pues es el juzgado quien dictamina', 'OPA 7: Embargos, levantamiento o inscripcion de medidas cautelares con referencia a vehiculos - 
DERECHO DE PETICIÓN procede cuando viene directamente del juzgado
REMISION DE DOCUMENTOS. Cuando viene directamente del peticionario pues es el juzgado quien dictamina', '110', '113'),
  ('8', 'Traslado o devoluciones de cuentas de vehiculos- REMISION DE DOCUMENTOS', 'OPA 8: Traslado o devoluciones de cuentas de vehiculos- REMISION DE DOCUMENTOS', '110', '113'),
  ('9', 'Radicacion, traslados, devolucion de cuenta placas vehiculares', 'OPA 9: Radicacion, traslados, devolucion de cuenta placas vehiculares', '110', '113'),
  ('10', 'Solicitud historiales vehiculares', 'OPA 10: Solicitud historiales vehiculares', '110', '113'),
  ('11', 'Informacion sobre acuerdos de pago comparendos', 'OPA 11: Informacion sobre acuerdos de pago comparendos', '110', '113'),
  ('12', 'Tramites de personas (inscripcion RUNT, licencias de conduccion)', 'OPA 12: Tramites de personas (inscripcion RUNT, licencias de conduccion)', '110', '113'),
  ('13', 'Agendamiento citas para tramites', 'OPA 13: Agendamiento citas para tramites', '110', '113'),
  ('14', 'Certificado sobre retencion en la fuente por tramite de vehiculos', 'OPA 14: Certificado sobre retencion en la fuente por tramite de vehiculos', '110', '113'),
  ('15', 'tarjetas de operación', 'OPA 15: tarjetas de operación', '110', '113'),
  ('16', 'Certificación de cumplimiento de requisitos para el registro inicial de vehículo nuevo en reposición con Exclusión de IVA (CREI)', 'OPA 16: Certificación de cumplimiento de requisitos para el registro inicial de vehículo nuevo en reposición con Exclusión de IVA (CREI)', '110', '113'),
  ('17', 'Aprehension y secuestro de vehiculos', 'OPA 17: Aprehension y secuestro de vehiculos', '110', '113')
) AS v(code, name, description, dep_code, subdep_code)
JOIN dependencies d ON d.code = v.dep_code
LEFT JOIN subdependencies s ON s.dependency_id = d.id AND s.code = v.subdep_code
WHERE NOT EXISTS (
    SELECT 1 FROM opas o 
    WHERE o.code = v.code 
    AND o.dependency_id = d.id 
    AND (o.subdependency_id = s.id OR (o.subdependency_id IS NULL AND s.id IS NULL))
);

-- Verificar carga
SELECT 
    'Secretaria de Movilidad' as dependencia,
    COUNT(*) as opas_cargados
FROM opas o
JOIN dependencies d ON o.dependency_id = d.id
WHERE d.code = '110';

COMMIT;
