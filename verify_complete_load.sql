-- VERIFICACIÓN FINAL DE CARGA COMPLETA
SELECT 
    'CARGA COMPLETA DE OPAs' as titulo,
    COUNT(*) as total_opas_cargados,
    COUNT(DISTINCT dependency_id) as dependencias_con_opas,
    COUNT(DISTINCT subdependency_id) as subdependencias_con_opas
FROM opas;

-- Estadísticas finales por dependencia
SELECT 
    d.name as dependencia,
    d.code as codigo,
    COUNT(o.id) as opas_cargados,
    ROUND(COUNT(o.id) * 100.0 / 721, 2) as porcentaje_del_total
FROM dependencies d
LEFT JOIN opas o ON o.dependency_id = d.id
WHERE o.id IS NOT NULL
GROUP BY d.id, d.name, d.code
ORDER BY opas_cargados DESC;

-- Progreso final
SELECT 
    721 as total_opas_esperados,
    COUNT(*) as opas_cargados,
    ROUND(COUNT(*) * 100.0 / 721, 2) as porcentaje_completado,
    (721 - COUNT(*)) as opas_pendientes,
    CASE 
        WHEN COUNT(*) = 721 THEN '🎉 CARGA COMPLETA!'
        ELSE '⚠️ Faltan OPAs por cargar'
    END as estado
FROM opas;

-- Vista unificada de todos los procedimientos
SELECT 
    'RESUMEN FINAL' as titulo,
    COUNT(*) as total_procedures,
    SUM(CASE WHEN type = 'TRAMITE' THEN 1 ELSE 0 END) as tramites,
    SUM(CASE WHEN type = 'OPA' THEN 1 ELSE 0 END) as opas
FROM all_procedures;
