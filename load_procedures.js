const fs = require('fs');

// Leer archivo JSON de trámites
const tramites = JSON.parse(fs.readFileSync('tramites_chia_optimo.json', 'utf8'));

// Función para escapar comillas simples en SQL
function escapeSql(str) {
  if (!str) return '';
  return str.replace(/'/g, "''");
}

// Función para parsear el costo
function parseCost(costoStr) {
  if (!costoStr) return null;
  
  // Si contiene "gratuito" o "sin costo", es 0
  if (costoStr.toLowerCase().includes('gratuito') || 
      costoStr.toLowerCase().includes('sin costo') ||
      costoStr.toLowerCase().includes('no tiene costo')) {
    return 0;
  }
  
  // Si contiene números, extraer el primer número
  const match = costoStr.match(/\d+/);
  if (match) {
    return parseInt(match[0]);
  }
  
  // Si no se puede determinar, devolver null (costo variable)
  return null;
}

// Función para parsear tiempo de respuesta en días
function parseResponseTime(tiempoStr) {
  if (!tiempoStr) return null;
  
  const str = tiempoStr.toLowerCase();
  
  // Buscar números seguidos de "día" o "días"
  const diasMatch = str.match(/(\d+)\s*d[íi]as?/);
  if (diasMatch) {
    return parseInt(diasMatch[1]);
  }
  
  // Buscar "inmediato" o "en línea"
  if (str.includes('inmediato') || str.includes('en línea') || str.includes('tiempo real')) {
    return 0;
  }
  
  // Buscar horas y convertir a días (redondeando hacia arriba)
  const horasMatch = str.match(/(\d+)\s*horas?/);
  if (horasMatch) {
    return Math.ceil(parseInt(horasMatch[1]) / 24);
  }
  
  // Buscar meses y convertir a días
  const mesesMatch = str.match(/(\d+)\s*mes(es)?/);
  if (mesesMatch) {
    return parseInt(mesesMatch[1]) * 30;
  }
  
  // Por defecto, devolver null
  return null;
}

console.log('-- INSERTAR TRÁMITES EN LA BASE DE DATOS');
console.log('-- Total de trámites a procesar:', tramites.length);
console.log('');

tramites.forEach((tramite, index) => {
  const nombre = escapeSql(tramite.Nombre || '');
  const formulario = tramite.Formulario === 'Sí' ? true : false;
  const tiempoRespuesta = parseResponseTime(tramite['Tiempo de respuesta']);
  const costo = parseCost(tramite['¿Tiene pago?']);
  const costoDescripcion = escapeSql(tramite['¿Tiene pago?'] || '');
  const urlSuit = escapeSql(tramite['Visualización trámite en el SUIT'] || '');
  const urlGovCo = escapeSql(tramite['Visualización trámite en GOV.CO'] || '');
  const codigoDep = tramite.codigo_dependencia;
  const codigoSub = tramite.codigo_subdependencia;
  
  console.log(`-- Trámite ${index + 1}: ${nombre.substring(0, 50)}...`);
  console.log(`INSERT INTO procedures (
    name, 
    description, 
    requires_form, 
    response_time_days, 
    cost, 
    cost_description,
    suit_url,
    gov_co_url,
    dependency_id,
    subdependency_id,
    is_active
  ) SELECT 
    '${nombre}',
    '${nombre}', -- Usar nombre como descripción por ahora
    ${formulario},
    ${tiempoRespuesta || 'NULL'},
    ${costo || 'NULL'},
    '${costoDescripcion}',
    '${urlSuit}',
    '${urlGovCo}',
    d.id,
    s.id,
    true
  FROM dependencies d
  LEFT JOIN subdependencies s ON s.dependency_id = d.id AND s.code = '${codigoSub}'
  WHERE d.code = '${codigoDep}';`);
  console.log('');
});

console.log('-- ESTADÍSTICAS DE PROCESAMIENTO:');
console.log(`-- Total de trámites procesados: ${tramites.length}`);

// Estadísticas de costos
const conCosto = tramites.filter(t => parseCost(t['¿Tiene pago?']) !== null && parseCost(t['¿Tiene pago?']) > 0).length;
const gratuitos = tramites.filter(t => parseCost(t['¿Tiene pago?']) === 0).length;
const costoVariable = tramites.filter(t => parseCost(t['¿Tiene pago?']) === null).length;

console.log(`-- Trámites con costo fijo: ${conCosto}`);
console.log(`-- Trámites gratuitos: ${gratuitos}`);
console.log(`-- Trámites con costo variable: ${costoVariable}`);

// Estadísticas de tiempo de respuesta
const conTiempo = tramites.filter(t => parseResponseTime(t['Tiempo de respuesta']) !== null).length;
const sinTiempo = tramites.filter(t => parseResponseTime(t['Tiempo de respuesta']) === null).length;

console.log(`-- Trámites con tiempo de respuesta definido: ${conTiempo}`);
console.log(`-- Trámites sin tiempo de respuesta definido: ${sinTiempo}`);
