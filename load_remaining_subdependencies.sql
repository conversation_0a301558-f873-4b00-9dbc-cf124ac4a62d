-- CARGAR SUBDEPENDENCIAS RESTANTES

-- Secretaría de Gobierno (030)
INSERT INTO subdependencies (dependency_id, code, name, acronym) 
  SELECT d.id, '030', 'Directo', 'SGOB' FROM dependencies d WHERE d.code = '030';
INSERT INTO subdependencies (dependency_id, code, name, acronym) 
  SELECT d.id, '031', 'Dirección de seguridad y Convivencia Ciudadana', 'DSCC' FROM dependencies d WHERE d.code = '030';
INSERT INTO subdependencies (dependency_id, code, name, acronym) 
  SELECT d.id, '032', 'Dirección de Asuntos Étnicos Raciales Religiosos y Posconflicto', 'DAERRP' FROM dependencies d WHERE d.code = '030';
INSERT INTO subdependencies (dependency_id, code, name, acronym) 
  SELECT d.id, '033', 'Dirección de Derechos y Resolución de Conflictos', 'DDRC' FROM dependencies d WHERE d.code = '030';
INSERT INTO subdependencies (dependency_id, code, name, acronym) 
  SELECT d.id, '330', 'Comisaria Primera de Familia', 'CPF' FROM dependencies d WHERE d.code = '030';
INSERT INTO subdependencies (dependency_id, code, name, acronym) 
  SELECT d.id, '331', 'Comisaria Segunda de Familia', 'CSF' FROM dependencies d WHERE d.code = '030';
INSERT INTO subdependencies (dependency_id, code, name, acronym) 
  SELECT d.id, '332', 'Comisaria Tercera de Familia', 'CTF' FROM dependencies d WHERE d.code = '030';
INSERT INTO subdependencies (dependency_id, code, name, acronym) 
  SELECT d.id, '333', 'Comisaria Cuarta de Familia', 'CCF' FROM dependencies d WHERE d.code = '030';
INSERT INTO subdependencies (dependency_id, code, name, acronym) 
  SELECT d.id, '334', 'Inspección Primera de Policía', 'IPP' FROM dependencies d WHERE d.code = '030';
INSERT INTO subdependencies (dependency_id, code, name, acronym) 
  SELECT d.id, '335', 'Inspección Segunda de Policía', 'PSP' FROM dependencies d WHERE d.code = '030';
INSERT INTO subdependencies (dependency_id, code, name, acronym) 
  SELECT d.id, '336', 'Inspección Tercera de Policía', 'ITP' FROM dependencies d WHERE d.code = '030';
INSERT INTO subdependencies (dependency_id, code, name, acronym) 
  SELECT d.id, '337', 'Inspección Cuarta de Policía', 'ICP' FROM dependencies d WHERE d.code = '030';
INSERT INTO subdependencies (dependency_id, code, name, acronym) 
  SELECT d.id, '338', 'Inspección Quinta de Policía', 'IQP' FROM dependencies d WHERE d.code = '030';
INSERT INTO subdependencies (dependency_id, code, name, acronym) 
  SELECT d.id, '339', 'Inspección Sexta de Policía', 'ISP' FROM dependencies d WHERE d.code = '030';

-- Secretaría de Obras Públicas (050)
INSERT INTO subdependencies (dependency_id, code, name, acronym) 
  SELECT d.id, '050', 'Directo', 'SOP' FROM dependencies d WHERE d.code = '050';
INSERT INTO subdependencies (dependency_id, code, name, acronym) 
  SELECT d.id, '051', 'Dirección de Infraestructura', 'DI' FROM dependencies d WHERE d.code = '050';
INSERT INTO subdependencies (dependency_id, code, name, acronym) 
  SELECT d.id, '052', 'Dirección de Programación, Estudios y Diseños', 'DPED' FROM dependencies d WHERE d.code = '050';
INSERT INTO subdependencies (dependency_id, code, name, acronym) 
  SELECT d.id, '053', 'Dirección de Valorización', 'DV' FROM dependencies d WHERE d.code = '050';

-- Secretaría de Desarrollo Social (060)
INSERT INTO subdependencies (dependency_id, code, name, acronym) 
  SELECT d.id, '060', 'Directo', 'SDS' FROM dependencies d WHERE d.code = '060';
INSERT INTO subdependencies (dependency_id, code, name, acronym) 
  SELECT d.id, '061', 'Dirección de Ciudadanía Juvenil', 'DCJ' FROM dependencies d WHERE d.code = '060';
INSERT INTO subdependencies (dependency_id, code, name, acronym) 
  SELECT d.id, '062', 'Dirección de Acción Social', 'DAS' FROM dependencies d WHERE d.code = '060';
INSERT INTO subdependencies (dependency_id, code, name, acronym) 
  SELECT d.id, '063', 'Dirección de Cultura', 'DC' FROM dependencies d WHERE d.code = '060';

-- Secretaría de Educación (070)
INSERT INTO subdependencies (dependency_id, code, name, acronym) 
  SELECT d.id, '070', 'Directo', 'SE' FROM dependencies d WHERE d.code = '070';
INSERT INTO subdependencies (dependency_id, code, name, acronym) 
  SELECT d.id, '071', 'Dirección de Inspección y Vigilancia', 'DIV' FROM dependencies d WHERE d.code = '070';
INSERT INTO subdependencies (dependency_id, code, name, acronym) 
  SELECT d.id, '072', 'Dirección de Gestión y Fomento a la Educación', 'DGFE' FROM dependencies d WHERE d.code = '070';
INSERT INTO subdependencies (dependency_id, code, name, acronym) 
  SELECT d.id, '073', 'Dirección Administrativa y Financiera', 'DAF' FROM dependencies d WHERE d.code = '070';

-- Secretaría de Salud (080)
INSERT INTO subdependencies (dependency_id, code, name, acronym)
  SELECT d.id, '080', 'Directo', 'SS' FROM dependencies d WHERE d.code = '080';
INSERT INTO subdependencies (dependency_id, code, name, acronym)
  SELECT d.id, '081', 'Dirección de Salud Publica', 'DSP' FROM dependencies d WHERE d.code = '080';
INSERT INTO subdependencies (dependency_id, code, name, acronym)
  SELECT d.id, '082', 'Dirección de Vigilancia y Control', 'DVC' FROM dependencies d WHERE d.code = '080';

-- Secretaría para el Desarrollo Económico (090)
INSERT INTO subdependencies (dependency_id, code, name, acronym)
  SELECT d.id, '090', 'Directo', 'SDE' FROM dependencies d WHERE d.code = '090';
INSERT INTO subdependencies (dependency_id, code, name, acronym)
  SELECT d.id, '091', 'Dirección de Desarrollo Agropecuario y Empresarial', 'DDAE' FROM dependencies d WHERE d.code = '090';
INSERT INTO subdependencies (dependency_id, code, name, acronym)
  SELECT d.id, '092', 'Dirección de Turismo', 'DT' FROM dependencies d WHERE d.code = '090';

-- Secretaría de Medio Ambiente (100)
INSERT INTO subdependencies (dependency_id, code, name, acronym)
  SELECT d.id, '101', 'Forestal', 'SMA' FROM dependencies d WHERE d.code = '100';
INSERT INTO subdependencies (dependency_id, code, name, acronym)
  SELECT d.id, '102', 'Control y conservación ambiental', 'SMA' FROM dependencies d WHERE d.code = '100';
INSERT INTO subdependencies (dependency_id, code, name, acronym)
  SELECT d.id, '103', 'Sistema hídrico', 'SMA' FROM dependencies d WHERE d.code = '100';
INSERT INTO subdependencies (dependency_id, code, name, acronym)
  SELECT d.id, '104', 'Informes Técnicos', 'SMA' FROM dependencies d WHERE d.code = '100';
INSERT INTO subdependencies (dependency_id, code, name, acronym)
  SELECT d.id, '105', 'PGIRS', 'SMA' FROM dependencies d WHERE d.code = '100';
INSERT INTO subdependencies (dependency_id, code, name, acronym)
  SELECT d.id, '106', 'Biodiversidad', 'SMA' FROM dependencies d WHERE d.code = '100';
INSERT INTO subdependencies (dependency_id, code, name, acronym)
  SELECT d.id, '107', 'Educación ambiental', 'SMA' FROM dependencies d WHERE d.code = '100';
INSERT INTO subdependencies (dependency_id, code, name, acronym)
  SELECT d.id, '108', 'Visita Técnica vigilancia y control fuentes contaminantes', 'SMA' FROM dependencies d WHERE d.code = '100';
INSERT INTO subdependencies (dependency_id, code, name, acronym)
  SELECT d.id, '109', 'Publicidad', 'SMA' FROM dependencies d WHERE d.code = '100';

-- Secretaría de Movilidad (110)
INSERT INTO subdependencies (dependency_id, code, name, acronym)
  SELECT d.id, '110', 'Directo', 'SM' FROM dependencies d WHERE d.code = '110';
INSERT INTO subdependencies (dependency_id, code, name, acronym)
  SELECT d.id, '111', 'Dirección de Servicios de Movilidad y Gestión del Transporte', 'DSMGT' FROM dependencies d WHERE d.code = '110';
INSERT INTO subdependencies (dependency_id, code, name, acronym)
  SELECT d.id, '112', 'Dirección de Educación, Seguridad Vial y Control de Transito', 'DESVCT' FROM dependencies d WHERE d.code = '110';
INSERT INTO subdependencies (dependency_id, code, name, acronym)
  SELECT d.id, '113', 'Unión Temporal Circulemos Chía', 'UTCCH' FROM dependencies d WHERE d.code = '110';

-- Secretaría de Participación Ciudadana (120)
INSERT INTO subdependencies (dependency_id, code, name, acronym)
  SELECT d.id, '120', 'Directo', 'SPCAC' FROM dependencies d WHERE d.code = '120';

-- Descentralizados (200)
INSERT INTO subdependencies (dependency_id, code, name, acronym)
  SELECT d.id, '201', 'IDUVI', 'IDUVI' FROM dependencies d WHERE d.code = '200';
INSERT INTO subdependencies (dependency_id, code, name, acronym)
  SELECT d.id, '202', 'IMRD', 'IMRD' FROM dependencies d WHERE d.code = '200';
INSERT INTO subdependencies (dependency_id, code, name, acronym)
  SELECT d.id, '203', 'EMSERCHIA', 'EMSERCHIA' FROM dependencies d WHERE d.code = '200';
INSERT INTO subdependencies (dependency_id, code, name, acronym)
  SELECT d.id, '204', 'PERSONERIA', 'PERSONERIA' FROM dependencies d WHERE d.code = '200';
INSERT INTO subdependencies (dependency_id, code, name, acronym)
  SELECT d.id, '212', 'AGUSTIN CODAZZI', 'CODAZZI' FROM dependencies d WHERE d.code = '200';
INSERT INTO subdependencies (dependency_id, code, name, acronym)
  SELECT d.id, '214', 'CUERPO DE BOMBEROS CHIA', 'BOMBEROS' FROM dependencies d WHERE d.code = '200';
INSERT INTO subdependencies (dependency_id, code, name, acronym)
  SELECT d.id, '215', 'DEFENSA CIVIL COLOMBIANA', 'DEFCIVIL' FROM dependencies d WHERE d.code = '200';
