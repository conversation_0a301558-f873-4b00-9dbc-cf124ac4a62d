-- =====================================================
-- CARGA MASIVA DE OPAs (OTROS PROCEDIMIENTOS ADMINISTRATIVOS)
-- Total de OPAs a cargar: 721
-- Fecha de generación: $(date)
-- =====================================================

BEGIN;

-- Verificar que existen las tablas necesarias
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'opas') THEN
        RAISE EXCEPTION 'La tabla opas no existe. Ejecutar primero el script de creación de esquema.';
    END IF;
END $$;


-- LOTE DE 50 OPAs
INSERT INTO opas (code, name, description, dependency_id, subdependency_id, is_active)
SELECT v.code, v.name, v.description, d.id, s.id, true
FROM (VALUES
  ('1', 'Si por solicitud del ciudadano exige la radicacion al alcalde, se debe indicar en el campo de observaciones "Radicado al despacho por solicitud del ciudadano"', 'OPA 1: Si por solicitud del ciudadano exige la radicacion al alcalde, se debe indicar en el campo de observaciones "Radicado al despacho por solicitud del ciudadano"', '000', '000'),
  ('2', 'Las solicitudes de citas al Alcalde y hojas de vida que requieran ser conocidas por el Alcalde, al momento de radicar seleccionar el tipo documental "REMISIÓN DE DOCUMENTOS".', 'OPA 2: Las solicitudes de citas al Alcalde y hojas de vida que requieran ser conocidas por el Alcalde, al momento de radicar seleccionar el tipo documental "REMISIÓN DE DOCUMENTOS".', '000', '000'),
  ('3', 'Las comunicaciones que se reciban de las IAS (Contraloria, Procuraduria, Fiscalia, etc) se deben radicar directamente al Despacho del Alcalde.', 'OPA 3: Las comunicaciones que se reciban de las IAS (Contraloria, Procuraduria, Fiscalia, etc) se deben radicar directamente al Despacho del Alcalde.', '000', '000'),
  ('4', 'Toma de juramento para nacionalidad colombiana', 'OPA 4: Toma de juramento para nacionalidad colombiana', '000', '000'),
  ('5', 'TEMAS RELACIONADOS CON EL RIO BOGOTÁ, Sentencia del rio Bogotá, temas relacionados con el rio Bogotá (debe ir con copia a defensa judicial)', 'OPA 5: TEMAS RELACIONADOS CON EL RIO BOGOTÁ, Sentencia del rio Bogotá, temas relacionados con el rio Bogotá (debe ir con copia a defensa judicial)', '000', '000'),
  ('6', 'Temas relacionados con la Planta de Tratamiento de Aguas Residuales (PTAR)', 'OPA 6: Temas relacionados con la Planta de Tratamiento de Aguas Residuales (PTAR)', '000', '000'),
  ('1', 'Certificado de residencia', 'OPA 1: Certificado de residencia', '000', '001'),
  ('2', 'Concepto juridico de las situaciones administrativas de la entidad', 'OPA 2: Concepto juridico de las situaciones administrativas de la entidad', '000', '001'),
  ('3', 'Inscripción de la propiedad horizontal', 'OPA 3: Inscripción de la propiedad horizontal', '000', '001'),
  ('4', 'Inscripción o cambio del representante legal y/o revisor fiscal de la propiedad horizontal -certificación personería juridica', 'OPA 4: Inscripción o cambio del representante legal y/o revisor fiscal de la propiedad horizontal -certificación personería juridica', '000', '001'),
  ('5', 'Registro de extinción de la propiedad horizontal', 'OPA 5: Registro de extinción de la propiedad horizontal', '000', '001'),
  ('6', 'Certificado de registros de marca', 'OPA 6: Certificado de registros de marca', '000', '001'),
  ('7', 'Solicitudes de información relacionada con proyectos de acuerdo', 'OPA 7: Solicitudes de información relacionada con proyectos de acuerdo', '000', '001'),
  ('8', 'Solicitudes de información relacionada con decretos', 'OPA 8: Solicitudes de información relacionada con decretos', '000', '001'),
  ('9', 'Para el trámite: "Inscripción de la propiedad horizontal": únicamente asociar las solicitudes que cumplan con los criterios y anexos del formulario " 1. INSCRIPCIÓN Y CERTIFICACIÓN DE PROPIEDAD HORIZONTAL" .', 'OPA 9: Para el trámite: "Inscripción de la propiedad horizontal": únicamente asociar las solicitudes que cumplan con los criterios y anexos del formulario " 1. INSCRIPCIÓN Y CERTIFICACIÓN DE PROPIEDAD HORIZONTAL" .', '000', '001'),
  ('10', 'Para el trámite "Inscripción o cambio del representante legal y/o revisor fiscal de la propiedad horizontal", asociar las solicitudes que cumplan con los criterios y anexos del formulario para los puntos  "2.ACTUALIZACIÓN POR CAMBIO DE REPRESENTACIÓN LEGAL Y/O REVISOR FISCAL; 3.ACTUALIZACIÓN DE FECHA; 4.REGISTRO DE EXTINCIÓN DE PROPIEDAD HORIZONTAL; 5. FOTOCOPIA DE LA CERTIFICACIÓN".', 'OPA 10: Para el trámite "Inscripción o cambio del representante legal y/o revisor fiscal de la propiedad horizontal", asociar las solicitudes que cumplan con los criterios y anexos del formulario para los puntos  "2.ACTUALIZACIÓN POR CAMBIO DE REPRESENTACIÓN LEGAL Y/O REVISOR FISCAL; 3.ACTUALIZACIÓN DE FECHA; 4.REGISTRO DE EXTINCIÓN DE PROPIEDAD HORIZONTAL; 5. FOTOCOPIA DE LA CERTIFICACIÓN".', '000', '001'),
  ('1', 'Informes para la contraloria, procuraduria relacionada con la ejecución de contratos', 'OPA 1: Informes para la contraloria, procuraduria relacionada con la ejecución de contratos', '000', '002'),
  ('2', 'lo que llega despues de la vigencia 2016 en adelante es con la oficina de contratación, las vigencias de años anteriores con archivo general', 'OPA 2: lo que llega despues de la vigencia 2016 en adelante es con la oficina de contratación, las vigencias de años anteriores con archivo general', '000', '002'),
  ('3', 'Solicitud de información o certificados de contratos ejecutados cuando se desconozca el supervisor solo las personas que han laborado con la oficina de contratación. es por cada dependencia.', 'OPA 3: Solicitud de información o certificados de contratos ejecutados cuando se desconozca el supervisor solo las personas que han laborado con la oficina de contratación. es por cada dependencia.', '000', '002'),
  ('1', 'Demandas dirigidas a la Alcaldía Municipal', 'OPA 1: Demandas dirigidas a la Alcaldía Municipal', '000', '003'),
  ('2', 'Procesos judiciales', 'OPA 2: Procesos judiciales', '000', '003'),
  ('3', 'Providencias', 'OPA 3: Providencias', '000', '003'),
  ('4', 'Conciliaciones dirigidas a la Alcaldía Municipal', 'OPA 4: Conciliaciones dirigidas a la Alcaldía Municipal', '000', '003'),
  ('5', 'Negociaciones de deudas por insolvencia economica, con el tipo documental Proceso Judicial', 'OPA 5: Negociaciones de deudas por insolvencia economica, con el tipo documental Proceso Judicial', '000', '003'),
  ('6', 'Accion de repeticion por parte de autoridades', 'OPA 6: Accion de repeticion por parte de autoridades', '000', '003'),
  ('7', 'Tutelas - se radica a la dependencia responsable y a defensa judicial para conocimiento', 'OPA 7: Tutelas - se radica a la dependencia responsable y a defensa judicial para conocimiento', '000', '003'),
  ('8', 'Solicitud de informes de la Agencia Nacional de Defensa Judicial', 'OPA 8: Solicitud de informes de la Agencia Nacional de Defensa Judicial', '000', '003'),
  ('1', 'Informes de auditoria remitidos por contraloria, Contaduria General de la República, SIAOBSERVA, SIACONTRALORIA', 'OPA 1: Informes de auditoria remitidos por contraloria, Contaduria General de la República, SIAOBSERVA, SIACONTRALORIA', '000', '004'),
  ('2', 'Planes de mejoramiento de auditoria  externas', 'OPA 2: Planes de mejoramiento de auditoria  externas', '000', '004'),
  ('3', 'Requerimiento con entidades externas de control (contraloria)', 'OPA 3: Requerimiento con entidades externas de control (contraloria)', '000', '004'),
  ('4', 'Todas las comunicaciones dirigidas por la CONTRALORIA independientemente del tema', 'OPA 4: Todas las comunicaciones dirigidas por la CONTRALORIA independientemente del tema', '000', '004'),
  ('5', 'Plan Anticorrupción y de Atención al Ciudadano', 'OPA 5: Plan Anticorrupción y de Atención al Ciudadano', '000', '004'),
  ('6', 'Indice de transparencia activa- ITA', 'OPA 6: Indice de transparencia activa- ITA', '000', '004'),
  ('1', 'Solicitudes de servicios de internet', 'OPA 1: Solicitudes de servicios de internet', '000', '005'),
  ('2', 'Prestamo de los Puntos Vive Digital', 'OPA 2: Prestamo de los Puntos Vive Digital', '000', '005'),
  ('3', 'Politica de seguridad digital y manejo de la información', 'OPA 3: Politica de seguridad digital y manejo de la información', '000', '005'),
  ('4', 'Convenios y proyectos informáticos de la alcaldía y el municipio', 'OPA 4: Convenios y proyectos informáticos de la alcaldía y el municipio', '000', '005'),
  ('5', 'Sistemas de información', 'OPA 5: Sistemas de información', '000', '005'),
  ('6', 'Conectividad en internet del municipio', 'OPA 6: Conectividad en internet del municipio', '000', '005'),
  ('1', 'Solicitudes relacionadas con la administracion o contenidos de la pagina web y redes sociales de la alcaldia', 'OPA 1: Solicitudes relacionadas con la administracion o contenidos de la pagina web y redes sociales de la alcaldia', '000', '006'),
  ('2', 'Uso de imagen institucional', 'OPA 2: Uso de imagen institucional', '000', '006'),
  ('3', 'Relaciones publicas del alcalde con eventos externos', 'OPA 3: Relaciones publicas del alcalde con eventos externos', '000', '006'),
  ('1', 'Plan anticorrupcion', 'OPA 1: Plan anticorrupcion', '010', '010'),
  ('2', 'Información referente a patrimonio de predios en el municipio', 'OPA 2: Información referente a patrimonio de predios en el municipio', '010', '010'),
  ('1', 'Documentación y procedimientos relacionados con el SISBEN (Encuesta, inclusion en la base, retiro)', 'OPA 1: Documentación y procedimientos relacionados con el SISBEN (Encuesta, inclusion en la base, retiro)', '010', '011'),
  ('2', 'Solicitud de cartografias del municipio', 'OPA 2: Solicitud de cartografias del municipio', '010', '011'),
  ('3', 'Certificado de nomenclatura', 'OPA 3: Certificado de nomenclatura', '010', '011'),
  ('4', 'Georreferenciación', 'OPA 4: Georreferenciación', '010', '011'),
  ('5', 'Documentación y procedimientos relacionados con Estratificación', 'OPA 5: Documentación y procedimientos relacionados con Estratificación', '010', '011'),
  ('1', 'Plan operativo anual de inversiones', 'OPA 1: Plan operativo anual de inversiones', '010', '012')
) AS v(code, name, description, dep_code, subdep_code)
JOIN dependencies d ON d.code = v.dep_code
LEFT JOIN subdependencies s ON s.dependency_id = d.id AND s.code = v.subdep_code
WHERE NOT EXISTS (
    SELECT 1 FROM opas o 
    WHERE o.code = v.code 
    AND o.dependency_id = d.id 
    AND (o.subdependency_id = s.id OR (o.subdependency_id IS NULL AND s.id IS NULL))
);


-- LOTE DE 50 OPAs
INSERT INTO opas (code, name, description, dependency_id, subdependency_id, is_active)
SELECT v.code, v.name, v.description, d.id, s.id, true
FROM (VALUES
  ('2', 'Información respecto a las politicas publicas del municipio', 'OPA 2: Información respecto a las politicas publicas del municipio', '010', '012'),
  ('3', 'Regalias', 'OPA 3: Regalias', '010', '012'),
  ('4', 'Informacion del plan de desarrollo general (lo especifico puede dirigirse a la dependencia requerida)', 'OPA 4: Informacion del plan de desarrollo general (lo especifico puede dirigirse a la dependencia requerida)', '010', '012'),
  ('1', 'Concepto de uso de suelos', 'OPA 1: Concepto de uso de suelos', '010', '013'),
  ('2', 'Conceptos de no riesgo', 'OPA 2: Conceptos de no riesgo', '010', '013'),
  ('3', 'Conceptos de reserva vial', 'OPA 3: Conceptos de reserva vial', '010', '013'),
  ('4', 'Certificado de riesgos de predios', 'OPA 4: Certificado de riesgos de predios', '010', '013'),
  ('5', 'Concepto de norma urbanistica', 'OPA 5: Concepto de norma urbanistica', '010', '013'),
  ('6', 'Certificado de desafectación de Plusvalía, Participación en Plusvalia, Cancelación anotación en certificado de tradición', 'OPA 6: Certificado de desafectación de Plusvalía, Participación en Plusvalia, Cancelación anotación en certificado de tradición', '010', '013'),
  ('7', 'Inquietudes respecto al Plan de Ordenamiento Territorial (documentación, cartografía)', 'OPA 7: Inquietudes respecto al Plan de Ordenamiento Territorial (documentación, cartografía)', '010', '013'),
  ('8', 'Formulación, radicación o ajustes a proyectos de plan parcial', 'OPA 8: Formulación, radicación o ajustes a proyectos de plan parcial', '010', '013'),
  ('9', 'Legalización urbanística de asentamientos humanos', 'OPA 9: Legalización urbanística de asentamientos humanos', '010', '013'),
  ('10', 'Concepto- Certificado de paramentacion', 'OPA 10: Concepto- Certificado de paramentacion', '010', '013'),
  ('1', 'Permiso de rompimiento de vías o intervención de espacio publico', 'OPA 1: Permiso de rompimiento de vías o intervención de espacio publico', '010', '014'),
  ('2', 'Prorroga de intervención de espacio público', 'OPA 2: Prorroga de intervención de espacio público', '010', '014'),
  ('3', 'Todo lo relacionado con Licencias Urbanísticas', 'OPA 3: Todo lo relacionado con Licencias Urbanísticas', '010', '014'),
  ('4', 'Correción, revalidación y prorroga de licencia urbanistica', 'OPA 4: Correción, revalidación y prorroga de licencia urbanistica', '010', '014'),
  ('5', 'Radicación Documentos para el inicio de actividades de enajenación de ventas de vivienda', 'OPA 5: Radicación Documentos para el inicio de actividades de enajenación de ventas de vivienda', '010', '014'),
  ('6', 'Registro de enajenación', 'OPA 6: Registro de enajenación', '010', '014'),
  ('7', 'Matricula del arrendador', 'OPA 7: Matricula del arrendador', '010', '014'),
  ('8', 'Ajuste de cotas y areas', 'OPA 8: Ajuste de cotas y areas', '010', '014'),
  ('9', 'Aprobacion de planos de propiedad horizontal', 'OPA 9: Aprobacion de planos de propiedad horizontal', '010', '014'),
  ('10', 'Aprobacion de piscinas', 'OPA 10: Aprobacion de piscinas', '010', '014'),
  ('11', 'Autorizacion para el movimiento de tierras', 'OPA 11: Autorizacion para el movimiento de tierras', '010', '014'),
  ('12', 'Copia certificada de planos (del 2015 en adelante)', 'OPA 12: Copia certificada de planos (del 2015 en adelante)', '010', '014'),
  ('13', 'Licencia de localizacion de equipamiento de espacio publico', 'OPA 13: Licencia de localizacion de equipamiento de espacio publico', '010', '014'),
  ('14', 'Cancelación matricula del arrendador', 'OPA 14: Cancelación matricula del arrendador', '010', '014'),
  ('15', 'Control de constructoras e inmobiliarias', 'OPA 15: Control de constructoras e inmobiliarias', '010', '014'),
  ('16', 'Estados Financieros constructoras', 'OPA 16: Estados Financieros constructoras', '010', '014'),
  ('17', 'Correción, revalidación y prorroga de licencia urbanistica', 'OPA 17: Correción, revalidación y prorroga de licencia urbanistica', '010', '014'),
  ('18', 'Constancia ejecutoria (2024 en adelante)', 'OPA 18: Constancia ejecutoria (2024 en adelante)', '010', '014'),
  ('1', 'Todo lo concerniente con servicios públicos domiciliarios (energía eléctrica, telefonía pública básica conmutada, telefonía móvil rural, y distribución de gas combustible ley 142)', 'OPA 1: Todo lo concerniente con servicios públicos domiciliarios (energía eléctrica, telefonía pública básica conmutada, telefonía móvil rural, y distribución de gas combustible ley 142)', '010', '015'),
  ('2', 'Normas, regulacion  del centro de acopio y residuos aprovechables', 'OPA 2: Normas, regulacion  del centro de acopio y residuos aprovechables', '010', '015'),
  ('3', 'Traslado a empresas de energía de casos de arboles que se cruzan con red electrica y generan riesgo', 'OPA 3: Traslado a empresas de energía de casos de arboles que se cruzan con red electrica y generan riesgo', '010', '015'),
  ('4', 'Alumbrado publico  (Remitir facturas de alumbrado público)', 'OPA 4: Alumbrado publico  (Remitir facturas de alumbrado público)', '010', '015'),
  ('5', 'Solicitudes de paz y salvo de los permisos de intervención. Estos deberán radicarse como derecho de petición.', 'OPA 5: Solicitudes de paz y salvo de los permisos de intervención. Estos deberán radicarse como derecho de petición.', '010', '015'),
  ('6', 'Infraestructura para antenas de telecomunicaciones: Estos deben radicarse como Autorización como infraestructura de Telecomunicaciones.', 'OPA 6: Infraestructura para antenas de telecomunicaciones: Estos deben radicarse como Autorización como infraestructura de Telecomunicaciones.', '010', '015'),
  ('1', 'Comunicaciones de los sindicatos', 'OPA 1: Comunicaciones de los sindicatos', '020', '020'),
  ('1', 'Empleo publico', 'OPA 1: Empleo publico', '020', '021'),
  ('2', 'Cargas laborales', 'OPA 2: Cargas laborales', '020', '021'),
  ('3', 'Empleos provisionales', 'OPA 3: Empleos provisionales', '020', '021'),
  ('4', 'Listas de elegibles, concursos de meritos', 'OPA 4: Listas de elegibles, concursos de meritos', '020', '021'),
  ('5', 'Pre pensionados', 'OPA 5: Pre pensionados', '020', '021'),
  ('6', 'Calamidad de funcionario', 'OPA 6: Calamidad de funcionario', '020', '021'),
  ('7', 'Renuncia de funcionarios', 'OPA 7: Renuncia de funcionarios', '020', '021'),
  ('8', 'Horario flexible, teletrabajo, trabajo en casa,', 'OPA 8: Horario flexible, teletrabajo, trabajo en casa,', '020', '021'),
  ('9', 'Manual de funciones', 'OPA 9: Manual de funciones', '020', '021'),
  ('10', 'Escalas salariales - empleados de la alcaldía, Planta de personal de la alcaldía', 'OPA 10: Escalas salariales - empleados de la alcaldía, Planta de personal de la alcaldía', '020', '021'),
  ('11', 'Competencias de los empleados de la alcaldía', 'OPA 11: Competencias de los empleados de la alcaldía', '020', '021'),
  ('12', 'Inducción y capacitación de los empleados de la alcaldía', 'OPA 12: Inducción y capacitación de los empleados de la alcaldía', '020', '021')
) AS v(code, name, description, dep_code, subdep_code)
JOIN dependencies d ON d.code = v.dep_code
LEFT JOIN subdependencies s ON s.dependency_id = d.id AND s.code = v.subdep_code
WHERE NOT EXISTS (
    SELECT 1 FROM opas o 
    WHERE o.code = v.code 
    AND o.dependency_id = d.id 
    AND (o.subdependency_id = s.id OR (o.subdependency_id IS NULL AND s.id IS NULL))
);


-- LOTE DE 50 OPAs
INSERT INTO opas (code, name, description, dependency_id, subdependency_id, is_active)
SELECT v.code, v.name, v.description, d.id, s.id, true
FROM (VALUES
  ('13', 'Bienestar social de los empleados de la alcaldía', 'OPA 13: Bienestar social de los empleados de la alcaldía', '020', '021'),
  ('14', 'Encargos de funcionarios, traslados de funcionarios', 'OPA 14: Encargos de funcionarios, traslados de funcionarios', '020', '021'),
  ('15', 'Licencias de funcionarios, permisos de funcionarios, formato de ausencias de funcionarios', 'OPA 15: Licencias de funcionarios, permisos de funcionarios, formato de ausencias de funcionarios', '020', '021'),
  ('16', 'Documentos hojas de vida', 'OPA 16: Documentos hojas de vida', '020', '021'),
  ('17', 'Historia laboral', 'OPA 17: Historia laboral', '020', '021'),
  ('18', 'Solicitud de certificación laboral', 'OPA 18: Solicitud de certificación laboral', '020', '021'),
  ('19', 'Apoyos educativos para funcionarios', 'OPA 19: Apoyos educativos para funcionarios', '020', '021'),
  ('20', 'Salud y seguridad en el trabajo para funcionarios,', 'OPA 20: Salud y seguridad en el trabajo para funcionarios,', '020', '021'),
  ('21', 'Brigadistas', 'OPA 21: Brigadistas', '020', '021'),
  ('22', 'Dotación de ley para funcionarios de la alcaldia', 'OPA 22: Dotación de ley para funcionarios de la alcaldia', '020', '021'),
  ('23', 'Exámenes ocupacionales', 'OPA 23: Exámenes ocupacionales', '020', '021'),
  ('24', 'Recomendaciones medico laborales,  perdida de capacidad laboral', 'OPA 24: Recomendaciones medico laborales,  perdida de capacidad laboral', '020', '021'),
  ('25', 'Solicitud de cesantías', 'OPA 25: Solicitud de cesantías', '020', '021'),
  ('26', 'Incapacidades de funcionarios( MAYOR A 3 DIAS ES NOVEDADES DE NOMINA)(INFERIOR A 1 DIA SITUACIONES ADMINISTRATIVAS)', 'OPA 26: Incapacidades de funcionarios( MAYOR A 3 DIAS ES NOVEDADES DE NOMINA)(INFERIOR A 1 DIA SITUACIONES ADMINISTRATIVAS)', '020', '021'),
  ('27', 'Prestaciones sociales de funcionarios públicos', 'OPA 27: Prestaciones sociales de funcionarios públicos', '020', '021'),
  ('28', 'Cuotas partes pensionales', 'OPA 28: Cuotas partes pensionales', '020', '021'),
  ('29', 'Solicitud de vacaciones, indemnización de vacaciones, suspensión de vacaciones', 'OPA 29: Solicitud de vacaciones, indemnización de vacaciones, suspensión de vacaciones', '020', '021'),
  ('30', 'Entrega de citación jurados de votación', 'OPA 30: Entrega de citación jurados de votación', '020', '021'),
  ('31', 'Solictud cambio de extinguidores', 'OPA 31: Solictud cambio de extinguidores', '020', '021'),
  ('32', 'Novedades de ingreso o retiro de sindicatos', 'OPA 32: Novedades de ingreso o retiro de sindicatos', '020', '021'),
  ('33', 'Certificados CETIL', 'OPA 33: Certificados CETIL', '020', '021'),
  ('34', 'Comité de convivencia laboral', 'OPA 34: Comité de convivencia laboral', '020', '021'),
  ('35', 'Compensatorio de un día', 'OPA 35: Compensatorio de un día', '020', '021'),
  ('36', 'Evaluación de desempeño empleados de la alcaldía', 'OPA 36: Evaluación de desempeño empleados de la alcaldía', '020', '021'),
  ('37', 'Solicitud de pasantia (estudiantes) independientemente de la dependencia, practicas universitarias, tecnicas o tecnologicas', 'OPA 37: Solicitud de pasantia (estudiantes) independientemente de la dependencia, practicas universitarias, tecnicas o tecnologicas', '020', '021'),
  ('38', 'Notificación accidente de trabajo, accidente de trabajo', 'OPA 38: Notificación accidente de trabajo, accidente de trabajo', '020', '021'),
  ('39', 'Novedades de nómina, libranzas, descuentos de nomina, aclaraciones', 'OPA 39: Novedades de nómina, libranzas, descuentos de nomina, aclaraciones', '020', '021'),
  ('40', 'Certificado de existencia y dependencia economica para el beneficio tributario', 'OPA 40: Certificado de existencia y dependencia economica para el beneficio tributario', '020', '021'),
  ('41', 'Solictud de información para prácticas - pasantias', 'OPA 41: Solictud de información para prácticas - pasantias', '020', '021'),
  ('1', 'Plan anual de adquisiciones', 'OPA 1: Plan anual de adquisiciones', '020', '022'),
  ('2', 'Manejo de inventarios de la alcaldia', 'OPA 2: Manejo de inventarios de la alcaldia', '020', '022'),
  ('3', 'Almacén', 'OPA 3: Almacén', '020', '022'),
  ('4', 'Bienes de consumo', 'OPA 4: Bienes de consumo', '020', '022'),
  ('5', 'Papelería', 'OPA 5: Papelería', '020', '022'),
  ('6', 'Aseguramiento de bienes - pólizas', 'OPA 6: Aseguramiento de bienes - pólizas', '020', '022'),
  ('7', 'Comité de bajas', 'OPA 7: Comité de bajas', '020', '022'),
  ('8', 'Parque automotor', 'OPA 8: Parque automotor', '020', '022'),
  ('9', 'Servicios Públicos', 'OPA 9: Servicios Públicos', '020', '022'),
  ('10', 'Aseo', 'OPA 10: Aseo', '020', '022'),
  ('11', 'Vigilancia', 'OPA 11: Vigilancia', '020', '022'),
  ('12', 'Guardas de seguridad', 'OPA 12: Guardas de seguridad', '020', '022'),
  ('13', 'Producción documental, gestión documental', 'OPA 13: Producción documental, gestión documental', '020', '022'),
  ('14', 'Conservación y administración de archivos', 'OPA 14: Conservación y administración de archivos', '020', '022'),
  ('15', 'Solicitud de copias de archivos (contratos', 'OPA 15: Solicitud de copias de archivos (contratos', '020', '022'),
  ('16', 'Conductores', 'OPA 16: Conductores', '020', '022'),
  ('17', 'Copia de planos (anterior a 2015)', 'OPA 17: Copia de planos (anterior a 2015)', '020', '022'),
  ('18', 'Solicitud de vehículos oficiales (buses, camionetas, camiones, busetas, minivan), servicios de transporte', 'OPA 18: Solicitud de vehículos oficiales (buses, camionetas, camiones, busetas, minivan), servicios de transporte', '020', '022'),
  ('19', 'Perifoneo', 'OPA 19: Perifoneo', '020', '022'),
  ('20', 'Centro administrativo municipal - CAM', 'OPA 20: Centro administrativo municipal - CAM', '020', '022'),
  ('21', 'Constancia ejecutoria (214 hacias atras)', 'OPA 21: Constancia ejecutoria (214 hacias atras)', '020', '022')
) AS v(code, name, description, dep_code, subdep_code)
JOIN dependencies d ON d.code = v.dep_code
LEFT JOIN subdependencies s ON s.dependency_id = d.id AND s.code = v.subdep_code
WHERE NOT EXISTS (
    SELECT 1 FROM opas o 
    WHERE o.code = v.code 
    AND o.dependency_id = d.id 
    AND (o.subdependency_id = s.id OR (o.subdependency_id IS NULL AND s.id IS NULL))
);


-- LOTE DE 50 OPAs
INSERT INTO opas (code, name, description, dependency_id, subdependency_id, is_active)
SELECT v.code, v.name, v.description, d.id, s.id, true
FROM (VALUES
  ('1', 'Canales de atención', 'OPA 1: Canales de atención', '020', '023'),
  ('2', 'Atención telefónica', 'OPA 2: Atención telefónica', '020', '023'),
  ('3', 'Atención virtual', 'OPA 3: Atención virtual', '020', '023'),
  ('4', 'Atención presencial', 'OPA 4: Atención presencial', '020', '023'),
  ('5', 'Punto de atención y orientación PACO', 'OPA 5: Punto de atención y orientación PACO', '020', '023'),
  ('6', 'PQRSDF', 'OPA 6: PQRSDF', '020', '023'),
  ('7', 'Trazabilidad de correspondencia', 'OPA 7: Trazabilidad de correspondencia', '020', '023'),
  ('8', 'Racionalización de trámites', 'OPA 8: Racionalización de trámites', '020', '023'),
  ('9', 'Protocolo de atención al ciudadano', 'OPA 9: Protocolo de atención al ciudadano', '020', '023'),
  ('10', 'boletas', 'OPA 10: boletas', '020', '023'),
  ('11', 'Citaciones', 'OPA 11: Citaciones', '020', '023'),
  ('12', 'Servicios de mensajería, correo certificado', 'OPA 12: Servicios de mensajería, correo certificado', '020', '023'),
  ('13', 'sistema de radicación', 'OPA 13: sistema de radicación', '020', '023'),
  ('1', 'Investigaciones procesos disciplinarios', 'OPA 1: Investigaciones procesos disciplinarios', '020', '024'),
  ('2', 'Quejas de funcionarios o ex funcionarios', 'OPA 2: Quejas de funcionarios o ex funcionarios', '020', '024'),
  ('3', 'Remisión de expedientes', 'OPA 3: Remisión de expedientes', '020', '024'),
  ('4', 'Solicitud de expediente', 'OPA 4: Solicitud de expediente', '020', '024'),
  ('5', 'Solicitud de pruebas', 'OPA 5: Solicitud de pruebas', '020', '024'),
  ('1', 'Recuperación y defensa del espacio publico', 'OPA 1: Recuperación y defensa del espacio publico', '030', '030'),
  ('2', 'Ventas ambulantes', 'OPA 2: Ventas ambulantes', '030', '030'),
  ('3', 'Saneamiento de bienes constitutivos como patrimonio inmobiliario', 'OPA 3: Saneamiento de bienes constitutivos como patrimonio inmobiliario', '030', '030'),
  ('4', 'Programación de gestión de riesgos', 'OPA 4: Programación de gestión de riesgos', '030', '030'),
  ('5', 'Cultura de prevención de emergencias y desastres', 'OPA 5: Cultura de prevención de emergencias y desastres', '030', '030'),
  ('6', 'Sistemas de alerta y monitoreo de fenomenos naturales', 'OPA 6: Sistemas de alerta y monitoreo de fenomenos naturales', '030', '030'),
  ('7', 'Mitigación de riesgos', 'OPA 7: Mitigación de riesgos', '030', '030'),
  ('8', 'Red de comunicaciones de emergencias', 'OPA 8: Red de comunicaciones de emergencias', '030', '030'),
  ('9', 'Protección al consumidor', 'OPA 9: Protección al consumidor', '030', '030'),
  ('10', 'Control de precios, pesas y medidas', 'OPA 10: Control de precios, pesas y medidas', '030', '030'),
  ('11', 'Rifas juegos y espectaculos', 'OPA 11: Rifas juegos y espectaculos', '030', '030'),
  ('12', 'Supervisión delegado sorteos y concursos', 'OPA 12: Supervisión delegado sorteos y concursos', '030', '030'),
  ('13', 'Coordinación con el concejo municipal', 'OPA 13: Coordinación con el concejo municipal', '030', '030'),
  ('14', 'Proyectos de integración regional', 'OPA 14: Proyectos de integración regional', '030', '030'),
  ('15', 'Comité de conocimientos y protección de riesgos para eventos masivos y no masivos', 'OPA 15: Comité de conocimientos y protección de riesgos para eventos masivos y no masivos', '030', '030'),
  ('16', 'Restitución de inmueble', 'OPA 16: Restitución de inmueble', '030', '030'),
  ('17', 'Permiso para la realización de espectáculos públicos', 'OPA 17: Permiso para la realización de espectáculos públicos', '030', '030'),
  ('18', 'Todas las peticiones enviadas por la policia nacional se remiten directamente a gobierno independientemente del tema', 'OPA 18: Todas las peticiones enviadas por la policia nacional se remiten directamente a gobierno independientemente del tema', '030', '030'),
  ('19', 'Temas realcionados con el INPEC o carceles del municipio (PPL)', 'OPA 19: Temas realcionados con el INPEC o carceles del municipio (PPL)', '030', '030'),
  ('20', 'Estacion de policia de Chia', 'OPA 20: Estacion de policia de Chia', '030', '030'),
  ('1', 'Prevención de delitos, contravenciones, problemas de convivencia y seguridad ciudadana', 'OPA 1: Prevención de delitos, contravenciones, problemas de convivencia y seguridad ciudadana', '030', '031'),
  ('2', 'Estudios sobre desplazamiento por violencia en el municipio', 'OPA 2: Estudios sobre desplazamiento por violencia en el municipio', '030', '031'),
  ('3', 'Observatorio de convivencia y seguridad', 'OPA 3: Observatorio de convivencia y seguridad', '030', '031'),
  ('4', 'Programas para la reducción de la oferta de sustancias psicoactivas', 'OPA 4: Programas para la reducción de la oferta de sustancias psicoactivas', '030', '031'),
  ('5', 'Diseño de planes de seguridad con Policía', 'OPA 5: Diseño de planes de seguridad con Policía', '030', '031'),
  ('6', 'Estrategias para el mantenimiento y restablecimiento del  orden publico', 'OPA 6: Estrategias para el mantenimiento y restablecimiento del  orden publico', '030', '031'),
  ('7', 'Coordinación procesos electorales con la Registraduría', 'OPA 7: Coordinación procesos electorales con la Registraduría', '030', '031'),
  ('8', 'Información de seguridad ciudadana', 'OPA 8: Información de seguridad ciudadana', '030', '031'),
  ('9', 'Solicitud revisión de cámaras de seguridad, copia de videos de seguridad de las camaras publicas', 'OPA 9: Solicitud revisión de cámaras de seguridad, copia de videos de seguridad de las camaras publicas', '030', '031'),
  ('10', 'Paneles de control - alarmas de seguridad - botones de pánico', 'OPA 10: Paneles de control - alarmas de seguridad - botones de pánico', '030', '031'),
  ('11', 'Línea de emergencias 123', 'OPA 11: Línea de emergencias 123', '030', '031'),
  ('12', 'Temas relacionados con informes y plataforma SYGOB - CIPRET,  Funcionaria Claudia Córdoba', 'OPA 12: Temas relacionados con informes y plataforma SYGOB - CIPRET,  Funcionaria Claudia Córdoba', '030', '031')
) AS v(code, name, description, dep_code, subdep_code)
JOIN dependencies d ON d.code = v.dep_code
LEFT JOIN subdependencies s ON s.dependency_id = d.id AND s.code = v.subdep_code
WHERE NOT EXISTS (
    SELECT 1 FROM opas o 
    WHERE o.code = v.code 
    AND o.dependency_id = d.id 
    AND (o.subdependency_id = s.id OR (o.subdependency_id IS NULL AND s.id IS NULL))
);


-- LOTE DE 50 OPAs
INSERT INTO opas (code, name, description, dependency_id, subdependency_id, is_active)
SELECT v.code, v.name, v.description, d.id, s.id, true
FROM (VALUES
  ('13', 'Migrantes, enlace Migracion Colombia', 'OPA 13: Migrantes, enlace Migracion Colombia', '030', '031'),
  ('14', 'Solicitud de Evaluación del Estudio de Nivel de Riesgo de la persona en relación de hechos que sean de conocimiento en lo
que concierne a amenazas o situaciones de riesgo', 'OPA 14: Solicitud de Evaluación del Estudio de Nivel de Riesgo de la persona en relación de hechos que sean de conocimiento en lo
que concierne a amenazas o situaciones de riesgo', '030', '031'),
  ('1', 'Validada con la dependencia 28/10/2023', 'OPA 1: Validada con la dependencia 28/10/2023', '030', '032'),
  ('2', 'Protección conocimientos tradicionales', 'OPA 2: Protección conocimientos tradicionales', '030', '032'),
  ('3', 'Comunidades étnicas, comunidades indígenas, minorías étnicas', 'OPA 3: Comunidades étnicas, comunidades indígenas, minorías étnicas', '030', '032'),
  ('4', 'Censo de población comunidades', 'OPA 4: Censo de población comunidades', '030', '032'),
  ('5', 'Resguardos indígenas', 'OPA 5: Resguardos indígenas', '030', '032'),
  ('6', 'Autoridades tradicionales', 'OPA 6: Autoridades tradicionales', '030', '032'),
  ('7', 'Resolución de conflictos (comunidades indígenas y minorías étnicas)  las minorías étnicas puden también asignarse a la DDRC', 'OPA 7: Resolución de conflictos (comunidades indígenas y minorías étnicas)  las minorías étnicas puden también asignarse a la DDRC', '030', '032'),
  ('8', 'Libertad de culto, libertad religiosa, libertad de conciencia', 'OPA 8: Libertad de culto, libertad religiosa, libertad de conciencia', '030', '032'),
  ('9', 'Protección población LGTBI (Lesbiana, Gay, Transexual, Bisexual e Intersexual)', 'OPA 9: Protección población LGTBI (Lesbiana, Gay, Transexual, Bisexual e Intersexual)', '030', '032'),
  ('10', 'Censo de establecimiento de culto religioso', 'OPA 10: Censo de establecimiento de culto religioso', '030', '032'),
  ('11', 'Enlace victimas de conflicto armado', 'OPA 11: Enlace victimas de conflicto armado', '030', '032'),
  ('12', 'Población desplazada por la violencia', 'OPA 12: Población desplazada por la violencia', '030', '032'),
  ('1', 'Validada con la dependencia 28/10/2023', 'OPA 1: Validada con la dependencia 28/10/2023', '030', '033'),
  ('2', 'Políticas para promocionar los derechos humanos', 'OPA 2: Políticas para promocionar los derechos humanos', '030', '033'),
  ('3', 'Acceso al ciudadano a la justicia', 'OPA 3: Acceso al ciudadano a la justicia', '030', '033'),
  ('4', 'Garantía de derechos y libertades individuales', 'OPA 4: Garantía de derechos y libertades individuales', '030', '033'),
  ('5', 'Casa de justicia', 'OPA 5: Casa de justicia', '030', '033'),
  ('6', 'Resolución de conflictos en ámbitos escolares y comunitarios', 'OPA 6: Resolución de conflictos en ámbitos escolares y comunitarios', '030', '033'),
  ('7', 'Sistema de información gestión de la justicia', 'OPA 7: Sistema de información gestión de la justicia', '030', '033'),
  ('8', 'Inspecciones de policía', 'OPA 8: Inspecciones de policía', '030', '033'),
  ('9', 'Asuntos policivos', 'OPA 9: Asuntos policivos', '030', '033'),
  ('10', 'Rechazo a comparendos policivos', 'OPA 10: Rechazo a comparendos policivos', '030', '033'),
  ('11', 'Contravenciones', 'OPA 11: Contravenciones', '030', '033'),
  ('12', 'Comisarias de familia', 'OPA 12: Comisarias de familia', '030', '033'),
  ('13', 'Violencia intrafamiliar', 'OPA 13: Violencia intrafamiliar', '030', '033'),
  ('14', 'Protección de niños, niñas y adolescentes', 'OPA 14: Protección de niños, niñas y adolescentes', '030', '033'),
  ('15', 'Medidas de protección', 'OPA 15: Medidas de protección', '030', '033'),
  ('16', 'Orientación jurídica', 'OPA 16: Orientación jurídica', '030', '033'),
  ('17', 'Asistencia psicológica y social para la familia y menores a través de comisarias de familia', 'OPA 17: Asistencia psicológica y social para la familia y menores a través de comisarias de familia', '030', '033'),
  ('18', 'Código de infancia y adolescencia', 'OPA 18: Código de infancia y adolescencia', '030', '033'),
  ('19', 'Comportamientos contrarios a la convivencia: de seguridad, tranquilidad, ambiente y recursos naturales, derecho de reunión, protección a los bienes y privacidad, actividad económica, urbanismo, espacio público y libertad de circulación.', 'OPA 19: Comportamientos contrarios a la convivencia: de seguridad, tranquilidad, ambiente y recursos naturales, derecho de reunión, protección a los bienes y privacidad, actividad económica, urbanismo, espacio público y libertad de circulación.', '030', '033'),
  ('20', 'Autorización de ocupación de inmuebles', 'OPA 20: Autorización de ocupación de inmuebles', '030', '033'),
  ('21', 'Reporte de registro de defunción pasadas 48 horas', 'OPA 21: Reporte de registro de defunción pasadas 48 horas', '030', '033'),
  ('22', 'Entrega de inmuebles (a través de despachos comisorios)', 'OPA 22: Entrega de inmuebles (a través de despachos comisorios)', '030', '033'),
  ('23', 'SIM Sistema de Información Misional - ICBF', 'OPA 23: SIM Sistema de Información Misional - ICBF', '030', '033'),
  ('24', 'SRD Solicitud de restitución de derechos', 'OPA 24: SRD Solicitud de restitución de derechos', '030', '033'),
  ('25', 'PARD Proceso administrativo de restablecimiento de derechos', 'OPA 25: PARD Proceso administrativo de restablecimiento de derechos', '030', '033'),
  ('26', 'Ejecutar la orden de restitución, en casos de tierras comunales (A TRAVES DE DESPACHOS COMISORIOS)', 'OPA 26: Ejecutar la orden de restitución, en casos de tierras comunales (A TRAVES DE DESPACHOS COMISORIOS)', '030', '033'),
  ('27', 'Maltrato animal', 'OPA 27: Maltrato animal', '030', '033'),
  ('28', 'Los propietarios de los predios sean requeridos persuasivamente para que de manera voluntaria , realicen la poda de los árboles y en caso de renuencia a dicha recomendación, estos asuntos serán remitidos a esta Dirección ,para que sean asignados a la Inspección de Policía', 'OPA 28: Los propietarios de los predios sean requeridos persuasivamente para que de manera voluntaria , realicen la poda de los árboles y en caso de renuencia a dicha recomendación, estos asuntos serán remitidos a esta Dirección ,para que sean asignados a la Inspección de Policía', '030', '033'),
  ('29', 'Programa comunitario de actividad pedagogica - para reducir costo de multas', 'OPA 29: Programa comunitario de actividad pedagogica - para reducir costo de multas', '030', '033'),
  ('30', 'Secuestro de bien inmuebles (A TRAVES DE DESPACHOS COMISORIOS)', 'OPA 30: Secuestro de bien inmuebles (A TRAVES DE DESPACHOS COMISORIOS)', '030', '033'),
  ('31', 'Secuestro de la cuota parte del inmueble  (A TRAVES DE DESPACHOS COMISORIOS)', 'OPA 31: Secuestro de la cuota parte del inmueble  (A TRAVES DE DESPACHOS COMISORIOS)', '030', '033'),
  ('32', 'Daños al espacio publico (asfalto por obras)', 'OPA 32: Daños al espacio publico (asfalto por obras)', '030', '033'),
  ('33', 'Situaciones presentadas en cuanto a compra de proyecto de vivienda por incumplimientos siempre y cuando especifique que va con copia para la admnistracion municipal', 'OPA 33: Situaciones presentadas en cuanto a compra de proyecto de vivienda por incumplimientos siempre y cuando especifique que va con copia para la admnistracion municipal', '030', '033'),
  ('34', 'Abandono adulto mayor y vulnerabilidad(Derecho de petición)', 'OPA 34: Abandono adulto mayor y vulnerabilidad(Derecho de petición)', '030', '033'),
  ('35', 'Amparo de pobreza', 'OPA 35: Amparo de pobreza', '030', '033'),
  ('36', 'Actualización de claves del Sistema de Registro Nacional de Medidas Correctivas - RNMC', 'OPA 36: Actualización de claves del Sistema de Registro Nacional de Medidas Correctivas - RNMC', '030', '033')
) AS v(code, name, description, dep_code, subdep_code)
JOIN dependencies d ON d.code = v.dep_code
LEFT JOIN subdependencies s ON s.dependency_id = d.id AND s.code = v.subdep_code
WHERE NOT EXISTS (
    SELECT 1 FROM opas o 
    WHERE o.code = v.code 
    AND o.dependency_id = d.id 
    AND (o.subdependency_id = s.id OR (o.subdependency_id IS NULL AND s.id IS NULL))
);


-- LOTE DE 50 OPAs
INSERT INTO opas (code, name, description, dependency_id, subdependency_id, is_active)
SELECT v.code, v.name, v.description, d.id, s.id, true
FROM (VALUES
  ('37', 'Temas relacionados con informes y plataforma SYGOB - CIPRAT, Funcionaria Claudia Córdoba', 'OPA 37: Temas relacionados con informes y plataforma SYGOB - CIPRAT, Funcionaria Claudia Córdoba', '030', '033'),
  ('1', 'Temas relacionados con informes y plataforma SYGOB - CIPRAT, Funcionaria Claudia Córdoba', 'OPA 1: Temas relacionados con informes y plataforma SYGOB - CIPRAT, Funcionaria Claudia Córdoba', '030', '330'),
  ('1', 'Temas relacionados con informes y plataforma SYGOB - CIPRAT, Funcionaria Claudia Córdoba', 'OPA 1: Temas relacionados con informes y plataforma SYGOB - CIPRAT, Funcionaria Claudia Córdoba', '030', '331'),
  ('1', 'Temas relacionados con informes y plataforma SYGOB - CIPRAT, Funcionaria Claudia Córdoba', 'OPA 1: Temas relacionados con informes y plataforma SYGOB - CIPRAT, Funcionaria Claudia Córdoba', '030', '332'),
  ('1', 'Temas relacionados con informes y plataforma SYGOB - CIPRAT, Funcionaria Claudia Córdoba', 'OPA 1: Temas relacionados con informes y plataforma SYGOB - CIPRAT, Funcionaria Claudia Córdoba', '030', '333'),
  ('1', 'Temas relacionados con informes y plataforma SYGOB - CIPRAT, Funcionaria Claudia Córdoba', 'OPA 1: Temas relacionados con informes y plataforma SYGOB - CIPRAT, Funcionaria Claudia Córdoba', '030', '334'),
  ('1', 'Temas relacionados con informes y plataforma SYGOB - CIPRAT, Funcionaria Claudia Córdoba', 'OPA 1: Temas relacionados con informes y plataforma SYGOB - CIPRAT, Funcionaria Claudia Córdoba', '030', '335'),
  ('1', 'Temas relacionados con informes y plataforma SYGOB - CIPRAT, Funcionaria Claudia Córdoba', 'OPA 1: Temas relacionados con informes y plataforma SYGOB - CIPRAT, Funcionaria Claudia Córdoba', '030', '336'),
  ('1', 'Temas relacionados con informes y plataforma SYGOB - CIPRAT, Funcionaria Claudia Córdoba', 'OPA 1: Temas relacionados con informes y plataforma SYGOB - CIPRAT, Funcionaria Claudia Córdoba', '030', '337'),
  ('1', 'Temas relacionados con informes y plataforma SYGOB - CIPRAT, Funcionaria Claudia Córdoba', 'OPA 1: Temas relacionados con informes y plataforma SYGOB - CIPRAT, Funcionaria Claudia Córdoba', '030', '338'),
  ('1', 'Temas relacionados con informes y plataforma SYGOB - CIPRAT, Funcionaria Claudia Córdoba', 'OPA 1: Temas relacionados con informes y plataforma SYGOB - CIPRAT, Funcionaria Claudia Córdoba', '030', '339'),
  ('1', 'Racionalización de gasto', 'OPA 1: Racionalización de gasto', '040', '040'),
  ('2', 'Financiación de programas de inversión', 'OPA 2: Financiación de programas de inversión', '040', '040'),
  ('3', 'Estado de cuenta deudores', 'OPA 3: Estado de cuenta deudores', '040', '040'),
  ('4', 'Oficios DIAN', 'OPA 4: Oficios DIAN', '040', '040'),
  ('5', 'Oficios Contaduria General', 'OPA 5: Oficios Contaduria General', '040', '040'),
  ('6', 'Solicitud de avaluo catastral', 'OPA 6: Solicitud de avaluo catastral', '040', '040'),
  ('7', 'Remisión de sucesión', 'OPA 7: Remisión de sucesión', '040', '040'),
  ('8', 'Exención., devolución y compensación impuesto predial', 'OPA 8: Exención., devolución y compensación impuesto predial', '040', '040'),
  ('9', 'Proceso cobro coactivo', 'OPA 9: Proceso cobro coactivo', '040', '040'),
  ('10', 'Remision de recibos de pago del impuestro predial que están vinculados a cobros coactivos.', 'OPA 10: Remision de recibos de pago del impuestro predial que están vinculados a cobros coactivos.', '040', '040'),
  ('11', 'Recurso de reconsideración y reposición impuesto predial', 'OPA 11: Recurso de reconsideración y reposición impuesto predial', '040', '040'),
  ('12', 'Acuerdos de pago impuesto predial', 'OPA 12: Acuerdos de pago impuesto predial', '040', '040'),
  ('13', 'Solictud prescripción cartera impuesto predial', 'OPA 13: Solictud prescripción cartera impuesto predial', '040', '040'),
  ('14', 'Peticiones generales internas y externas sobre el impuesto predial', 'OPA 14: Peticiones generales internas y externas sobre el impuesto predial', '040', '040'),
  ('15', 'Reporte Categorías CUIPO y Sistema General de Regalías (SGR) Vigencia 2023 - Plataforma CHIP.', 'OPA 15: Reporte Categorías CUIPO y Sistema General de Regalías (SGR) Vigencia 2023 - Plataforma CHIP.', '040', '040'),
  ('16', 'Boletín de Deudores Morosos del Estado - BDME', 'OPA 16: Boletín de Deudores Morosos del Estado - BDME', '040', '040'),
  ('17', 'Facturas de Impuesto predial, cuando son de Cobro Coactivo o procesos', 'OPA 17: Facturas de Impuesto predial, cuando son de Cobro Coactivo o procesos', '040', '040'),
  ('18', 'Mutacion de inmuebles', 'OPA 18: Mutacion de inmuebles', '040', '040'),
  ('1', 'Solicitud de certificado de libertad (VUR)', 'OPA 1: Solicitud de certificado de libertad (VUR)', '040', '041'),
  ('2', 'Certificado de paz y salvo impuestos hacienda
A partir del mes de agosto de 2023, las notarías podrán acceder a consultar la paz y salvo en línea a través de la ventanilla única de registro VUR. Por lo cual no es necesario acercarse a la Secretaría de hacienda; a excepción de aquellos que tengan anotación de plusvalía en el certificado de tradición y libertad o en caso de que VUR no esté en funcionamiento podrá realizar la solicitud a la Secretaría de Hacienda".  Solicitudes por parte de los ciu', 'OPA 2: Certificado de paz y salvo impuestos hacienda
A partir del mes de agosto de 2023, las notarías podrán acceder a consultar la paz y salvo en línea a través de la ventanilla única de registro VUR. Por lo cual no es necesario acercarse a la Secretaría de hacienda; a excepción de aquellos que tengan anotación de plusvalía en el certificado de tradición y libertad o en caso de que VUR no esté en funcionamiento podrá realizar la solicitud a la Secretaría de Hacienda".  Solicitudes por parte de los ciudadanos de Paz y Salvos y/o valorización enviar "AL CORREO DE RENTAS"   como  "ORIENTACIÓN E INFORMACIÓN"', '040', '041'),
  ('3', 'Exencion de impuestos (Predial e Industria y Comercio)', 'OPA 3: Exencion de impuestos (Predial e Industria y Comercio)', '040', '041'),
  ('4', 'Devolución y/o compensación de pagos en exceso y pagos de lo no debido', 'OPA 4: Devolución y/o compensación de pagos en exceso y pagos de lo no debido', '040', '041'),
  ('5', 'Solicitud de Avaluo o de información catastral por parte de Inspecciones', 'OPA 5: Solicitud de Avaluo o de información catastral por parte de Inspecciones', '040', '041'),
  ('6', 'Sucesiones de Notaria', 'OPA 6: Sucesiones de Notaria', '040', '041'),
  ('7', 'Sobretasa municipal o distrital a la gasolina motor', 'OPA 7: Sobretasa municipal o distrital a la gasolina motor', '040', '041'),
  ('8', 'Corrección de errores e inconsistencias en declaraciones y recibos de pago', 'OPA 8: Corrección de errores e inconsistencias en declaraciones y recibos de pago', '040', '041'),
  ('9', 'Impuesto sobre el servicio de alumbrado público', 'OPA 9: Impuesto sobre el servicio de alumbrado público', '040', '041'),
  ('10', 'Pago voluntario anticipado', 'OPA 10: Pago voluntario anticipado', '040', '041'),
  ('11', 'Afectación de predios con cobro de plusvalía', 'OPA 11: Afectación de predios con cobro de plusvalía', '040', '041'),
  ('12', 'Incentivos por discapacidad', 'OPA 12: Incentivos por discapacidad', '040', '041'),
  ('13', 'Procesos de fiscalización', 'OPA 13: Procesos de fiscalización', '040', '041'),
  ('14', 'Quitar el anticipo del año siguiente', 'OPA 14: Quitar el anticipo del año siguiente', '040', '041'),
  ('15', 'Devoluciones', 'OPA 15: Devoluciones', '040', '041'),
  ('16', 'Exención del impuesto de espectáculos públicos', 'OPA 16: Exención del impuesto de espectáculos públicos', '040', '041'),
  ('17', 'Exención del impuesto de industria y comercio', 'OPA 17: Exención del impuesto de industria y comercio', '040', '041'),
  ('18', 'Impuesto de espectaculos publicos', 'OPA 18: Impuesto de espectaculos publicos', '040', '041'),
  ('19', 'Acuerdos de pago', 'OPA 19: Acuerdos de pago', '040', '041'),
  ('20', 'Facilidades de pago para los deudores de obligaciones', 'OPA 20: Facilidades de pago para los deudores de obligaciones', '040', '041'),
  ('21', 'INFORMACIÓN Y ORIENTACIÓN (CASI TODO ICA Y RETEICA)', 'OPA 21: INFORMACIÓN Y ORIENTACIÓN (CASI TODO ICA Y RETEICA)', '040', '041')
) AS v(code, name, description, dep_code, subdep_code)
JOIN dependencies d ON d.code = v.dep_code
LEFT JOIN subdependencies s ON s.dependency_id = d.id AND s.code = v.subdep_code
WHERE NOT EXISTS (
    SELECT 1 FROM opas o 
    WHERE o.code = v.code 
    AND o.dependency_id = d.id 
    AND (o.subdependency_id = s.id OR (o.subdependency_id IS NULL AND s.id IS NULL))
);


-- LOTE DE 50 OPAs
INSERT INTO opas (code, name, description, dependency_id, subdependency_id, is_active)
SELECT v.code, v.name, v.description, d.id, s.id, true
FROM (VALUES
  ('22', 'Claves de acceso', 'OPA 22: Claves de acceso', '040', '041'),
  ('23', 'Normatividad (vencimiento y calendario tributario)', 'OPA 23: Normatividad (vencimiento y calendario tributario)', '040', '041'),
  ('24', 'Presentación y liquidación de pago', 'OPA 24: Presentación y liquidación de pago', '040', '041'),
  ('25', 'Cambio de representante legal, cambio de contador, cambio de revisor fiscal, cambio de actividades, cambio de dirección, cambio de razón social, cambio de fecha de inicio de actividades, cambio de dígito de verificación', 'OPA 25: Cambio de representante legal, cambio de contador, cambio de revisor fiscal, cambio de actividades, cambio de dirección, cambio de razón social, cambio de fecha de inicio de actividades, cambio de dígito de verificación', '040', '041'),
  ('26', 'Requisitos para registrar un establecimiento', 'OPA 26: Requisitos para registrar un establecimiento', '040', '041'),
  ('27', 'Quitar avisos y tableros', 'OPA 27: Quitar avisos y tableros', '040', '041'),
  ('28', 'Presentar información exogena (Sólo se deben radicar los casos especiales de exógenas, como Derechos de Petición)', 'OPA 28: Presentar información exogena (Sólo se deben radicar los casos especiales de exógenas, como Derechos de Petición)', '040', '041'),
  ('29', 'Paz y Salvo - Estado de cuenta (Industria y comercio)', 'OPA 29: Paz y Salvo - Estado de cuenta (Industria y comercio)', '040', '041'),
  ('30', 'Estado del establecimiento (Activo o Inactivo)', 'OPA 30: Estado del establecimiento (Activo o Inactivo)', '040', '041'),
  ('31', 'Cerrar RIC (Cancelación de establecimiento)', 'OPA 31: Cerrar RIC (Cancelación de establecimiento)', '040', '041'),
  ('32', 'Certificación de registro (Qué datos hay de un establecimiento)', 'OPA 32: Certificación de registro (Qué datos hay de un establecimiento)', '040', '041'),
  ('33', 'Cómo inscribirse', 'OPA 33: Cómo inscribirse', '040', '041'),
  ('34', 'liquidación de impuestos', 'OPA 34: liquidación de impuestos', '040', '041'),
  ('35', 'cobro de impuestos', 'OPA 35: cobro de impuestos', '040', '041'),
  ('36', 'rentas municipales', 'OPA 36: rentas municipales', '040', '041'),
  ('37', 'tarifas tasas', 'OPA 37: tarifas tasas', '040', '041'),
  ('38', 'contribuciones', 'OPA 38: contribuciones', '040', '041'),
  ('39', 'evasión tributaria', 'OPA 39: evasión tributaria', '040', '041'),
  ('40', 'contribuyentes morosos', 'OPA 40: contribuyentes morosos', '040', '041'),
  ('41', 'politica fiscal', 'OPA 41: politica fiscal', '040', '041'),
  ('42', 'calendario tributario', 'OPA 42: calendario tributario', '040', '041'),
  ('43', 'descuentos de impuestos', 'OPA 43: descuentos de impuestos', '040', '041'),
  ('44', 'recibos de pago', 'OPA 44: recibos de pago', '040', '041'),
  ('45', 'Impuesto al degüello de ganado mayor', 'OPA 45: Impuesto al degüello de ganado mayor', '040', '041'),
  ('46', 'Impuesto de industria y comercio y su complementario de avisos y tableros', 'OPA 46: Impuesto de industria y comercio y su complementario de avisos y tableros', '040', '041'),
  ('47', 'Medios magneticos', 'OPA 47: Medios magneticos', '040', '041'),
  ('48', 'Solicitud liquidacion credito FOES', 'OPA 48: Solicitud liquidacion credito FOES', '040', '041'),
  ('49', 'Reorganizacion de sociedades', 'OPA 49: Reorganizacion de sociedades', '040', '041'),
  ('50', 'Recuperar credenciales de acceso', 'OPA 50: Recuperar credenciales de acceso', '040', '041'),
  ('51', 'Orientación e información presentación de exogenas', 'OPA 51: Orientación e información presentación de exogenas', '040', '041'),
  ('52', 'Facturas de Impuesto predial', 'OPA 52: Facturas de Impuesto predial', '040', '041'),
  ('53', 'Paz y salvos de Impuesto predial', 'OPA 53: Paz y salvos de Impuesto predial', '040', '041'),
  ('54', 'Actualización de Registro de Información Tributaria  RIT', 'OPA 54: Actualización de Registro de Información Tributaria  RIT', '040', '041'),
  ('1', 'presupuesto del municipio', 'OPA 1: presupuesto del municipio', '040', '042'),
  ('2', 'distribución de recursos', 'OPA 2: distribución de recursos', '040', '042'),
  ('3', 'apoyar presupuesto entidades descentralizadas', 'OPA 3: apoyar presupuesto entidades descentralizadas', '040', '042'),
  ('4', 'contabilidad del municipio', 'OPA 4: contabilidad del municipio', '040', '042'),
  ('5', 'consolidar contabilidad de las instituciones educativas', 'OPA 5: consolidar contabilidad de las instituciones educativas', '040', '042'),
  ('6', 'consolidar contabilidad de consejo y personería municipal', 'OPA 6: consolidar contabilidad de consejo y personería municipal', '040', '042'),
  ('7', 'estados financieros', 'OPA 7: estados financieros', '040', '042'),
  ('8', 'registro contable de inventarios', 'OPA 8: registro contable de inventarios', '040', '042'),
  ('9', 'reporte de riesgo - contingencias', 'OPA 9: reporte de riesgo - contingencias', '040', '042'),
  ('10', 'endeudamiento', 'OPA 10: endeudamiento', '040', '042'),
  ('11', 'inversiones del municipio', 'OPA 11: inversiones del municipio', '040', '042'),
  ('12', 'programa anualizado mensualizado de caja', 'OPA 12: programa anualizado mensualizado de caja', '040', '042'),
  ('13', 'cuentas por pagar', 'OPA 13: cuentas por pagar', '040', '042'),
  ('14', 'reservas', 'OPA 14: reservas', '040', '042'),
  ('15', 'sistema de información financiera', 'OPA 15: sistema de información financiera', '040', '042'),
  ('16', 'ingresos y gastos', 'OPA 16: ingresos y gastos', '040', '042'),
  ('17', 'marco fiscal de mediano plazo', 'OPA 17: marco fiscal de mediano plazo', '040', '042')
) AS v(code, name, description, dep_code, subdep_code)
JOIN dependencies d ON d.code = v.dep_code
LEFT JOIN subdependencies s ON s.dependency_id = d.id AND s.code = v.subdep_code
WHERE NOT EXISTS (
    SELECT 1 FROM opas o 
    WHERE o.code = v.code 
    AND o.dependency_id = d.id 
    AND (o.subdependency_id = s.id OR (o.subdependency_id IS NULL AND s.id IS NULL))
);


-- LOTE DE 50 OPAs
INSERT INTO opas (code, name, description, dependency_id, subdependency_id, is_active)
SELECT v.code, v.name, v.description, d.id, s.id, true
FROM (VALUES
  ('18', 'disponibilidad presupuestal CDP', 'OPA 18: disponibilidad presupuestal CDP', '040', '042'),
  ('19', 'FONPET', 'OPA 19: FONPET', '040', '042'),
  ('20', 'Formulario Unico Territorial FUT', 'OPA 20: Formulario Unico Territorial FUT', '040', '042'),
  ('21', 'impacto fiscal', 'OPA 21: impacto fiscal', '040', '042'),
  ('22', 'Nuevo!  Solicitud de Certificado de Retenciones de IVA, ICA, ESTAMPILLAS, entre otros,   de Personas Naturales o Juridicas  que haya celebrado contratos con el Municipio', 'OPA 22: Nuevo!  Solicitud de Certificado de Retenciones de IVA, ICA, ESTAMPILLAS, entre otros,   de Personas Naturales o Juridicas  que haya celebrado contratos con el Municipio', '040', '042'),
  ('1', 'recaudo', 'OPA 1: recaudo', '040', '043'),
  ('2', 'pagos', 'OPA 2: pagos', '040', '043'),
  ('3', 'cuentas bancarias', 'OPA 3: cuentas bancarias', '040', '043'),
  ('4', 'cheques', 'OPA 4: cheques', '040', '043'),
  ('5', 'giros', 'OPA 5: giros', '040', '043'),
  ('6', 'descuentos tributarios', 'OPA 6: descuentos tributarios', '040', '043'),
  ('7', 'leasing financiero', 'OPA 7: leasing financiero', '040', '043'),
  ('8', 'cobro coactivo', 'OPA 8: cobro coactivo', '040', '043'),
  ('9', 'cobro persuasivo', 'OPA 9: cobro persuasivo', '040', '043'),
  ('10', 'pago nómina de funcionarios', 'OPA 10: pago nómina de funcionarios', '040', '043'),
  ('11', 'descuentos a contratistas, Certificados de retencion a contratistas', 'OPA 11: descuentos a contratistas, Certificados de retencion a contratistas', '040', '043'),
  ('12', 'Conciliación de operaciones recíprocas', 'OPA 12: Conciliación de operaciones recíprocas', '040', '043'),
  ('13', 'FONSET', 'OPA 13: FONSET', '040', '043'),
  ('14', 'demora en pagos', 'OPA 14: demora en pagos', '040', '043'),
  ('15', 'Procesos de Embargo de creditos entre terceros', 'OPA 15: Procesos de Embargo de creditos entre terceros', '040', '043'),
  ('1', 'Banco de materiales', 'OPA 1: Banco de materiales', '050', '050'),
  ('2', 'demolición de muros', 'OPA 2: demolición de muros', '050', '050'),
  ('3', 'señalización y aislamiento de obras', 'OPA 3: señalización y aislamiento de obras', '050', '050'),
  ('4', 'Poda de Parques y manzanas institucionales', 'OPA 4: Poda de Parques y manzanas institucionales', '050', '050'),
  ('5', 'materialización de sanción demolición', 'OPA 5: materialización de sanción demolición', '050', '050'),
  ('6', 'Indicador de Producción de Obras Civiles IPOC del DANE', 'OPA 6: Indicador de Producción de Obras Civiles IPOC del DANE', '050', '050'),
  ('1', 'Infrastructura vial y obras generales del municipio.', 'OPA 1: Infrastructura vial y obras generales del municipio.', '050', '051'),
  ('2', 'Coordinación y gestión de redes de servicios públicos', 'OPA 2: Coordinación y gestión de redes de servicios públicos', '050', '051'),
  ('3', 'Red vial del municipio.', 'OPA 3: Red vial del municipio.', '050', '051'),
  ('4', 'Mantenimiento de edificios públicos', 'OPA 4: Mantenimiento de edificios públicos', '050', '051'),
  ('5', 'Adecuación de edificios publicos', 'OPA 5: Adecuación de edificios publicos', '050', '051'),
  ('6', 'Supervisión de obras de infrastructura', 'OPA 6: Supervisión de obras de infrastructura', '050', '051'),
  ('7', 'interventorias de obras de infrastructura', 'OPA 7: interventorias de obras de infrastructura', '050', '051'),
  ('8', 'Metodologías del sistema de concesión de obras o servicios', 'OPA 8: Metodologías del sistema de concesión de obras o servicios', '050', '051'),
  ('9', 'Infraestructura comunitaria y asociativa', 'OPA 9: Infraestructura comunitaria y asociativa', '050', '051'),
  ('10', 'Materiales usados en proyectos', 'OPA 10: Materiales usados en proyectos', '050', '051'),
  ('11', 'Informes de estado de las obras', 'OPA 11: Informes de estado de las obras', '050', '051'),
  ('12', 'obras inconclusas', 'OPA 12: obras inconclusas', '050', '051'),
  ('13', 'Administración de maquinaria, equipo y herramienta menor del municipio', 'OPA 13: Administración de maquinaria, equipo y herramienta menor del municipio', '050', '051'),
  ('14', 'Adquisición de repuestos y/o herramienta menor para la maQuinaria del municipio', 'OPA 14: Adquisición de repuestos y/o herramienta menor para la maQuinaria del municipio', '050', '051'),
  ('15', 'Novedades y mantenimiento preventivo de maquinaria', 'OPA 15: Novedades y mantenimiento preventivo de maquinaria', '050', '051'),
  ('16', 'Suministro y consumos para maquinaria', 'OPA 16: Suministro y consumos para maquinaria', '050', '051'),
  ('17', 'Andenes', 'OPA 17: Andenes', '050', '051'),
  ('18', 'mantenimiento de estructura puentes', 'OPA 18: mantenimiento de estructura puentes', '050', '051'),
  ('19', 'visitas', 'OPA 19: visitas', '050', '051'),
  ('20', 'Intervención vía', 'OPA 20: Intervención vía', '050', '051'),
  ('21', 'ajuste de fachadas', 'OPA 21: ajuste de fachadas', '050', '051'),
  ('22', 'reparaciones locativas(lamparas, baños, infiltraciones)', 'OPA 22: reparaciones locativas(lamparas, baños, infiltraciones)', '050', '051'),
  ('23', 'Informe sobre estado de bienes de interes cultural', 'OPA 23: Informe sobre estado de bienes de interes cultural', '050', '051'),
  ('1', 'Etapa precontractual y ejecución de contratos de la dependencia', 'OPA 1: Etapa precontractual y ejecución de contratos de la dependencia', '050', '052')
) AS v(code, name, description, dep_code, subdep_code)
JOIN dependencies d ON d.code = v.dep_code
LEFT JOIN subdependencies s ON s.dependency_id = d.id AND s.code = v.subdep_code
WHERE NOT EXISTS (
    SELECT 1 FROM opas o 
    WHERE o.code = v.code 
    AND o.dependency_id = d.id 
    AND (o.subdependency_id = s.id OR (o.subdependency_id IS NULL AND s.id IS NULL))
);


-- LOTE DE 50 OPAs
INSERT INTO opas (code, name, description, dependency_id, subdependency_id, is_active)
SELECT v.code, v.name, v.description, d.id, s.id, true
FROM (VALUES
  ('2', 'Estudios técnicos diseño y estructura de pavimientos', 'OPA 2: Estudios técnicos diseño y estructura de pavimientos', '050', '052'),
  ('3', 'Estudios de suelos y geotécnicos', 'OPA 3: Estudios de suelos y geotécnicos', '050', '052'),
  ('4', 'Diseño red vial del municipio', 'OPA 4: Diseño red vial del municipio', '050', '052'),
  ('5', 'Estudios y diseños de las obras', 'OPA 5: Estudios y diseños de las obras', '050', '052'),
  ('6', 'Documentación obras', 'OPA 6: Documentación obras', '050', '052'),
  ('7', 'Estudios proyecto de integracion regional capital., Bogotá - Cundinamarca', 'OPA 7: Estudios proyecto de integracion regional capital., Bogotá - Cundinamarca', '050', '052'),
  ('1', 'Sistema de valorización', 'OPA 1: Sistema de valorización', '050', '053'),
  ('2', 'Liquidación de valorización', 'OPA 2: Liquidación de valorización', '050', '053'),
  ('3', 'Atención a contribuyentes en temas de valorización', 'OPA 3: Atención a contribuyentes en temas de valorización', '050', '053'),
  ('1', 'Consejo municipal de politica fiscal', 'OPA 1: Consejo municipal de politica fiscal', '060', '060'),
  ('2', 'Lucha contra la pobreza extrema', 'OPA 2: Lucha contra la pobreza extrema', '060', '060'),
  ('3', 'Temas realacionados con voluntariados', 'OPA 3: Temas realacionados con voluntariados', '060', '060'),
  ('4', 'Grupos vulnerables', 'OPA 4: Grupos vulnerables', '060', '060'),
  ('5', 'Préstamo de la Biblioteca HOQABIGA', 'OPA 5: Préstamo de la Biblioteca HOQABIGA', '060', '060'),
  ('6', 'Comunicaciones dirigidas a la Gestora Social', 'OPA 6: Comunicaciones dirigidas a la Gestora Social', '060', '060'),
  ('1', 'solicitud de espacios para procesos juveniles', 'OPA 1: solicitud de espacios para procesos juveniles', '060', '061'),
  ('2', 'Sistema nacional de información sobre la juventud', 'OPA 2: Sistema nacional de información sobre la juventud', '060', '061'),
  ('3', 'Bienestar integral de los y las jóvenes', 'OPA 3: Bienestar integral de los y las jóvenes', '060', '061'),
  ('4', 'Derechos de las juventudes', 'OPA 4: Derechos de las juventudes', '060', '061'),
  ('5', 'Plataforma municipal de juventudes', 'OPA 5: Plataforma municipal de juventudes', '060', '061'),
  ('6', 'Gobierno municipal estudiantil', 'OPA 6: Gobierno municipal estudiantil', '060', '061'),
  ('7', 'Consejo municipal de juventud', 'OPA 7: Consejo municipal de juventud', '060', '061'),
  ('8', 'Jovenes en accion exclusivo de esta dirección', 'OPA 8: Jovenes en accion exclusivo de esta dirección', '060', '061'),
  ('1', 'Acciones de responsabilidad social empresarial, academica y gubernamental', 'OPA 1: Acciones de responsabilidad social empresarial, academica y gubernamental', '060', '062'),
  ('2', 'programas y proyectos mujer y género', 'OPA 2: programas y proyectos mujer y género', '060', '062'),
  ('3', 'concejo consultivo de la mujer', 'OPA 3: concejo consultivo de la mujer', '060', '062'),
  ('4', 'Programas y proyectos infancia', 'OPA 4: Programas y proyectos infancia', '060', '062'),
  ('5', 'Programas y proyectos adolescencia', 'OPA 5: Programas y proyectos adolescencia', '060', '062'),
  ('6', 'Programas y proyectos familia', 'OPA 6: Programas y proyectos familia', '060', '062'),
  ('7', 'Formación de familias', 'OPA 7: Formación de familias', '060', '062'),
  ('8', 'Pautas de crianza', 'OPA 8: Pautas de crianza', '060', '062'),
  ('9', 'Programas y proyectos adulto mayor', 'OPA 9: Programas y proyectos adulto mayor', '060', '062'),
  ('10', 'Uso del tiempo libre adulto mayor', 'OPA 10: Uso del tiempo libre adulto mayor', '060', '062'),
  ('11', 'Adulto mayor en estado de abandono', 'OPA 11: Adulto mayor en estado de abandono', '060', '062'),
  ('12', 'Beneficencia', 'OPA 12: Beneficencia', '060', '062'),
  ('13', 'Colombia mayor', 'OPA 13: Colombia mayor', '060', '062'),
  ('14', 'Familias en acción', 'OPA 14: Familias en acción', '060', '062'),
  ('15', 'Programas y proyectos población con discapacidad, población indigena, etnias', 'OPA 15: Programas y proyectos población con discapacidad, población indigena, etnias', '060', '062'),
  ('16', 'actualizacion de sistemas de información de la población frente al desarrollo humano y social', 'OPA 16: actualizacion de sistemas de información de la población frente al desarrollo humano y social', '060', '062'),
  ('17', 'Politícas sociales de envejecimiento, seguridad alimentaria y nutricional', 'OPA 17: Politícas sociales de envejecimiento, seguridad alimentaria y nutricional', '060', '062'),
  ('18', 'Jardines sociales', 'OPA 18: Jardines sociales', '060', '062'),
  ('19', 'Centro de Desarrollo Infantil', 'OPA 19: Centro de Desarrollo Infantil', '060', '062'),
  ('20', 'Madres Gestantes', 'OPA 20: Madres Gestantes', '060', '062'),
  ('21', 'Desnutrición de niños', 'OPA 21: Desnutrición de niños', '060', '062'),
  ('22', 'Ingreso solidario', 'OPA 22: Ingreso solidario', '060', '062'),
  ('23', 'Vigilancia y control Hogar Geriatrico San Rafael', 'OPA 23: Vigilancia y control Hogar Geriatrico San Rafael', '060', '062'),
  ('24', 'INFORMACIÓN SOBRE RENTA CIUDADANA', 'OPA 24: INFORMACIÓN SOBRE RENTA CIUDADANA', '060', '062'),
  ('25', 'Certificado de habitante de calle', 'OPA 25: Certificado de habitante de calle', '060', '062'),
  ('26', 'Abandono adulto mayor y vulnerabilidad(Derecho de petición)', 'OPA 26: Abandono adulto mayor y vulnerabilidad(Derecho de petición)', '060', '062'),
  ('1', 'Desarrollo cultural , acceso a los bienes y servicios de cultura', 'OPA 1: Desarrollo cultural , acceso a los bienes y servicios de cultura', '060', '063')
) AS v(code, name, description, dep_code, subdep_code)
JOIN dependencies d ON d.code = v.dep_code
LEFT JOIN subdependencies s ON s.dependency_id = d.id AND s.code = v.subdep_code
WHERE NOT EXISTS (
    SELECT 1 FROM opas o 
    WHERE o.code = v.code 
    AND o.dependency_id = d.id 
    AND (o.subdependency_id = s.id OR (o.subdependency_id IS NULL AND s.id IS NULL))
);


-- LOTE DE 50 OPAs
INSERT INTO opas (code, name, description, dependency_id, subdependency_id, is_active)
SELECT v.code, v.name, v.description, d.id, s.id, true
FROM (VALUES
  ('2', 'prestamo de trajes- presentaciones artisticas', 'OPA 2: prestamo de trajes- presentaciones artisticas', '060', '063'),
  ('3', 'Sistema de información Cultural', 'OPA 3: Sistema de información Cultural', '060', '063'),
  ('4', 'incentivo de la libre creación cultural', 'OPA 4: incentivo de la libre creación cultural', '060', '063'),
  ('5', 'Declaratoria y manejo de bienes de interés cultural', 'OPA 5: Declaratoria y manejo de bienes de interés cultural', '060', '063'),
  ('6', 'Asesoría cultural a instituciones del municipio', 'OPA 6: Asesoría cultural a instituciones del municipio', '060', '063'),
  ('7', 'Escuela de técnica de artes del Municipio', 'OPA 7: Escuela de técnica de artes del Municipio', '060', '063'),
  ('8', 'Biblioteca y su red', 'OPA 8: Biblioteca y su red', '060', '063'),
  ('9', 'Consejos de cultura y sistema nacional de cultura', 'OPA 9: Consejos de cultura y sistema nacional de cultura', '060', '063'),
  ('10', 'Estimulos y premios para el reconocimiento de artistas y autores.', 'OPA 10: Estimulos y premios para el reconocimiento de artistas y autores.', '060', '063'),
  ('11', 'Registro de bienes de interes cultural', 'OPA 11: Registro de bienes de interes cultural', '060', '063'),
  ('12', 'Protección y conservación del patrimonio cultural', 'OPA 12: Protección y conservación del patrimonio cultural', '060', '063'),
  ('13', 'oferta artistica', 'OPA 13: oferta artistica', '060', '063'),
  ('14', 'eventos artisticos', 'OPA 14: eventos artisticos', '060', '063'),
  ('15', 'prestamo de tarima', 'OPA 15: prestamo de tarima', '060', '063'),
  ('16', 'Permisos  para pintura de murales', 'OPA 16: Permisos  para pintura de murales', '060', '063'),
  ('17', 'Madres Gestantes', 'OPA 17: Madres Gestantes', '060', '063'),
  ('1', 'Se creó el tipo documental "COMUNICACIONES PARA SEC. EDUCACIÓN", el cual se debe seleccionar cuando lleguen derechos de petición o cualquier comunicación excepto tutelas para la Secretaría de Educación.', 'OPA 1: Se creó el tipo documental "COMUNICACIONES PARA SEC. EDUCACIÓN", el cual se debe seleccionar cuando lleguen derechos de petición o cualquier comunicación excepto tutelas para la Secretaría de Educación.', '070', '070'),
  ('1', 'Inspección, vigilancia y control instituciones educativas publicas', 'OPA 1: Inspección, vigilancia y control instituciones educativas publicas', '070', '071'),
  ('2', 'Inspección, vigilancia y control instituciones educativas privadas', 'OPA 2: Inspección, vigilancia y control instituciones educativas privadas', '070', '071'),
  ('3', 'Inspección, vigilancia y control entidades sin animo de lucro con fines educativos', 'OPA 3: Inspección, vigilancia y control entidades sin animo de lucro con fines educativos', '070', '071'),
  ('4', 'Inspección, vigilancia y control asociación de padres de familia', 'OPA 4: Inspección, vigilancia y control asociación de padres de familia', '070', '071'),
  ('5', 'Personerias juridicas instituciones educativas', 'OPA 5: Personerias juridicas instituciones educativas', '070', '071'),
  ('6', 'Investigaciones a establecimientos de educación formal', 'OPA 6: Investigaciones a establecimientos de educación formal', '070', '071'),
  ('7', 'Investigaciones instituciones de formación para el trabajo y desarrollo humano', 'OPA 7: Investigaciones instituciones de formación para el trabajo y desarrollo humano', '070', '071'),
  ('8', 'Investigaciones a entidades sin animo de lucro con fines educativos', 'OPA 8: Investigaciones a entidades sin animo de lucro con fines educativos', '070', '071'),
  ('9', 'Investigaciones a asociación de padres de familia', 'OPA 9: Investigaciones a asociación de padres de familia', '070', '071'),
  ('10', 'Costos educativos', 'OPA 10: Costos educativos', '070', '071'),
  ('11', 'Sanciones instituciones educativas', 'OPA 11: Sanciones instituciones educativas', '070', '071'),
  ('12', 'Actualización proyectos educativos institucionales', 'OPA 12: Actualización proyectos educativos institucionales', '070', '071'),
  ('13', 'Normas de convivencia escolar', 'OPA 13: Normas de convivencia escolar', '070', '071'),
  ('14', 'Seguimiento jornada escolar', 'OPA 14: Seguimiento jornada escolar', '070', '071'),
  ('15', 'Seguimiento jornada laboral establecimientos educativos oficiales', 'OPA 15: Seguimiento jornada laboral establecimientos educativos oficiales', '070', '071'),
  ('16', 'Sistemas de infomarmación sobre instituciones educativas', 'OPA 16: Sistemas de infomarmación sobre instituciones educativas', '070', '071'),
  ('17', 'Cierre de establecimientos educativos', 'OPA 17: Cierre de establecimientos educativos', '070', '071'),
  ('1', 'Procesos de aprendizaje', 'OPA 1: Procesos de aprendizaje', '070', '072'),
  ('2', 'Fortalecimiento de la educación', 'OPA 2: Fortalecimiento de la educación', '070', '072'),
  ('3', 'Procesos evaluativos', 'OPA 3: Procesos evaluativos', '070', '072'),
  ('4', 'Estrategias y metodologias', 'OPA 4: Estrategias y metodologias', '070', '072'),
  ('5', 'Programas de formación docente', 'OPA 5: Programas de formación docente', '070', '072'),
  ('6', 'Actualización docente', 'OPA 6: Actualización docente', '070', '072'),
  ('7', 'Integración de población campesina al sistema educativo', 'OPA 7: Integración de población campesina al sistema educativo', '070', '072'),
  ('8', 'Integración de población con talentos excepcionales al sistema educativo', 'OPA 8: Integración de población con talentos excepcionales al sistema educativo', '070', '072'),
  ('9', 'Integración de adultos al sistema educativo', 'OPA 9: Integración de adultos al sistema educativo', '070', '072'),
  ('10', 'Integración de población rural al sistema educativo', 'OPA 10: Integración de población rural al sistema educativo', '070', '072'),
  ('11', 'Integración de población con limitaciones al sistema educativo', 'OPA 11: Integración de población con limitaciones al sistema educativo', '070', '072'),
  ('12', 'Modelos de educación técnica', 'OPA 12: Modelos de educación técnica', '070', '072'),
  ('13', 'Modelos de educación tecnológica', 'OPA 13: Modelos de educación tecnológica', '070', '072'),
  ('14', 'Articulación con educación media', 'OPA 14: Articulación con educación media', '070', '072'),
  ('15', 'Articulación con educación superior', 'OPA 15: Articulación con educación superior', '070', '072'),
  ('16', 'Planes de mejoramiento de las instituciones educativas', 'OPA 16: Planes de mejoramiento de las instituciones educativas', '070', '072')
) AS v(code, name, description, dep_code, subdep_code)
JOIN dependencies d ON d.code = v.dep_code
LEFT JOIN subdependencies s ON s.dependency_id = d.id AND s.code = v.subdep_code
WHERE NOT EXISTS (
    SELECT 1 FROM opas o 
    WHERE o.code = v.code 
    AND o.dependency_id = d.id 
    AND (o.subdependency_id = s.id OR (o.subdependency_id IS NULL AND s.id IS NULL))
);


-- LOTE DE 50 OPAs
INSERT INTO opas (code, name, description, dependency_id, subdependency_id, is_active)
SELECT v.code, v.name, v.description, d.id, s.id, true
FROM (VALUES
  ('17', 'Calidad de la educación', 'OPA 17: Calidad de la educación', '070', '072'),
  ('18', 'Estrategias de acceso y permanencia', 'OPA 18: Estrategias de acceso y permanencia', '070', '072'),
  ('19', 'Creditos educación superior (FOES)', 'OPA 19: Creditos educación superior (FOES)', '070', '072'),
  ('20', 'Plan educativo institucional', 'OPA 20: Plan educativo institucional', '070', '072'),
  ('21', 'Programa de alimentación escolar  (PAE)', 'OPA 21: Programa de alimentación escolar  (PAE)', '070', '072'),
  ('22', 'Mantenimiento instituciones educativas oficiales', 'OPA 22: Mantenimiento instituciones educativas oficiales', '070', '072'),
  ('23', 'Trasporte escolar', 'OPA 23: Trasporte escolar', '070', '072'),
  ('24', 'Dotación escolar', 'OPA 24: Dotación escolar', '070', '072'),
  ('25', 'El sistema integrado de matrícula SIMAT', 'OPA 25: El sistema integrado de matrícula SIMAT', '070', '072'),
  ('1', 'Estados financieros instituciones educativas oficiales', 'OPA 1: Estados financieros instituciones educativas oficiales', '070', '073'),
  ('2', 'Atención al ciudadano de la secretaría de educación', 'OPA 2: Atención al ciudadano de la secretaría de educación', '070', '073'),
  ('3', 'Archivo de la secretaría de educación', 'OPA 3: Archivo de la secretaría de educación', '070', '073'),
  ('4', 'Reclutamiento docentes', 'OPA 4: Reclutamiento docentes', '070', '073'),
  ('5', 'Concursos docentes', 'OPA 5: Concursos docentes', '070', '073'),
  ('6', 'Selección docentes', 'OPA 6: Selección docentes', '070', '073'),
  ('7', 'Inducción docentes', 'OPA 7: Inducción docentes', '070', '073'),
  ('8', 'Formación docentes', 'OPA 8: Formación docentes', '070', '073'),
  ('9', 'Evaluación de docentes', 'OPA 9: Evaluación de docentes', '070', '073'),
  ('10', 'Salud ocupacional docentes', 'OPA 10: Salud ocupacional docentes', '070', '073'),
  ('11', 'Bienestar docentes', 'OPA 11: Bienestar docentes', '070', '073'),
  ('12', 'Capacitación docentes', 'OPA 12: Capacitación docentes', '070', '073'),
  ('13', 'Reinducción docentes', 'OPA 13: Reinducción docentes', '070', '073'),
  ('14', 'Escalafon docentes', 'OPA 14: Escalafon docentes', '070', '073'),
  ('15', 'Carrera administrativa docentes', 'OPA 15: Carrera administrativa docentes', '070', '073'),
  ('16', 'Información de personal docente', 'OPA 16: Información de personal docente', '070', '073'),
  ('17', 'Historias laborales docentes', 'OPA 17: Historias laborales docentes', '070', '073'),
  ('18', 'Certificación de personal docentes', 'OPA 18: Certificación de personal docentes', '070', '073'),
  ('19', 'Planta docente', 'OPA 19: Planta docente', '070', '073'),
  ('20', 'Novedades nómina', 'OPA 20: Novedades nómina', '070', '073'),
  ('21', 'Cesantias docentes', 'OPA 21: Cesantias docentes', '070', '073'),
  ('22', 'Embargos docentes', 'OPA 22: Embargos docentes', '070', '073'),
  ('23', 'Libranzas docentes', 'OPA 23: Libranzas docentes', '070', '073'),
  ('24', 'Liquidación nómina docente y administrativa de las instituciones educativas oficiales', 'OPA 24: Liquidación nómina docente y administrativa de las instituciones educativas oficiales', '070', '073'),
  ('25', 'Prestaciones sociales docentes', 'OPA 25: Prestaciones sociales docentes', '070', '073'),
  ('26', 'Sindicato docentes', 'OPA 26: Sindicato docentes', '070', '073'),
  ('1', 'Contribución solidaria- SISBEN', 'OPA 1: Contribución solidaria- SISBEN', '080', '080'),
  ('2', 'Aseguramiento, afilicaciones regimen contributivo y subsidiado', 'OPA 2: Aseguramiento, afilicaciones regimen contributivo y subsidiado', '080', '080'),
  ('3', 'Sistema General de Seguridad Social en Salud. Afiliacion a EPS, Sistema de afiliacion transaccional - SAT', 'OPA 3: Sistema General de Seguridad Social en Salud. Afiliacion a EPS, Sistema de afiliacion transaccional - SAT', '080', '080'),
  ('4', 'Abandono adulto mayor y vulnerabilidad(Derecho de petición)', 'OPA 4: Abandono adulto mayor y vulnerabilidad(Derecho de petición)', '080', '080'),
  ('5', 'Licencia de exhumación, inhumación, cremacion y traslado de cadáveres', 'OPA 5: Licencia de exhumación, inhumación, cremacion y traslado de cadáveres', '080', '080'),
  ('6', 'Vigilancia a hogares geriatricos(excepción Hogar San Rafael que le correponde a Accion social)', 'OPA 6: Vigilancia a hogares geriatricos(excepción Hogar San Rafael que le correponde a Accion social)', '080', '080'),
  ('7', 'PQRS sistema de seguridad social en salud (Los reclamos o quejas por la mala prestación de servicios de salud debe radicarse como derecho de petición y no como reclamo).', 'OPA 7: PQRS sistema de seguridad social en salud (Los reclamos o quejas por la mala prestación de servicios de salud debe radicarse como derecho de petición y no como reclamo).', '080', '080'),
  ('1', 'Novedades y vigilancia epídemiologicas, infecciones', 'OPA 1: Novedades y vigilancia epídemiologicas, infecciones', '080', '081'),
  ('2', 'Vacunacion a humanos. PAI  Plan Ampliado de Vacunación', 'OPA 2: Vacunacion a humanos. PAI  Plan Ampliado de Vacunación', '080', '081'),
  ('3', 'Comité de estadisticas vitales', 'OPA 3: Comité de estadisticas vitales', '080', '081'),
  ('4', 'Registro Único de Afiliados  RUAF, MANGO, SIVIGILA, RIPS', 'OPA 4: Registro Único de Afiliados  RUAF, MANGO, SIVIGILA, RIPS', '080', '081'),
  ('5', 'Visita a IPS municipales. Riesgos asociados a IPS y EPS en el municipio por capacidad instalada y panorama de riesgo.', 'OPA 5: Visita a IPS municipales. Riesgos asociados a IPS y EPS en el municipio por capacidad instalada y panorama de riesgo.', '080', '081'),
  ('6', 'Analisis de situacion en salud', 'OPA 6: Analisis de situacion en salud', '080', '081'),
  ('7', 'Politicas de salud pública (Hábitos saludables, poblaciones vulnerables, enfermedades transmisibles, enfermedades no transmisibles, salud sexual y reproductiva; salud mental, salud ambiental, salud nutricional, vacunación)', 'OPA 7: Politicas de salud pública (Hábitos saludables, poblaciones vulnerables, enfermedades transmisibles, enfermedades no transmisibles, salud sexual y reproductiva; salud mental, salud ambiental, salud nutricional, vacunación)', '080', '081'),
  ('8', 'Plan Decenal de Salud Pública.', 'OPA 8: Plan Decenal de Salud Pública.', '080', '081')
) AS v(code, name, description, dep_code, subdep_code)
JOIN dependencies d ON d.code = v.dep_code
LEFT JOIN subdependencies s ON s.dependency_id = d.id AND s.code = v.subdep_code
WHERE NOT EXISTS (
    SELECT 1 FROM opas o 
    WHERE o.code = v.code 
    AND o.dependency_id = d.id 
    AND (o.subdependency_id = s.id OR (o.subdependency_id IS NULL AND s.id IS NULL))
);


-- LOTE DE 50 OPAs
INSERT INTO opas (code, name, description, dependency_id, subdependency_id, is_active)
SELECT v.code, v.name, v.description, d.id, s.id, true
FROM (VALUES
  ('9', 'Plan de Intervenciones Colectivas PIC', 'OPA 9: Plan de Intervenciones Colectivas PIC', '080', '081'),
  ('10', 'Prevención y promoción  gestantes.', 'OPA 10: Prevención y promoción  gestantes.', '080', '081'),
  ('11', 'AIEPI  Atención en enfermedades prevalentes en la infancia.', 'OPA 11: AIEPI  Atención en enfermedades prevalentes en la infancia.', '080', '081'),
  ('12', 'Enfermedades huerfanas', 'OPA 12: Enfermedades huerfanas', '080', '081'),
  ('13', 'Rutas integrales de atencion en salud', 'OPA 13: Rutas integrales de atencion en salud', '080', '081'),
  ('14', 'REPS- Registro Especial de Prestadores de Servicios de Salud', 'OPA 14: REPS- Registro Especial de Prestadores de Servicios de Salud', '080', '081'),
  ('15', 'Certificado de discapacidad', 'OPA 15: Certificado de discapacidad', '080', '081'),
  ('1', 'Concepto sanitario, Alimentos sanos y seguros, seguridad quimica, esfermedades trasmitidas por alimentos ETAS', 'OPA 1: Concepto sanitario, Alimentos sanos y seguros, seguridad quimica, esfermedades trasmitidas por alimentos ETAS', '080', '082'),
  ('2', 'Eventos transmisibles de origen zoonótico, enfermedades transmitidas por animales', 'OPA 2: Eventos transmisibles de origen zoonótico, enfermedades transmitidas por animales', '080', '082'),
  ('3', 'Protocolos de bioseguridad', 'OPA 3: Protocolos de bioseguridad', '080', '082'),
  ('4', 'Verificación de estándares de habilitación de los establecimientos dispensadores de medicamentos', 'OPA 4: Verificación de estándares de habilitación de los establecimientos dispensadores de medicamentos', '080', '082'),
  ('5', 'Proceso de inspección, vigilancia y control, IVC', 'OPA 5: Proceso de inspección, vigilancia y control, IVC', '080', '082'),
  ('6', 'Control de población canina y felina.', 'OPA 6: Control de población canina y felina.', '080', '082'),
  ('7', 'Vigilancia del agua para consumo humano: control en puntos de vertimiento de agua domestica,', 'OPA 7: Vigilancia del agua para consumo humano: control en puntos de vertimiento de agua domestica,', '080', '082'),
  ('8', 'Radiación electromagnética.', 'OPA 8: Radiación electromagnética.', '080', '082'),
  ('9', 'Esterilizacion y vacunacion antirrabica canina y felina.', 'OPA 9: Esterilizacion y vacunacion antirrabica canina y felina.', '080', '082'),
  ('10', 'Capacitacion sobre tenencia responsable de mascotas', 'OPA 10: Capacitacion sobre tenencia responsable de mascotas', '080', '082'),
  ('11', 'Control de Plagas (artrópodos y roedores) moscos, mosquitos para predios publicos realizan fumigación. Cuando se traten de denuncias relacionadas con predios privados se hace una visita para verificar', 'OPA 11: Control de Plagas (artrópodos y roedores) moscos, mosquitos para predios publicos realizan fumigación. Cuando se traten de denuncias relacionadas con predios privados se hace una visita para verificar', '080', '082'),
  ('12', 'Visitas a Establecimientos y Empresas expendedoras o aplicadores de sustancia químicas', 'OPA 12: Visitas a Establecimientos y Empresas expendedoras o aplicadores de sustancia químicas', '080', '082'),
  ('13', 'Visita a establecimientos de preparación y comercialización  de alimentos y transporte de alimentos.', 'OPA 13: Visita a establecimientos de preparación y comercialización  de alimentos y transporte de alimentos.', '080', '082'),
  ('14', 'Registro de ejemplares caninos potencialmente peligrosos', 'OPA 14: Registro de ejemplares caninos potencialmente peligrosos', '080', '082'),
  ('15', 'Maltrato animal de gatos y perros unicamente (Si es una denuncia radicarlo como proceso policivo a Resolución de Conflictos).', 'OPA 15: Maltrato animal de gatos y perros unicamente (Si es una denuncia radicarlo como proceso policivo a Resolución de Conflictos).', '080', '082'),
  ('16', 'Adopcion canina y felina, albergue municipal', 'OPA 16: Adopcion canina y felina, albergue municipal', '080', '082'),
  ('1', 'General', 'OPA 1: General', '090', '090'),
  ('1', 'Administracion plaza de mercado, vivero (centro de investigacion y desarrollo y tecnologia agropecuaria), Planta de Sacrificio y Faenado', 'OPA 1: Administracion plaza de mercado, vivero (centro de investigacion y desarrollo y tecnologia agropecuaria), Planta de Sacrificio y Faenado', '090', '091'),
  ('2', 'Centro de bienestar animal y COSO municipal', 'OPA 2: Centro de bienestar animal y COSO municipal', '090', '091'),
  ('3', 'Plan estrategico agropecuario, estadisticas agricolas', 'OPA 3: Plan estrategico agropecuario, estadisticas agricolas', '090', '091'),
  ('4', 'Asistencia tecnica rural para el sector agropecuario:  animales para consumo humano(pollos, conejos, codornices, lombrices, patos, piscos, pavos, vacas, cerdos)', 'OPA 4: Asistencia tecnica rural para el sector agropecuario:  animales para consumo humano(pollos, conejos, codornices, lombrices, patos, piscos, pavos, vacas, cerdos)', '090', '091'),
  ('5', 'Huertas urbanas', 'OPA 5: Huertas urbanas', '090', '091'),
  ('6', 'Seguridad y sostenibilidad alimentaria, plantulas y semillas de arboles frutales, hortalizas. *Semillas de plantas de produccion (arboles aromaticos (cidron, laurel, tomillo, plantulas como lechuga, cilantro)', 'OPA 6: Seguridad y sostenibilidad alimentaria, plantulas y semillas de arboles frutales, hortalizas. *Semillas de plantas de produccion (arboles aromaticos (cidron, laurel, tomillo, plantulas como lechuga, cilantro)', '090', '091'),
  ('7', 'Cadenas productivas y asociatividad empresarial, economia campesina', 'OPA 7: Cadenas productivas y asociatividad empresarial, economia campesina', '090', '091'),
  ('8', 'Programa Chia Emprende : Proyectos productivos de victimas, emprendimiento, ferias de emprendimiento, encuentros empresariales', 'OPA 8: Programa Chia Emprende : Proyectos productivos de victimas, emprendimiento, ferias de emprendimiento, encuentros empresariales', '090', '091'),
  ('9', 'Agencia publica de empleo: Desarrollo de competencias laborales, hojas de vida', 'OPA 9: Agencia publica de empleo: Desarrollo de competencias laborales, hojas de vida', '090', '091'),
  ('10', 'Bancarizacion y generacion de lineas de credito', 'OPA 10: Bancarizacion y generacion de lineas de credito', '090', '091'),
  ('11', 'Asistencia tecnica empresarial, proyectos de incubacion', 'OPA 11: Asistencia tecnica empresarial, proyectos de incubacion', '090', '091'),
  ('12', 'Solicitud de certificacion de actrividades agricolas predios  dedicados a actividades agrícolas y ganaderas', 'OPA 12: Solicitud de certificacion de actrividades agricolas predios  dedicados a actividades agrícolas y ganaderas', '090', '091'),
  ('13', 'Abandono o maltrato de especies mayores- pecuarias: (Vacas, cerdos, caballos, etc)', 'OPA 13: Abandono o maltrato de especies mayores- pecuarias: (Vacas, cerdos, caballos, etc)', '090', '091'),
  ('14', 'Impuesto al degüello de ganado mayor', 'OPA 14: Impuesto al degüello de ganado mayor', '090', '091'),
  ('15', 'Informacion general para apertura de establecimientos comerciales en el municipio', 'OPA 15: Informacion general para apertura de establecimientos comerciales en el municipio', '090', '091'),
  ('16', 'Solicitud de apoyo economico por reactivacion economica', 'OPA 16: Solicitud de apoyo economico por reactivacion economica', '090', '091'),
  ('1', 'Plan de desarrollo Turistico, proyectos de desarrollo turistico', 'OPA 1: Plan de desarrollo Turistico, proyectos de desarrollo turistico', '090', '092'),
  ('2', 'Fondo Nacional de Turismo- FONTUR', 'OPA 2: Fondo Nacional de Turismo- FONTUR', '090', '092'),
  ('3', 'Consejo municipal de Turismo, Consejo regional de seguridad turistica, policia de turismo', 'OPA 3: Consejo municipal de Turismo, Consejo regional de seguridad turistica, policia de turismo', '090', '092'),
  ('4', 'Festival Chia Gastronomica, ferias gastronomicas', 'OPA 4: Festival Chia Gastronomica, ferias gastronomicas', '090', '092'),
  ('5', 'Infraestructura turistica, señalizacion turistica', 'OPA 5: Infraestructura turistica, señalizacion turistica', '090', '092'),
  ('6', 'Vigilancia y control de prestadores de servicios turisticos frente al servicio (hoteles, agencias de viajes, guias turisticos, plataformas de comercializacion en linea, booking, Airbnb, apartamentos alquilados por dias), formalizacion turistica, superintendencia de industria y comercio en relacion con el turismo', 'OPA 6: Vigilancia y control de prestadores de servicios turisticos frente al servicio (hoteles, agencias de viajes, guias turisticos, plataformas de comercializacion en linea, booking, Airbnb, apartamentos alquilados por dias), formalizacion turistica, superintendencia de industria y comercio en relacion con el turismo', '090', '092'),
  ('7', 'Inventario de atractivos turisticos, rutas turisticas', 'OPA 7: Inventario de atractivos turisticos, rutas turisticas', '090', '092'),
  ('8', 'Zonas de desarrollo turistico prioritario', 'OPA 8: Zonas de desarrollo turistico prioritario', '090', '092'),
  ('9', 'Campañas de mercadeo y promocion turistica', 'OPA 9: Campañas de mercadeo y promocion turistica', '090', '092'),
  ('10', 'Capacitaciones para el sector turistico', 'OPA 10: Capacitaciones para el sector turistico', '090', '092')
) AS v(code, name, description, dep_code, subdep_code)
JOIN dependencies d ON d.code = v.dep_code
LEFT JOIN subdependencies s ON s.dependency_id = d.id AND s.code = v.subdep_code
WHERE NOT EXISTS (
    SELECT 1 FROM opas o 
    WHERE o.code = v.code 
    AND o.dependency_id = d.id 
    AND (o.subdependency_id = s.id OR (o.subdependency_id IS NULL AND s.id IS NULL))
);


-- LOTE DE 50 OPAs
INSERT INTO opas (code, name, description, dependency_id, subdependency_id, is_active)
SELECT v.code, v.name, v.description, d.id, s.id, true
FROM (VALUES
  ('11', 'Punto de informacion turistica', 'OPA 11: Punto de informacion turistica', '090', '092'),
  ('12', 'Registro nacional de turismo', 'OPA 12: Registro nacional de turismo', '090', '092'),
  ('1', 'Manejo de Fauna Silvestre  (zarigüeyas, cuando es invación se traslada a CAR)', 'OPA 1: Manejo de Fauna Silvestre  (zarigüeyas, cuando es invación se traslada a CAR)', '100', '106'),
  ('2', 'Tenencia ilegal y maltrato  de fauna silvestre (la Secretaria de Medio Ambiente, solicita operativo a la CAR)', 'OPA 2: Tenencia ilegal y maltrato  de fauna silvestre (la Secretaria de Medio Ambiente, solicita operativo a la CAR)', '100', '106'),
  ('3', 'Animales silvestres heridos o en estado de emergencia (la Secretaria de Medio Ambiente solicita Asistencia a la CAR)', 'OPA 3: Animales silvestres heridos o en estado de emergencia (la Secretaria de Medio Ambiente solicita Asistencia a la CAR)', '100', '106'),
  ('4', 'Control y vigilancia de los recursos naturales renovables y no renovables de fauna silvestre', 'OPA 4: Control y vigilancia de los recursos naturales renovables y no renovables de fauna silvestre', '100', '106'),
  ('1', 'Plan de Manejo Ambiental Municipal (competencia CAR )', 'OPA 1: Plan de Manejo Ambiental Municipal (competencia CAR )', '100', '102'),
  ('2', 'Plan de Manejo de Recuperación y Restauración Ambiental PMRAA (competencia CAR),  a la Secretaria de Medio Ambiente solamente información del estado del  Plan; requerimientos relacionados con impacto ambiental por minería, canteras', 'OPA 2: Plan de Manejo de Recuperación y Restauración Ambiental PMRAA (competencia CAR),  a la Secretaria de Medio Ambiente solamente información del estado del  Plan; requerimientos relacionados con impacto ambiental por minería, canteras', '100', '102'),
  ('3', 'Estructura ecológica del Municipio', 'OPA 3: Estructura ecológica del Municipio', '100', '102'),
  ('4', 'Control y Vigilancia de los recursos naturales renovables (no renovables No aplica para el Municipio de Chía)', 'OPA 4: Control y Vigilancia de los recursos naturales renovables (no renovables No aplica para el Municipio de Chía)', '100', '102'),
  ('1', 'Sensibilización en educación ambiental en Instituciones educativas, conjuntos residenciales, juntas comunales, comunidad', 'OPA 1: Sensibilización en educación ambiental en Instituciones educativas, conjuntos residenciales, juntas comunales, comunidad', '100', '107'),
  ('2', 'Proyectos educativos ambientales (asesoria y aprobación por el comité CIDEA)', 'OPA 2: Proyectos educativos ambientales (asesoria y aprobación por el comité CIDEA)', '100', '107'),
  ('1', 'Concepto para aprovechamiento forestal en espacio público', 'OPA 1: Concepto para aprovechamiento forestal en espacio público', '100', '101'),
  ('2', 'Visita Técnica  de inspección ocular para registrar el estado físico y sanitario de individuo arbóreo para establecer recomendaciones de manejo', 'OPA 2: Visita Técnica  de inspección ocular para registrar el estado físico y sanitario de individuo arbóreo para establecer recomendaciones de manejo', '100', '101'),
  ('3', 'Jornadas de reforestación', 'OPA 3: Jornadas de reforestación', '100', '101'),
  ('4', 'Control y vigilancia  de los recursos naturales renovables y no renovables', 'OPA 4: Control y vigilancia  de los recursos naturales renovables y no renovables', '100', '101'),
  ('5', 'Control y vigilancia del manejo de la vegetación', 'OPA 5: Control y vigilancia del manejo de la vegetación', '100', '101'),
  ('6', 'Poda de árboles en espacio público y privado área urbana y rural: La secretaría de Ambiente emite concepto y remite  el informe a la autoridad competente de requerirse.', 'OPA 6: Poda de árboles en espacio público y privado área urbana y rural: La secretaría de Ambiente emite concepto y remite  el informe a la autoridad competente de requerirse.', '100', '101'),
  ('7', 'Tala de árboles en espacio público área urbana: Medio Ambiente puede ordenar la tala.
Tala de árboles en propiedad privada Urbana y Rural: La CAR es quien autoriza la tala.', 'OPA 7: Tala de árboles en espacio público área urbana: Medio Ambiente puede ordenar la tala.
Tala de árboles en propiedad privada Urbana y Rural: La CAR es quien autoriza la tala.', '100', '101'),
  ('8', 'Poda de Césped urbano corresponde a EMSERCHIA', 'OPA 8: Poda de Césped urbano corresponde a EMSERCHIA', '100', '101'),
  ('9', 'Poda de Césped rural corresponde a Medio Ambiente', 'OPA 9: Poda de Césped rural corresponde a Medio Ambiente', '100', '101'),
  ('10', 'Poda de Parques y manzanas Institucionales corresponde a Obras Públicas', 'OPA 10: Poda de Parques y manzanas Institucionales corresponde a Obras Públicas', '100', '101'),
  ('11', 'Glorieta de Jumbo corresponde a Medio Ambiente', 'OPA 11: Glorieta de Jumbo corresponde a Medio Ambiente', '100', '101'),
  ('12', 'Árboles en riesgo: el concepto lo da Medio Ambiente y ejecuta Bomberos', 'OPA 12: Árboles en riesgo: el concepto lo da Medio Ambiente y ejecuta Bomberos', '100', '101'),
  ('13', 'Arboles con redes de servicios públicos o telemática: Dirección de servicios públicos', 'OPA 13: Arboles con redes de servicios públicos o telemática: Dirección de servicios públicos', '100', '101'),
  ('1', 'Certificado paisajístico (trámite de licenciamiento urbanísitico)', 'OPA 1: Certificado paisajístico (trámite de licenciamiento urbanísitico)', '100', '104'),
  ('2', 'Informe Técnico para beneficio de exención de impuesto predial', 'OPA 2: Informe Técnico para beneficio de exención de impuesto predial', '100', '104'),
  ('3', 'Informe Técnico de presunta infracción de los recursos naturales: agua, suelo, flora, aire', 'OPA 3: Informe Técnico de presunta infracción de los recursos naturales: agua, suelo, flora, aire', '100', '104'),
  ('4', 'Informe  Técnico de palomas (no erradicación y se comparte responsabilidad  con Secretaria de Salud, por enfermedades que genera)', 'OPA 4: Informe  Técnico de palomas (no erradicación y se comparte responsabilidad  con Secretaria de Salud, por enfermedades que genera)', '100', '104'),
  ('5', 'Informe Técnico Residuos construcción y demolición (PGIRS)', 'OPA 5: Informe Técnico Residuos construcción y demolición (PGIRS)', '100', '104'),
  ('6', 'Informe Técnico de puntos críticos  ubicados en predios privados  (PGIRS); competencia de EMSERCHIA espacio público', 'OPA 6: Informe Técnico de puntos críticos  ubicados en predios privados  (PGIRS); competencia de EMSERCHIA espacio público', '100', '104'),
  ('1', 'Información referente a Asociaciones gremiales  de recuperadores de oficio en proceso de formalización a empresas prestadoras de servicio de aprovechamiento reconocidas  por el municipio e inscritas  ante la Superintendencia de Servicios Públicos Domiciliarios', 'OPA 1: Información referente a Asociaciones gremiales  de recuperadores de oficio en proceso de formalización a empresas prestadoras de servicio de aprovechamiento reconocidas  por el municipio e inscritas  ante la Superintendencia de Servicios Públicos Domiciliarios', '100', '105'),
  ('2', 'Seguimiento a rutas, microrutas  y puntos de recolección de residuos potencialmente aprovechables en propiedades horizontales, viviendas  (no rutas habituales de recolección de basuras)', 'OPA 2: Seguimiento a rutas, microrutas  y puntos de recolección de residuos potencialmente aprovechables en propiedades horizontales, viviendas  (no rutas habituales de recolección de basuras)', '100', '105'),
  ('3', 'Solicitud de visitas de inspección  y vigilancia a estaciones de Clasificación  y aprovechamiento a Estaciones de de clasificación  y aprovechamiento - ECAS', 'OPA 3: Solicitud de visitas de inspección  y vigilancia a estaciones de Clasificación  y aprovechamiento a Estaciones de de clasificación  y aprovechamiento - ECAS', '100', '105'),
  ('4', 'Implementación de proyectos de autogestión de aprovechamiento orgánicos (composteras, tierra verde)', 'OPA 4: Implementación de proyectos de autogestión de aprovechamiento orgánicos (composteras, tierra verde)', '100', '105'),
  ('1', 'Visita de inspección ocular', 'OPA 1: Visita de inspección ocular', '100', '109'),
  ('2', 'Trámite e impuesto de permiso de publicidad exterior. Aplica para todo tipo de publicidad que supere los 8 mts2 constituyéndose como vallas', 'OPA 2: Trámite e impuesto de permiso de publicidad exterior. Aplica para todo tipo de publicidad que supere los 8 mts2 constituyéndose como vallas', '100', '109'),
  ('3', 'Notificación Resolución publicidad (DCAC debe llamar a la SMA para realizar la notificación)', 'OPA 3: Notificación Resolución publicidad (DCAC debe llamar a la SMA para realizar la notificación)', '100', '109'),
  ('4', 'Registro de avisos publicidad menor a 8mts2', 'OPA 4: Registro de avisos publicidad menor a 8mts2', '100', '109'),
  ('5', 'Trámite de publicidad temporal  PERIFONEO (por mes ) PERIFONEO (quincenal) PUBLICIDAD ESTÁTICA MENOR A 8 mts2 (por m2 anual), PUBLICIDAD EXTERIOR MÓVIL (por mes o fracción de mes) PUBLICIDAD DINÁMICA Y/O DIGITAL MENOR A 8 M2 (por m2 anual), PASACALLES, PUBLICIDAD COMBINADA, PENDONES, PUBLICIDAD AEREA, PUBLICIDAD  MEDIANTE PÁNELES DE CERRAMIENTOS TEMPORALES', 'OPA 5: Trámite de publicidad temporal  PERIFONEO (por mes ) PERIFONEO (quincenal) PUBLICIDAD ESTÁTICA MENOR A 8 mts2 (por m2 anual), PUBLICIDAD EXTERIOR MÓVIL (por mes o fracción de mes) PUBLICIDAD DINÁMICA Y/O DIGITAL MENOR A 8 M2 (por m2 anual), PASACALLES, PUBLICIDAD COMBINADA, PENDONES, PUBLICIDAD AEREA, PUBLICIDAD  MEDIANTE PÁNELES DE CERRAMIENTOS TEMPORALES', '100', '109'),
  ('6', 'Notificación resolución publicidad temporal  (DCAC debe llamar a la SMA para realizar la notificación)', 'OPA 6: Notificación resolución publicidad temporal  (DCAC debe llamar a la SMA para realizar la notificación)', '100', '109'),
  ('1', 'Planes de ordenación y manejo de las cuencas hidrográficas - POMCA', 'OPA 1: Planes de ordenación y manejo de las cuencas hidrográficas - POMCA', '100', '103'),
  ('2', 'Curva de lluvias, intensidad, duración y frecuencia "Curva de IDF" (IDEAM-CAR)', 'OPA 2: Curva de lluvias, intensidad, duración y frecuencia "Curva de IDF" (IDEAM-CAR)', '100', '103'),
  ('3', 'Conservación y protección de fuentes hídricas  de origen natural (ronda hídrica, cauce de rios, quebradas, nacimientos, chucua, humedales)', 'OPA 3: Conservación y protección de fuentes hídricas  de origen natural (ronda hídrica, cauce de rios, quebradas, nacimientos, chucua, humedales)', '100', '103'),
  ('4', 'Informe de autorización intervención de vallados - antrópico (acceso a predios y restitución de tramos obstruidos)', 'OPA 4: Informe de autorización intervención de vallados - antrópico (acceso a predios y restitución de tramos obstruidos)', '100', '103'),
  ('5', 'Mantenimento  manual de vallados  (con personal operativo)', 'OPA 5: Mantenimento  manual de vallados  (con personal operativo)', '100', '103'),
  ('1', 'Emisión de ruido por fuentes fijas', 'OPA 1: Emisión de ruido por fuentes fijas', '100', '108'),
  ('2', 'Material particulado, olores ofensivos', 'OPA 2: Material particulado, olores ofensivos', '100', '108'),
  ('1', 'Tutelas dirigidas a la Secretaria de Movilidad', 'OPA 1: Tutelas dirigidas a la Secretaria de Movilidad', '110', '110'),
  ('2', 'Invitaciones externas a eventos del Secretario de despacho', 'OPA 2: Invitaciones externas a eventos del Secretario de despacho', '110', '110')
) AS v(code, name, description, dep_code, subdep_code)
JOIN dependencies d ON d.code = v.dep_code
LEFT JOIN subdependencies s ON s.dependency_id = d.id AND s.code = v.subdep_code
WHERE NOT EXISTS (
    SELECT 1 FROM opas o 
    WHERE o.code = v.code 
    AND o.dependency_id = d.id 
    AND (o.subdependency_id = s.id OR (o.subdependency_id IS NULL AND s.id IS NULL))
);


-- LOTE DE 50 OPAs
INSERT INTO opas (code, name, description, dependency_id, subdependency_id, is_active)
SELECT v.code, v.name, v.description, d.id, s.id, true
FROM (VALUES
  ('3', 'Requerimientos de contratacion de la secretaria', 'OPA 3: Requerimientos de contratacion de la secretaria', '110', '110'),
  ('4', 'Requerimientos desde el Concejo Municipal', 'OPA 4: Requerimientos desde el Concejo Municipal', '110', '110'),
  ('1', 'Requerimientos respecto al funcionamiento o novedades con el transporte publico del municipio', 'OPA 1: Requerimientos respecto al funcionamiento o novedades con el transporte publico del municipio', '110', '111'),
  ('2', 'Quejas y reclamos relacionados con el servicio y operación de transporte público', 'OPA 2: Quejas y reclamos relacionados con el servicio y operación de transporte público', '110', '111'),
  ('3', 'Prescripciones de comparendos del 2006 hacia atrás', 'OPA 3: Prescripciones de comparendos del 2006 hacia atrás', '110', '111'),
  ('4', 'Desvinculaciones de vehiculos de empresas transportaras (chatarrización)', 'OPA 4: Desvinculaciones de vehiculos de empresas transportaras (chatarrización)', '110', '111'),
  ('5', 'Capacidad transportadora', 'OPA 5: Capacidad transportadora', '110', '111'),
  ('6', 'Solicitudes de audiencias por comparendos', 'OPA 6: Solicitudes de audiencias por comparendos', '110', '111'),
  ('7', 'Suspension, cancelacion, devolucion de licencias de conduccion por embriaguez', 'OPA 7: Suspension, cancelacion, devolucion de licencias de conduccion por embriaguez', '110', '111'),
  ('8', 'Impugnacion de comparendos', 'OPA 8: Impugnacion de comparendos', '110', '111'),
  ('9', 'Expedientes disciplinarios', 'OPA 9: Expedientes disciplinarios', '110', '111'),
  ('10', 'Trámite de servicio publico de pasajeros', 'OPA 10: Trámite de servicio publico de pasajeros', '110', '111'),
  ('11', 'Paraderos de transporte publico del municipio', 'OPA 11: Paraderos de transporte publico del municipio', '110', '111'),
  ('12', 'Pruebas de alcoholemia', 'OPA 12: Pruebas de alcoholemia', '110', '111'),
  ('13', 'Copias de procesos de impugnación de comparendos dentro de procesos disciplinarios', 'OPA 13: Copias de procesos de impugnación de comparendos dentro de procesos disciplinarios', '110', '111'),
  ('14', 'Quejas y reclamos relacionados con la prestación de servicio al ciudadano por parte de la Union Temporal Circulemos de Chia - UT o quejas y reclamos relacionadas con trámites vehiculares.', 'OPA 14: Quejas y reclamos relacionados con la prestación de servicio al ciudadano por parte de la Union Temporal Circulemos de Chia - UT o quejas y reclamos relacionadas con trámites vehiculares.', '110', '111'),
  ('15', 'LOS IMPUESTOS DE VEHÍCULOS SE RECAUDAN DIRECTAMENTE EN LA GOBERNACIÓN DE CUNDINAMARCA EN BOGOTÁ', 'OPA 15: LOS IMPUESTOS DE VEHÍCULOS SE RECAUDAN DIRECTAMENTE EN LA GOBERNACIÓN DE CUNDINAMARCA EN BOGOTÁ', '110', '111'),
  ('1', 'Campañas y capacitaciones de seguridad vial', 'OPA 1: Campañas y capacitaciones de seguridad vial', '110', '112'),
  ('2', 'Agentes de transito', 'OPA 2: Agentes de transito', '110', '112'),
  ('3', 'Solicitudes de plan de manejo de transito PMT- Prorroga', 'OPA 3: Solicitudes de plan de manejo de transito PMT- Prorroga', '110', '112'),
  ('4', 'SOLICTUD DE PRORROGA para plan de manejo de transito PMT- Se  genera numero nuevo de radicado y se asocia al numero de radicado con el que se hizo la solictud inicalmente.', 'OPA 4: SOLICTUD DE PRORROGA para plan de manejo de transito PMT- Se  genera numero nuevo de radicado y se asocia al numero de radicado con el que se hizo la solictud inicalmente.', '110', '112'),
  ('5', 'Solicitudes de reductores de velocidad', 'OPA 5: Solicitudes de reductores de velocidad', '110', '112'),
  ('6', 'Solicitudes relacionadas con el funcionamiento o necesidades de semaforos', 'OPA 6: Solicitudes relacionadas con el funcionamiento o necesidades de semaforos', '110', '112'),
  ('7', 'Reportes de accidentes de transito', 'OPA 7: Reportes de accidentes de transito', '110', '112'),
  ('8', 'Permisos de circulacion vehicular por restricciones (¿novedades dia sin carro?)', 'OPA 8: Permisos de circulacion vehicular por restricciones (¿novedades dia sin carro?)', '110', '112'),
  ('9', 'Señalizacion de vias (¿requerimientos de mantenimiento??, instalacion de señalizacion informativa (hoteles, restaurantes, parqueaderos, hospitales, etc?', 'OPA 9: Señalizacion de vias (¿requerimientos de mantenimiento??, instalacion de señalizacion informativa (hoteles, restaurantes, parqueaderos, hospitales, etc?', '110', '112'),
  ('10', 'Plan Maestro de tránsito', 'OPA 10: Plan Maestro de tránsito', '110', '112'),
  ('11', 'Límite de tarifa de parqueadero', 'OPA 11: Límite de tarifa de parqueadero', '110', '112'),
  ('12', 'Información plan local de seguridad vial', 'OPA 12: Información plan local de seguridad vial', '110', '112'),
  ('13', 'Solicitud de Restricción de carga en Chía 3,4 toneladas o más. Decreto 279 de 2024.
UNICAMENTE: Empresas ubicadas en Chia, conductores o propietarios de vehiculos que residen en Chia. 

Para el caso de empresas registradas en Bogotá, pero que tienen una sede o sucursal en Chía, pueden transitar con el ultimo recibo de pago del ICA y adicional, el formato de remision de la empresa, donde conste la direccion de Chia donde va a ser entregado o recogido el producto.', 'OPA 13: Solicitud de Restricción de carga en Chía 3,4 toneladas o más. Decreto 279 de 2024.
UNICAMENTE: Empresas ubicadas en Chia, conductores o propietarios de vehiculos que residen en Chia. 

Para el caso de empresas registradas en Bogotá, pero que tienen una sede o sucursal en Chía, pueden transitar con el ultimo recibo de pago del ICA y adicional, el formato de remision de la empresa, donde conste la direccion de Chia donde va a ser entregado o recogido el producto.', '110', '112'),
  ('1', 'Inmovilizacion de vehiculos', 'OPA 1: Inmovilizacion de vehiculos', '110', '113'),
  ('2', 'Salidas de patios', 'OPA 2: Salidas de patios', '110', '113'),
  ('3', 'Prescripciones de comparendos del 2007 en adelante', 'OPA 3: Prescripciones de comparendos del 2007 en adelante', '110', '113'),
  ('4', 'Tramites con placas o certificados de tradicion', 'OPA 4: Tramites con placas o certificados de tradicion', '110', '113'),
  ('5', 'Traspasos, levantamiento de prendas y cancelacion de matriculas', 'OPA 5: Traspasos, levantamiento de prendas y cancelacion de matriculas', '110', '113'),
  ('6', 'Derechos de peticion de prescripciones, descargue de comparendos de las plataformas HQ RUNT- TRANSITO CHIA', 'OPA 6: Derechos de peticion de prescripciones, descargue de comparendos de las plataformas HQ RUNT- TRANSITO CHIA', '110', '113'),
  ('7', 'Embargos, levantamiento o inscripcion de medidas cautelares con referencia a vehiculos - 
DERECHO DE PETICIÓN procede cuando viene directamente del juzgado
REMISION DE DOCUMENTOS. Cuando viene directamente del peticionario pues es el juzgado quien dictamina', 'OPA 7: Embargos, levantamiento o inscripcion de medidas cautelares con referencia a vehiculos - 
DERECHO DE PETICIÓN procede cuando viene directamente del juzgado
REMISION DE DOCUMENTOS. Cuando viene directamente del peticionario pues es el juzgado quien dictamina', '110', '113'),
  ('8', 'Traslado o devoluciones de cuentas de vehiculos- REMISION DE DOCUMENTOS', 'OPA 8: Traslado o devoluciones de cuentas de vehiculos- REMISION DE DOCUMENTOS', '110', '113'),
  ('9', 'Radicacion, traslados, devolucion de cuenta placas vehiculares', 'OPA 9: Radicacion, traslados, devolucion de cuenta placas vehiculares', '110', '113'),
  ('10', 'Solicitud historiales vehiculares', 'OPA 10: Solicitud historiales vehiculares', '110', '113'),
  ('11', 'Informacion sobre acuerdos de pago comparendos', 'OPA 11: Informacion sobre acuerdos de pago comparendos', '110', '113'),
  ('12', 'Tramites de personas (inscripcion RUNT, licencias de conduccion)', 'OPA 12: Tramites de personas (inscripcion RUNT, licencias de conduccion)', '110', '113'),
  ('13', 'Agendamiento citas para tramites', 'OPA 13: Agendamiento citas para tramites', '110', '113'),
  ('14', 'Certificado sobre retencion en la fuente por tramite de vehiculos', 'OPA 14: Certificado sobre retencion en la fuente por tramite de vehiculos', '110', '113'),
  ('15', 'tarjetas de operación', 'OPA 15: tarjetas de operación', '110', '113'),
  ('16', 'Certificación de cumplimiento de requisitos para el registro inicial de vehículo nuevo en reposición con Exclusión de IVA (CREI)', 'OPA 16: Certificación de cumplimiento de requisitos para el registro inicial de vehículo nuevo en reposición con Exclusión de IVA (CREI)', '110', '113'),
  ('17', 'Aprehension y secuestro de vehiculos', 'OPA 17: Aprehension y secuestro de vehiculos', '110', '113'),
  ('1', 'Politicas publicas de participacion ciudadana', 'OPA 1: Politicas publicas de participacion ciudadana', '120', '120'),
  ('2', 'Organización comunitaria', 'OPA 2: Organización comunitaria', '120', '120'),
  ('3', 'Rendicion de cuentas', 'OPA 3: Rendicion de cuentas', '120', '120')
) AS v(code, name, description, dep_code, subdep_code)
JOIN dependencies d ON d.code = v.dep_code
LEFT JOIN subdependencies s ON s.dependency_id = d.id AND s.code = v.subdep_code
WHERE NOT EXISTS (
    SELECT 1 FROM opas o 
    WHERE o.code = v.code 
    AND o.dependency_id = d.id 
    AND (o.subdependency_id = s.id OR (o.subdependency_id IS NULL AND s.id IS NULL))
);


-- LOTE DE 21 OPAs
INSERT INTO opas (code, name, description, dependency_id, subdependency_id, is_active)
SELECT v.code, v.name, v.description, d.id, s.id, true
FROM (VALUES
  ('4', 'Juntas de accion comunal  (Certificado de representación legal - Presidentes de Junta)', 'OPA 4: Juntas de accion comunal  (Certificado de representación legal - Presidentes de Junta)', '120', '120'),
  ('5', 'Procesos asociativos de organizaciones sociales comunitarias', 'OPA 5: Procesos asociativos de organizaciones sociales comunitarias', '120', '120'),
  ('6', 'Presupuesto participativo', 'OPA 6: Presupuesto participativo', '120', '120'),
  ('7', 'Inspeccion control, vigilancia sobre las organizaciones de accion comunal, Ley 743 de 2002, Decreto 890 de 2008, Decreto compilatorio 1066 de 2015', 'OPA 7: Inspeccion control, vigilancia sobre las organizaciones de accion comunal, Ley 743 de 2002, Decreto 890 de 2008, Decreto compilatorio 1066 de 2015', '120', '120'),
  ('8', 'Plan Indicativo de Accion', 'OPA 8: Plan Indicativo de Accion', '120', '120'),
  ('9', 'Asistencia técnica a Organizaciones de Acción Comunal y servidores públicos en torno a temas de participación ciudadana y acción comunal', 'OPA 9: Asistencia técnica a Organizaciones de Acción Comunal y servidores públicos en torno a temas de participación ciudadana y acción comunal', '120', '120'),
  ('10', 'Programas de formación - educación informal sobre aspectos relevantes para el ejercicio del derecho a la participación ciudadana.', 'OPA 10: Programas de formación - educación informal sobre aspectos relevantes para el ejercicio del derecho a la participación ciudadana.', '120', '120'),
  ('1', 'Orden de Archivo NUC, Archivo de una denuncia enviado por la fiscalia', 'OPA 1: Orden de Archivo NUC, Archivo de una denuncia enviado por la fiscalia', '200', '204'),
  ('1', 'Confirmar a que PTAR corresponde la conexion de acueducto y/o alcantarillado de un predio', 'OPA 1: Confirmar a que PTAR corresponde la conexion de acueducto y/o alcantarillado de un predio', '200', '203'),
  ('2', 'Viabilidad de servicios públicos(acueducto y alcantarillado)', 'OPA 2: Viabilidad de servicios públicos(acueducto y alcantarillado)', '200', '203'),
  ('3', 'Poda de Césped Área Urbana', 'OPA 3: Poda de Césped Área Urbana', '200', '203'),
  ('4', 'Acuerdos de pago, instalación y todo lo concerniente a los medidores de agua', 'OPA 4: Acuerdos de pago, instalación y todo lo concerniente a los medidores de agua', '200', '203'),
  ('1', 'Actualizacion del predio y/o rectificacion catastral  bien sea nombre del propietario, area o linderos', 'OPA 1: Actualizacion del predio y/o rectificacion catastral  bien sea nombre del propietario, area o linderos', '200', '212'),
  ('1', 'Sesiones urbanísticas en suelo rural y suburbano en el municipio de Chía.', 'OPA 1: Sesiones urbanísticas en suelo rural y suburbano en el municipio de Chía.', '200', '201'),
  ('2', 'Desistimiento de compra proyectos de vivienda VIS(PROYECTOS DE LA ADMINISTRACIÓN)', 'OPA 2: Desistimiento de compra proyectos de vivienda VIS(PROYECTOS DE LA ADMINISTRACIÓN)', '200', '201'),
  ('3', 'Préstamos auditorio ZEA MAYS, Casona Santa Rita y Terminal de Transporte', 'OPA 3: Préstamos auditorio ZEA MAYS, Casona Santa Rita y Terminal de Transporte', '200', '201'),
  ('1', 'Mantenimiento, administracion peticion de prestamo de los parques: 
Unidad deportiva parque paraiso 
Unidad deportiva Bojaca
Unidad deportiva Rio Frio 
Unidad deportiva La Lorena
Unidad deportiva Parque El Cedro
Unidad deportiva Samaria 
Unidad deportiva el Campincito 
Unidad deportiva coliseo de la Luna- sede administrativa canchas de tenis zona urbana
Unidad deportiva concha acustica 
Unidad deportiva el eden 
Unidad deportiva La Balsa
Unidad deportiva Nispero
Unidad deportiva La Fagua
Unidad ', 'OPA 1: Mantenimiento, administracion peticion de prestamo de los parques: 
Unidad deportiva parque paraiso 
Unidad deportiva Bojaca
Unidad deportiva Rio Frio 
Unidad deportiva La Lorena
Unidad deportiva Parque El Cedro
Unidad deportiva Samaria 
Unidad deportiva el Campincito 
Unidad deportiva coliseo de la Luna- sede administrativa canchas de tenis zona urbana
Unidad deportiva concha acustica 
Unidad deportiva el eden 
Unidad deportiva La Balsa
Unidad deportiva Nispero
Unidad deportiva La Fagua
Unidad deportiva La Libertad
Unidad deportiva Lagos de Chia
Campo de Golf
ESCENARIO CULTURAL JUMBO', '200', '202'),
  ('2', 'Escuelas de formacion deportiva', 'OPA 2: Escuelas de formacion deportiva', '200', '202'),
  ('3', 'Deporte social comunitario: Servicio social, juegos comunales', 'OPA 3: Deporte social comunitario: Servicio social, juegos comunales', '200', '202'),
  ('1', 'Retiro de panal de abejas', 'OPA 1: Retiro de panal de abejas', '200', '215'),
  ('1', 'Emite concepto de Plan de Emergencia y Contingencia para eventos masivos y no masivos                                                                                                             correo <EMAIL>', 'OPA 1: Emite concepto de Plan de Emergencia y Contingencia para eventos masivos y no masivos                                                                                                             correo <EMAIL>', '200', '214')
) AS v(code, name, description, dep_code, subdep_code)
JOIN dependencies d ON d.code = v.dep_code
LEFT JOIN subdependencies s ON s.dependency_id = d.id AND s.code = v.subdep_code
WHERE NOT EXISTS (
    SELECT 1 FROM opas o 
    WHERE o.code = v.code 
    AND o.dependency_id = d.id 
    AND (o.subdependency_id = s.id OR (o.subdependency_id IS NULL AND s.id IS NULL))
);

-- Verificar carga exitosa
DO $$
DECLARE
    opa_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO opa_count FROM opas;
    RAISE NOTICE 'Total de OPAs cargados: %', opa_count;
    
    IF opa_count < 721 THEN
        RAISE WARNING 'Se esperaban 721 OPAs pero solo se cargaron %', opa_count;
    ELSE
        RAISE NOTICE '✅ Carga de OPAs completada exitosamente!';
    END IF;
END $$;

COMMIT;

-- Generar reporte final
SELECT 
    'REPORTE FINAL - CARGA DE OPAs' as titulo,
    COUNT(*) as total_opas_cargados,
    COUNT(DISTINCT dependency_id) as dependencias_con_opas,
    COUNT(DISTINCT subdependency_id) as subdependencias_con_opas
FROM opas;
