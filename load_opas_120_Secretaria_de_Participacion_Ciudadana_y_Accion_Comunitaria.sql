-- CARGA OPAs PARA Secretaria de Participacion Ciudadana y Accion Comunitaria (10 OPAs)
BEGIN;

INSERT INTO opas (code, name, description, dependency_id, subdependency_id, is_active)
SELECT v.code, v.name, v.description, d.id, s.id, true
FROM (VALUES
  ('1', 'Politicas publicas de participacion ciudadana', 'OPA 1: Politicas publicas de participacion ciudadana', '120', '120'),
  ('2', 'Organización comunitaria', 'OPA 2: Organización comunitaria', '120', '120'),
  ('3', 'Rendicion de cuentas', 'OPA 3: Rendicion de cuentas', '120', '120'),
  ('4', 'Juntas de accion comunal  (Certificado de representación legal - Presidentes de Junta)', 'OPA 4: Juntas de accion comunal  (Certificado de representación legal - Presidentes de Junta)', '120', '120'),
  ('5', 'Procesos asociativos de organizaciones sociales comunitarias', 'OPA 5: Procesos asociativos de organizaciones sociales comunitarias', '120', '120'),
  ('6', 'Presupuesto participativo', 'OPA 6: Presupuesto participativo', '120', '120'),
  ('7', 'Inspeccion control, vigilancia sobre las organizaciones de accion comunal, Ley 743 de 2002, Decreto 890 de 2008, Decreto compilatorio 1066 de 2015', 'OPA 7: Inspeccion control, vigilancia sobre las organizaciones de accion comunal, Ley 743 de 2002, Decreto 890 de 2008, Decreto compilatorio 1066 de 2015', '120', '120'),
  ('8', 'Plan Indicativo de Accion', 'OPA 8: Plan Indicativo de Accion', '120', '120'),
  ('9', 'Asistencia técnica a Organizaciones de Acción Comunal y servidores públicos en torno a temas de participación ciudadana y acción comunal', 'OPA 9: Asistencia técnica a Organizaciones de Acción Comunal y servidores públicos en torno a temas de participación ciudadana y acción comunal', '120', '120'),
  ('10', 'Programas de formación - educación informal sobre aspectos relevantes para el ejercicio del derecho a la participación ciudadana.', 'OPA 10: Programas de formación - educación informal sobre aspectos relevantes para el ejercicio del derecho a la participación ciudadana.', '120', '120')
) AS v(code, name, description, dep_code, subdep_code)
JOIN dependencies d ON d.code = v.dep_code
LEFT JOIN subdependencies s ON s.dependency_id = d.id AND s.code = v.subdep_code
WHERE NOT EXISTS (
    SELECT 1 FROM opas o 
    WHERE o.code = v.code 
    AND o.dependency_id = d.id 
    AND (o.subdependency_id = s.id OR (o.subdependency_id IS NULL AND s.id IS NULL))
);

-- Verificar carga
SELECT 
    'Secretaria de Participacion Ciudadana y Accion Comunitaria' as dependencia,
    COUNT(*) as opas_cargados
FROM opas o
JOIN dependencies d ON o.dependency_id = d.id
WHERE d.code = '120';

COMMIT;
