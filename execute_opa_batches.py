#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para ejecutar la carga de OPAs en lotes usando la API de Supabase
Divide el archivo SQL en lotes más pequeños para evitar timeouts
"""

import json
import sys
import re
import time

def load_and_execute_opas():
    """Carga todos los OPAs ejecutando lotes directamente"""
    
    try:
        # Leer el archivo JSON
        with open('OPA-chia-optimo.json', 'r', encoding='utf-8') as file:
            data = json.load(file)
        
        print("🚀 Iniciando carga masiva de OPAs...")
        
        # Procesar en lotes más pequeños (20 OPAs por lote)
        batch_size = 20
        current_batch = []
        total_loaded = 0
        batch_number = 1
        
        # Procesar cada dependencia
        for dep_code, dep_data in data['dependencias'].items():
            dep_name = dep_data['nombre']
            
            # Procesar subdependencias
            if 'subdependencias' in dep_data:
                for subdep_code, subdep_data in dep_data['subdependencias'].items():
                    
                    if 'OPA' in subdep_data and subdep_data['OPA']:
                        # Procesar cada OPA
                        for opa in subdep_data['OPA']:
                            opa_code = clean_text(opa['codigo_OPA'])
                            opa_name = clean_text(opa['OPA'])
                            
                            # Crear descripción más detallada
                            opa_description = f"OPA {opa_code}: {opa_name}"
                            
                            # Agregar al lote actual
                            current_batch.append({
                                'code': opa_code,
                                'name': opa_name[:500],  # Limitar longitud
                                'description': opa_description[:1000],  # Limitar longitud
                                'dep_code': dep_code,
                                'subdep_code': subdep_code
                            })
                            
                            # Si el lote está lleno, ejecutar
                            if len(current_batch) >= batch_size:
                                success = execute_batch(current_batch, batch_number)
                                if success:
                                    total_loaded += len(current_batch)
                                    print(f"✅ Lote {batch_number} completado ({len(current_batch)} OPAs)")
                                else:
                                    print(f"❌ Error en lote {batch_number}")
                                    return False
                                
                                current_batch = []
                                batch_number += 1
                                time.sleep(0.5)  # Pausa breve entre lotes
        
        # Procesar el último lote si no está vacío
        if current_batch:
            success = execute_batch(current_batch, batch_number)
            if success:
                total_loaded += len(current_batch)
                print(f"✅ Lote final {batch_number} completado ({len(current_batch)} OPAs)")
            else:
                print(f"❌ Error en lote final {batch_number}")
                return False
        
        print(f"\n🎉 ¡Carga completada exitosamente!")
        print(f"📊 Total OPAs cargados: {total_loaded}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error general: {e}")
        return False

def clean_text(text):
    """Limpia y escapa texto para SQL"""
    if not text:
        return ""
    
    # Reemplazar comillas simples para evitar errores SQL
    text = text.replace("'", "''")
    
    # Limpiar caracteres especiales problemáticos
    text = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', text)
    
    return text.strip()

def execute_batch(batch, batch_number):
    """Ejecuta un lote de OPAs"""
    
    try:
        # Generar SQL para el lote
        values_list = []
        for opa in batch:
            values_list.append(
                f"('{opa['code']}', '{opa['name']}', '{opa['description']}', '{opa['dep_code']}', '{opa['subdep_code']}')"
            )
        
        values_str = ',\n  '.join(values_list)
        
        sql = f"""
INSERT INTO opas (code, name, description, dependency_id, subdependency_id, is_active)
SELECT v.code, v.name, v.description, d.id, s.id, true
FROM (VALUES
  {values_str}
) AS v(code, name, description, dep_code, subdep_code)
JOIN dependencies d ON d.code = v.dep_code
LEFT JOIN subdependencies s ON s.dependency_id = d.id AND s.code = v.subdep_code
WHERE NOT EXISTS (
    SELECT 1 FROM opas o 
    WHERE o.code = v.code 
    AND o.dependency_id = d.id 
    AND (o.subdependency_id = s.id OR (o.subdependency_id IS NULL AND s.id IS NULL))
);
"""
        
        # Guardar SQL del lote para debugging
        with open(f'batch_{batch_number:03d}.sql', 'w', encoding='utf-8') as f:
            f.write(sql)
        
        print(f"🔄 Ejecutando lote {batch_number} ({len(batch)} OPAs)...")
        
        # Aquí normalmente ejecutaríamos el SQL, pero como estamos usando Python
        # vamos a generar los archivos SQL para ejecución manual
        return True
        
    except Exception as e:
        print(f"❌ Error en lote {batch_number}: {e}")
        return False

if __name__ == "__main__":
    print("📋 Generando lotes SQL para carga manual...")
    success = load_and_execute_opas()
    
    if success:
        print("\n🚀 Archivos SQL generados exitosamente!")
        print("📁 Ejecutar manualmente cada archivo batch_XXX.sql en Supabase")
    else:
        print("❌ Error en la generación")
        sys.exit(1)
