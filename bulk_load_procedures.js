const fs = require('fs');

// Leer archivo JSON de trámites
const tramites = JSON.parse(fs.readFileSync('tramites_chia_optimo.json', 'utf8'));

// Función para escapar comillas simples en SQL
function escapeSql(str) {
  if (!str) return '';
  return str.replace(/'/g, "''");
}

// Función para parsear el costo
function parseCost(costoStr) {
  if (!costoStr) return null;
  
  // Si contiene 'gratuito' o 'sin costo', es 0
  if (costoStr.toLowerCase().includes('gratuito') || 
      costoStr.toLowerCase().includes('sin costo') ||
      costoStr.toLowerCase().includes('no tiene costo') ||
      costoStr.toLowerCase().includes('no')) {
    return 0;
  }
  
  // Si contiene números, extraer el primer número
  const match = costoStr.match(/\d+/);
  if (match) {
    return parseInt(match[0]);
  }
  
  // Si no se puede determinar, devolver null (costo variable)
  return null;
}

// Generar lotes de 20 trámites
const batchSize = 20;
const totalBatches = Math.ceil(tramites.length / batchSize);

console.log('-- CARGAR TODOS LOS TRÁMITES EN LOTES');
console.log('-- Total trámites:', tramites.length);
console.log('-- Tamaño de lote:', batchSize);
console.log('-- Total lotes:', totalBatches);
console.log('');

for (let batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
  const startIndex = batchIndex * batchSize;
  const endIndex = Math.min(startIndex + batchSize, tramites.length);
  const batch = tramites.slice(startIndex, endIndex);
  
  console.log(`-- LOTE ${batchIndex + 1} (trámites ${startIndex + 1} al ${endIndex})`);
  console.log('BEGIN;');
  
  batch.forEach((tramite, index) => {
    const nombre = escapeSql(tramite.Nombre || '');
    const tiempoRespuesta = escapeSql(tramite['Tiempo de respuesta'] || '');
    const costo = parseCost(tramite['¿Tiene pago?']);
    const codigoDep = tramite.codigo_dependencia;
    const codigoSub = tramite.codigo_subdependencia;
    
    console.log('INSERT INTO procedures (name, description, response_time, cost, dependency_id, subdependency_id, is_active)');
    console.log('SELECT');
    console.log('  \'' + nombre + '\',');
    console.log('  \'' + nombre + '\',');
    console.log('  \'' + tiempoRespuesta + '\',');
    console.log('  ' + (costo !== null ? costo : 'NULL') + ',');
    console.log('  d.id,');
    console.log('  s.id,');
    console.log('  true');
    console.log('FROM dependencies d');
    console.log('LEFT JOIN subdependencies s ON s.dependency_id = d.id AND s.code = \'' + codigoSub + '\'');
    console.log('WHERE d.code = \'' + codigoDep + '\'');
    console.log('AND NOT EXISTS (SELECT 1 FROM procedures p WHERE p.name = \'' + nombre + '\');');
    console.log('');
  });
  
  console.log('COMMIT;');
  console.log('');
}

console.log('-- Verificar carga final');
console.log('SELECT COUNT(*) as total_procedures FROM procedures;');
