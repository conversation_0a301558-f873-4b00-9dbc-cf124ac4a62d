-- =====================================================
-- SCRIPT MAESTRO - CARGA COMPLETA DE OPAs
-- Total de OPAs: 721
-- =====================================================

-- Verificar estado inicial
SELECT 
    'ESTADO INICIAL' as estado,
    COUNT(*) as opas_existentes
FROM opas;

-- Estadísticas por dependencia ANTES de la carga
SELECT 
    d.name as dependencia,
    COUNT(o.id) as opas_actuales
FROM dependencies d
LEFT JOIN opas o ON o.dependency_id = d.id
GROUP BY d.id, d.name
ORDER BY d.name;


-- Ejecutar: load_opas_000_Despacho_Alcalde.sql
-- \i load_opas_000_Despacho_Alcalde.sql

-- Ejecutar: load_opas_010_Secretaria_de_Planeacion.sql
-- \i load_opas_010_Secretaria_de_Planeacion.sql

-- Ejecutar: load_opas_020_Secretaria_General.sql
-- \i load_opas_020_Secretaria_General.sql

-- Ejecutar: load_opas_030_Secretaria_de_Gobierno.sql
-- \i load_opas_030_Secretaria_de_Gobierno.sql

-- Ejecutar: load_opas_040_Secretaria_de_Hacienda.sql
-- \i load_opas_040_Secretaria_de_Hacienda.sql

-- Ejecutar: load_opas_050_Secretaria_de_Obras_Publicas.sql
-- \i load_opas_050_Secretaria_de_Obras_Publicas.sql

-- Ejecutar: load_opas_060_Secretaria_de_Desarrollo_Social.sql
-- \i load_opas_060_Secretaria_de_Desarrollo_Social.sql

-- Ejecutar: load_opas_070_Secretaria_de_Educacion.sql
-- \i load_opas_070_Secretaria_de_Educacion.sql

-- Ejecutar: load_opas_080_Secretaria_de_Salud.sql
-- \i load_opas_080_Secretaria_de_Salud.sql

-- Ejecutar: load_opas_090_Secretaria_para_el_Desarrollo_Economico.sql
-- \i load_opas_090_Secretaria_para_el_Desarrollo_Economico.sql

-- Ejecutar: load_opas_100_Secretaria_de_Medio_Ambiente.sql
-- \i load_opas_100_Secretaria_de_Medio_Ambiente.sql

-- Ejecutar: load_opas_110_Secretaria_de_Movilidad.sql
-- \i load_opas_110_Secretaria_de_Movilidad.sql

-- Ejecutar: load_opas_120_Secretaria_de_Participacion_Ciudadana_y_Accion_Comunitaria.sql
-- \i load_opas_120_Secretaria_de_Participacion_Ciudadana_y_Accion_Comunitaria.sql

-- Ejecutar: load_opas_200_Descentralizados.sql
-- \i load_opas_200_Descentralizados.sql


-- VERIFICACIÓN FINAL
SELECT 
    'ESTADO FINAL' as estado,
    COUNT(*) as total_opas_cargados
FROM opas;

-- Estadísticas finales por dependencia
SELECT 
    d.name as dependencia,
    COUNT(o.id) as opas_cargados,
    ROUND(COUNT(o.id) * 100.0 / 721, 2) as porcentaje
FROM dependencies d
LEFT JOIN opas o ON o.dependency_id = d.id
WHERE o.id IS NOT NULL
GROUP BY d.id, d.name
ORDER BY opas_cargados DESC;

-- Resumen final
SELECT 
    COUNT(*) as total_opas,
    COUNT(DISTINCT dependency_id) as dependencias_con_opas,
    COUNT(DISTINCT subdependency_id) as subdependencias_con_opas,
    ROUND(AVG(LENGTH(name)), 2) as longitud_promedio_nombre
FROM opas;
