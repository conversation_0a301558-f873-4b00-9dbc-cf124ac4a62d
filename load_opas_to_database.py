#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para cargar todos los OPAs desde OPA-chia-optimo.json a la base de datos Supabase
Genera archivos SQL para carga por lotes
"""

import json
import sys
import re

def clean_text(text):
    """Limpia y escapa texto para SQL"""
    if not text:
        return ""
    
    # Reemplazar comillas simples para evitar errores SQL
    text = text.replace("'", "''")
    
    # Limpiar caracteres especiales problemáticos
    text = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', text)
    
    return text.strip()

def generate_opa_sql():
    """Genera SQL para cargar todos los OPAs"""
    
    try:
        # Leer el archivo JSON
        with open('OPA-chia-optimo.json', 'r', encoding='utf-8') as file:
            data = json.load(file)
        
        sql_statements = []
        total_opas = 0
        batch_size = 50  # Procesar en lotes de 50
        current_batch = []
        
        print("🔄 Procesando OPAs para carga en base de datos...")
        
        # Procesar cada dependencia
        for dep_code, dep_data in data['dependencias'].items():
            dep_name = dep_data['nombre']
            print(f"📁 Procesando {dep_code}: {dep_name}")
            
            # Procesar subdependencias
            if 'subdependencias' in dep_data:
                for subdep_code, subdep_data in dep_data['subdependencias'].items():
                    subdep_name = subdep_data['nombre']
                    
                    if 'OPA' in subdep_data and subdep_data['OPA']:
                        print(f"  └── {subdep_code}: {subdep_name} ({len(subdep_data['OPA'])} OPAs)")
                        
                        # Procesar cada OPA
                        for opa in subdep_data['OPA']:
                            opa_code = clean_text(opa['codigo_OPA'])
                            opa_name = clean_text(opa['OPA'])
                            
                            # Crear descripción más detallada
                            opa_description = f"OPA {opa_code}: {opa_name}"
                            
                            # Agregar al lote actual
                            current_batch.append({
                                'code': opa_code,
                                'name': opa_name[:500],  # Limitar longitud
                                'description': opa_description[:1000],  # Limitar longitud
                                'dep_code': dep_code,
                                'subdep_code': subdep_code
                            })
                            
                            total_opas += 1
                            
                            # Si el lote está lleno, generar SQL
                            if len(current_batch) >= batch_size:
                                sql_statements.append(generate_batch_sql(current_batch))
                                current_batch = []
        
        # Procesar el último lote si no está vacío
        if current_batch:
            sql_statements.append(generate_batch_sql(current_batch))
        
        # Generar archivo SQL completo
        full_sql = generate_full_sql(sql_statements, total_opas)
        
        # Guardar archivo SQL
        with open('load_all_opas.sql', 'w', encoding='utf-8') as f:
            f.write(full_sql)
        
        print(f"\n✅ Procesamiento completado!")
        print(f"📊 Total OPAs procesados: {total_opas}")
        print(f"📄 Archivo generado: load_all_opas.sql")
        print(f"🔢 Lotes SQL generados: {len(sql_statements)}")
        
        return total_opas
        
    except Exception as e:
        print(f"❌ Error al procesar OPAs: {e}")
        return 0

def generate_batch_sql(batch):
    """Genera SQL para un lote de OPAs"""
    
    values_list = []
    for opa in batch:
        values_list.append(
            f"('{opa['code']}', '{opa['name']}', '{opa['description']}', '{opa['dep_code']}', '{opa['subdep_code']}')"
        )
    
    values_str = ',\n  '.join(values_list)
    
    sql = f"""
-- LOTE DE {len(batch)} OPAs
INSERT INTO opas (code, name, description, dependency_id, subdependency_id, is_active)
SELECT v.code, v.name, v.description, d.id, s.id, true
FROM (VALUES
  {values_str}
) AS v(code, name, description, dep_code, subdep_code)
JOIN dependencies d ON d.code = v.dep_code
LEFT JOIN subdependencies s ON s.dependency_id = d.id AND s.code = v.subdep_code
WHERE NOT EXISTS (
    SELECT 1 FROM opas o 
    WHERE o.code = v.code 
    AND o.dependency_id = d.id 
    AND (o.subdependency_id = s.id OR (o.subdependency_id IS NULL AND s.id IS NULL))
);
"""
    return sql

def generate_full_sql(sql_statements, total_opas):
    """Genera el archivo SQL completo"""
    
    header = f"""-- =====================================================
-- CARGA MASIVA DE OPAs (OTROS PROCEDIMIENTOS ADMINISTRATIVOS)
-- Total de OPAs a cargar: {total_opas}
-- Fecha de generación: $(date)
-- =====================================================

BEGIN;

-- Verificar que existen las tablas necesarias
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'opas') THEN
        RAISE EXCEPTION 'La tabla opas no existe. Ejecutar primero el script de creación de esquema.';
    END IF;
END $$;

"""
    
    footer = f"""
-- Verificar carga exitosa
DO $$
DECLARE
    opa_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO opa_count FROM opas;
    RAISE NOTICE 'Total de OPAs cargados: %', opa_count;
    
    IF opa_count < {total_opas} THEN
        RAISE WARNING 'Se esperaban {total_opas} OPAs pero solo se cargaron %', opa_count;
    ELSE
        RAISE NOTICE '✅ Carga de OPAs completada exitosamente!';
    END IF;
END $$;

COMMIT;

-- Generar reporte final
SELECT 
    'REPORTE FINAL - CARGA DE OPAs' as titulo,
    COUNT(*) as total_opas_cargados,
    COUNT(DISTINCT dependency_id) as dependencias_con_opas,
    COUNT(DISTINCT subdependency_id) as subdependencias_con_opas
FROM opas;
"""
    
    # Combinar todo
    full_sql = header + '\n'.join(sql_statements) + footer
    
    return full_sql

if __name__ == "__main__":
    total = generate_opa_sql()
    if total > 0:
        print(f"\n🚀 Listo para ejecutar: load_all_opas.sql")
        print(f"📋 Comando sugerido: Ejecutar el archivo SQL en Supabase")
    else:
        print("❌ Error en el procesamiento")
        sys.exit(1)
