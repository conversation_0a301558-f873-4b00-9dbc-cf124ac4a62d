# Configuración de Supabase para Sistema de Atención Ciudadana

## Información General

**Proyecto:** Sistema de Atención Ciudadana - Chía  
**Versión:** 1.0  
**Fecha:** 2025-06-30  

## 1. Configuración Inicial del Proyecto

### 1.1 Creación del Proyecto

```bash
# Crear nuevo proyecto en Supabase
# Nombre: atencion-ciudadana-chia
# Región: South America (São Paulo) - sa-east-1
# Plan: Pro (recomendado para producción)
```

### 1.2 Configuración de Base de Datos

```sql
-- Ejecutar en el SQL Editor de Supabase
-- 1. Crear extensiones necesarias
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";
CREATE EXTENSION IF NOT EXISTS "vector";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "btree_gist";

-- 2. Ejecutar DATABASE_SCHEMA.sql
-- 3. Ejecutar RLS_POLICIES.sql
```

### 1.3 Configuración de Autenticación

```json
{
  "site_url": "https://atencion.chia.gov.co",
  "redirect_urls": [
    "https://atencion.chia.gov.co/auth/callback",
    "https://staging-atencion.chia.gov.co/auth/callback",
    "http://localhost:3000/auth/callback"
  ],
  "jwt_expiry": 3600,
  "refresh_token_rotation_enabled": true,
  "security_update_password_require_reauthentication": true
}
```

## 2. Edge Functions

### 2.1 Función de Chat con IA

```typescript
// supabase/functions/chat-ai/index.ts
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      {
        global: {
          headers: { Authorization: req.headers.get('Authorization')! },
        },
      }
    )

    const { message, context, conversation_id } = await req.json()

    // Obtener contexto relevante usando búsqueda vectorial
    const { data: knowledgeData } = await supabaseClient.rpc('search_knowledge_base', {
      query_text: message,
      match_threshold: 0.7,
      match_count: 5
    })

    // Procesar con OpenAI
    const openaiResponse = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${Deno.env.get('OPENAI_API_KEY')}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4-turbo-preview',
        messages: [
          {
            role: 'system',
            content: `Eres un asistente virtual del municipio de Chía, Colombia. 
            Ayudas a los ciudadanos con información sobre trámites y servicios.
            Contexto relevante: ${JSON.stringify(knowledgeData)}`
          },
          { role: 'user', content: message }
        ],
        temperature: 0.7,
        max_tokens: 1000
      })
    })

    const aiResponse = await openaiResponse.json()
    const assistantMessage = aiResponse.choices[0].message.content

    // Guardar conversación
    await supabaseClient.from('chat_messages').insert([
      {
        conversation_id,
        role: 'user',
        content: message,
        tokens_used: aiResponse.usage?.prompt_tokens || 0
      },
      {
        conversation_id,
        role: 'assistant',
        content: assistantMessage,
        tokens_used: aiResponse.usage?.completion_tokens || 0,
        response_time_ms: Date.now() - startTime
      }
    ])

    return new Response(
      JSON.stringify({ 
        response: assistantMessage,
        sources: knowledgeData?.map(item => ({
          title: item.title,
          source_type: item.source_type
        })) || []
      }),
      { 
        headers: { 
          ...corsHeaders,
          'Content-Type': 'application/json' 
        } 
      }
    )

  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      { 
        status: 500,
        headers: { 
          ...corsHeaders,
          'Content-Type': 'application/json' 
        } 
      }
    )
  }
})
```

### 2.2 Función de Búsqueda Semántica

```sql
-- Función para búsqueda vectorial
CREATE OR REPLACE FUNCTION search_knowledge_base(
  query_text TEXT,
  match_threshold FLOAT DEFAULT 0.7,
  match_count INT DEFAULT 5
)
RETURNS TABLE (
  id UUID,
  title TEXT,
  content TEXT,
  source_type TEXT,
  similarity FLOAT
)
LANGUAGE plpgsql
AS $$
DECLARE
  query_embedding vector(1536);
BEGIN
  -- Generar embedding del query (esto se haría desde la Edge Function)
  -- Por ahora retornamos búsqueda por texto
  RETURN QUERY
  SELECT 
    kb.id,
    kb.title,
    kb.content,
    kb.source_type,
    ts_rank(to_tsvector('spanish', kb.content), plainto_tsquery('spanish', query_text)) as similarity
  FROM knowledge_base kb
  WHERE 
    kb.is_active = true
    AND to_tsvector('spanish', kb.content) @@ plainto_tsquery('spanish', query_text)
  ORDER BY similarity DESC
  LIMIT match_count;
END;
$$;
```

### 2.3 Función de Procesamiento de Documentos

```typescript
// supabase/functions/process-documents/index.ts
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

serve(async (req) => {
  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    const { file_path, source_type, source_id } = await req.json()

    // Descargar archivo desde Storage
    const { data: fileData } = await supabaseClient.storage
      .from('documents')
      .download(file_path)

    if (!fileData) {
      throw new Error('Archivo no encontrado')
    }

    // Extraer texto del archivo (implementar según tipo)
    let extractedText = ''
    
    if (file_path.endsWith('.pdf')) {
      // Procesar PDF (usar librería de extracción de texto)
      extractedText = await extractTextFromPDF(fileData)
    } else if (file_path.endsWith('.txt')) {
      extractedText = await fileData.text()
    }

    // Dividir texto en chunks
    const chunks = splitTextIntoChunks(extractedText, 1000)

    // Generar embeddings para cada chunk
    for (const chunk of chunks) {
      const embeddingResponse = await fetch('https://api.openai.com/v1/embeddings', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${Deno.env.get('OPENAI_API_KEY')}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: 'text-embedding-ada-002',
          input: chunk
        })
      })

      const embeddingData = await embeddingResponse.json()
      const embedding = embeddingData.data[0].embedding

      // Guardar en knowledge_base
      await supabaseClient.from('knowledge_base').insert({
        title: `Documento: ${file_path}`,
        content: chunk,
        source_type,
        source_id,
        embedding,
        metadata: { file_path, chunk_index: chunks.indexOf(chunk) }
      })
    }

    return new Response(
      JSON.stringify({ success: true, chunks_processed: chunks.length }),
      { headers: { 'Content-Type': 'application/json' } }
    )

  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    )
  }
})

function splitTextIntoChunks(text: string, chunkSize: number): string[] {
  const chunks = []
  for (let i = 0; i < text.length; i += chunkSize) {
    chunks.push(text.slice(i, i + chunkSize))
  }
  return chunks
}
```

## 3. Configuración de Storage

### 3.1 Buckets de Almacenamiento

```sql
-- Crear buckets necesarios
INSERT INTO storage.buckets (id, name, public) VALUES 
('documents', 'documents', false),
('avatars', 'avatars', true),
('procedure-attachments', 'procedure-attachments', false);
```

### 3.2 Políticas de Storage

```sql
-- Políticas para documentos
CREATE POLICY "Users can upload their own documents" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'documents' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can view their own documents" ON storage.objects
  FOR SELECT USING (
    bucket_id = 'documents' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

-- Políticas para avatares (públicos)
CREATE POLICY "Anyone can view avatars" ON storage.objects
  FOR SELECT USING (bucket_id = 'avatars');

CREATE POLICY "Users can upload their own avatar" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'avatars' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

-- Políticas para adjuntos de trámites
CREATE POLICY "Users can upload procedure attachments" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'procedure-attachments' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Admins can view all procedure attachments" ON storage.objects
  FOR SELECT USING (
    bucket_id = 'procedure-attachments' AND
    (
      auth.uid()::text = (storage.foldername(name))[1] OR
      EXISTS (
        SELECT 1 FROM profiles p
        JOIN roles r ON p.role_id = r.id
        WHERE p.id = auth.uid()
        AND r.name IN ('admin', 'super_admin')
      )
    )
  );
```

## 4. Configuración de Realtime

### 4.1 Habilitar Realtime en Tablas

```sql
-- Habilitar realtime para notificaciones
ALTER PUBLICATION supabase_realtime ADD TABLE notifications;

-- Habilitar realtime para estado de trámites
ALTER PUBLICATION supabase_realtime ADD TABLE citizen_procedures;
ALTER PUBLICATION supabase_realtime ADD TABLE procedure_status_history;

-- Habilitar realtime para chat
ALTER PUBLICATION supabase_realtime ADD TABLE chat_messages;
```

### 4.2 Configuración de Canales

```typescript
// Configuración del cliente para Realtime
const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  realtime: {
    params: {
      eventsPerSecond: 10
    }
  }
})

// Suscripción a notificaciones del usuario
const notificationsChannel = supabase
  .channel('user-notifications')
  .on(
    'postgres_changes',
    {
      event: 'INSERT',
      schema: 'public',
      table: 'notifications',
      filter: `user_id=eq.${userId}`
    },
    (payload) => {
      // Manejar nueva notificación
      showNotification(payload.new)
    }
  )
  .subscribe()

// Suscripción a cambios de estado de trámites
const procedureUpdatesChannel = supabase
  .channel('procedure-updates')
  .on(
    'postgres_changes',
    {
      event: 'UPDATE',
      schema: 'public',
      table: 'citizen_procedures',
      filter: `citizen_id=eq.${userId}`
    },
    (payload) => {
      // Manejar actualización de trámite
      updateProcedureStatus(payload.new)
    }
  )
  .subscribe()
```

## 5. Configuración de Webhooks

### 5.1 Webhook para Nuevos Usuarios

```sql
-- Función para crear perfil automáticamente
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO profiles (id, email, role_id)
  VALUES (
    NEW.id,
    NEW.email,
    (SELECT id FROM roles WHERE name = 'citizen' LIMIT 1)
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger para nuevos usuarios
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION handle_new_user();
```

### 5.2 Webhook para Notificaciones por Email

```typescript
// supabase/functions/send-email-notification/index.ts
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

serve(async (req) => {
  try {
    const { record } = await req.json()
    
    // Obtener datos del usuario
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    const { data: user } = await supabaseClient
      .from('profiles')
      .select('email, full_name')
      .eq('id', record.user_id)
      .single()

    if (!user) return new Response('User not found', { status: 404 })

    // Enviar email usando Resend o similar
    const emailResponse = await fetch('https://api.resend.com/emails', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${Deno.env.get('RESEND_API_KEY')}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        from: 'Sistema de Atención Ciudadana <<EMAIL>>',
        to: [user.email],
        subject: record.title,
        html: `
          <h2>Hola ${user.full_name}</h2>
          <p>${record.message}</p>
          <p>Puedes revisar más detalles en tu <a href="https://atencion.chia.gov.co/dashboard">panel de usuario</a>.</p>
        `
      })
    })

    if (emailResponse.ok) {
      // Marcar notificación como enviada
      await supabaseClient
        .from('notifications')
        .update({ sent_at: new Date().toISOString() })
        .eq('id', record.id)
    }

    return new Response('OK')

  } catch (error) {
    return new Response(error.message, { status: 500 })
  }
})
```

## 6. Configuración de Seguridad

### 6.1 Configuración de CORS

```json
{
  "allowed_origins": [
    "https://atencion.chia.gov.co",
    "https://staging-atencion.chia.gov.co",
    "http://localhost:3000"
  ],
  "allowed_methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
  "allowed_headers": ["authorization", "x-client-info", "apikey", "content-type"]
}
```

### 6.2 Rate Limiting

```sql
-- Configurar rate limiting en Edge Functions
-- Esto se configura a nivel de proyecto en Supabase Dashboard
-- Límites recomendados:
-- - API calls: 100 requests/minute por IP
-- - Auth operations: 10 requests/minute por IP
-- - File uploads: 5 requests/minute por usuario
```

## 7. Monitoreo y Logs

### 7.1 Configuración de Logs

```sql
-- Función para logging personalizado
CREATE OR REPLACE FUNCTION log_application_event(
  event_type TEXT,
  event_data JSONB DEFAULT '{}'
)
RETURNS VOID AS $$
BEGIN
  INSERT INTO audit_logs (
    user_id,
    action,
    table_name,
    new_values,
    created_at
  ) VALUES (
    auth.uid(),
    event_type,
    'application_events',
    event_data,
    NOW()
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

### 7.2 Métricas Personalizadas

```typescript
// Función para tracking de métricas
export async function trackEvent(eventName: string, properties: Record<string, any>) {
  await supabase.rpc('log_application_event', {
    event_type: eventName,
    event_data: properties
  })
}

// Ejemplos de uso
trackEvent('chat_message_sent', { message_length: message.length })
trackEvent('procedure_started', { procedure_id: procedureId })
trackEvent('search_performed', { query: searchQuery, results_count: results.length })
```

## 8. Backup y Recuperación

### 8.1 Configuración de Backups Automáticos

```bash
# Los backups automáticos están habilitados por defecto en Supabase Pro
# Configuración recomendada:
# - Backup diario automático
# - Retención de 30 días
# - Backup manual antes de deployments importantes
```

### 8.2 Exportación de Datos

```sql
-- Script para exportar datos críticos
COPY (
  SELECT * FROM profiles 
  WHERE created_at >= NOW() - INTERVAL '30 days'
) TO '/tmp/profiles_backup.csv' WITH CSV HEADER;

COPY (
  SELECT * FROM citizen_procedures 
  WHERE created_at >= NOW() - INTERVAL '30 days'
) TO '/tmp/procedures_backup.csv' WITH CSV HEADER;
```

## 9. Variables de Entorno para Supabase

```bash
# Variables requeridas en el proyecto
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Variables para Edge Functions
OPENAI_API_KEY=your-openai-key
RESEND_API_KEY=your-resend-key
WEBHOOK_SECRET=your-webhook-secret
```

## 10. Comandos Útiles

### 10.1 Supabase CLI

```bash
# Instalar Supabase CLI
npm install -g supabase

# Login
supabase login

# Inicializar proyecto local
supabase init

# Ejecutar localmente
supabase start

# Deploy de Edge Functions
supabase functions deploy chat-ai

# Generar tipos TypeScript
supabase gen types typescript --project-id your-project-id > types/supabase.ts
```

### 10.2 Comandos SQL Útiles

```sql
-- Ver uso de la base de datos
SELECT 
  schemaname,
  tablename,
  pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

-- Ver conexiones activas
SELECT count(*) FROM pg_stat_activity;

-- Ver queries lentas
SELECT query, mean_exec_time, calls 
FROM pg_stat_statements 
ORDER BY mean_exec_time DESC 
LIMIT 10;
```
