-- CARGA OPAs PARA Secretaria de Obras Publicas (39 OPAs)
BEGIN;

INSERT INTO opas (code, name, description, dependency_id, subdependency_id, is_active)
SELECT v.code, v.name, v.description, d.id, s.id, true
FROM (VALUES
  ('1', 'Banco de materiales', 'OPA 1: Banco de materiales', '050', '050'),
  ('2', 'demolición de muros', 'OPA 2: demolición de muros', '050', '050'),
  ('3', 'señalización y aislamiento de obras', 'OPA 3: señalización y aislamiento de obras', '050', '050'),
  ('4', 'Poda de Parques y manzanas institucionales', 'OPA 4: Poda de Parques y manzanas institucionales', '050', '050'),
  ('5', 'materialización de sanción demolición', 'OPA 5: materialización de sanción demolición', '050', '050'),
  ('6', 'Indicador de Producción de Obras Civiles IPOC del DANE', 'OPA 6: Indicador de Producción de Obras Civiles IPOC del DANE', '050', '050'),
  ('1', 'Infrastructura vial y obras generales del municipio.', 'OPA 1: Infrastructura vial y obras generales del municipio.', '050', '051'),
  ('2', 'Coordinación y gestión de redes de servicios públicos', 'OPA 2: Coordinación y gestión de redes de servicios públicos', '050', '051'),
  ('3', 'Red vial del municipio.', 'OPA 3: Red vial del municipio.', '050', '051'),
  ('4', 'Mantenimiento de edificios públicos', 'OPA 4: Mantenimiento de edificios públicos', '050', '051'),
  ('5', 'Adecuación de edificios publicos', 'OPA 5: Adecuación de edificios publicos', '050', '051'),
  ('6', 'Supervisión de obras de infrastructura', 'OPA 6: Supervisión de obras de infrastructura', '050', '051'),
  ('7', 'interventorias de obras de infrastructura', 'OPA 7: interventorias de obras de infrastructura', '050', '051'),
  ('8', 'Metodologías del sistema de concesión de obras o servicios', 'OPA 8: Metodologías del sistema de concesión de obras o servicios', '050', '051'),
  ('9', 'Infraestructura comunitaria y asociativa', 'OPA 9: Infraestructura comunitaria y asociativa', '050', '051'),
  ('10', 'Materiales usados en proyectos', 'OPA 10: Materiales usados en proyectos', '050', '051'),
  ('11', 'Informes de estado de las obras', 'OPA 11: Informes de estado de las obras', '050', '051'),
  ('12', 'obras inconclusas', 'OPA 12: obras inconclusas', '050', '051'),
  ('13', 'Administración de maquinaria, equipo y herramienta menor del municipio', 'OPA 13: Administración de maquinaria, equipo y herramienta menor del municipio', '050', '051'),
  ('14', 'Adquisición de repuestos y/o herramienta menor para la maQuinaria del municipio', 'OPA 14: Adquisición de repuestos y/o herramienta menor para la maQuinaria del municipio', '050', '051'),
  ('15', 'Novedades y mantenimiento preventivo de maquinaria', 'OPA 15: Novedades y mantenimiento preventivo de maquinaria', '050', '051'),
  ('16', 'Suministro y consumos para maquinaria', 'OPA 16: Suministro y consumos para maquinaria', '050', '051'),
  ('17', 'Andenes', 'OPA 17: Andenes', '050', '051'),
  ('18', 'mantenimiento de estructura puentes', 'OPA 18: mantenimiento de estructura puentes', '050', '051'),
  ('19', 'visitas', 'OPA 19: visitas', '050', '051'),
  ('20', 'Intervención vía', 'OPA 20: Intervención vía', '050', '051'),
  ('21', 'ajuste de fachadas', 'OPA 21: ajuste de fachadas', '050', '051'),
  ('22', 'reparaciones locativas(lamparas, baños, infiltraciones)', 'OPA 22: reparaciones locativas(lamparas, baños, infiltraciones)', '050', '051'),
  ('23', 'Informe sobre estado de bienes de interes cultural', 'OPA 23: Informe sobre estado de bienes de interes cultural', '050', '051'),
  ('1', 'Etapa precontractual y ejecución de contratos de la dependencia', 'OPA 1: Etapa precontractual y ejecución de contratos de la dependencia', '050', '052'),
  ('2', 'Estudios técnicos diseño y estructura de pavimientos', 'OPA 2: Estudios técnicos diseño y estructura de pavimientos', '050', '052'),
  ('3', 'Estudios de suelos y geotécnicos', 'OPA 3: Estudios de suelos y geotécnicos', '050', '052'),
  ('4', 'Diseño red vial del municipio', 'OPA 4: Diseño red vial del municipio', '050', '052'),
  ('5', 'Estudios y diseños de las obras', 'OPA 5: Estudios y diseños de las obras', '050', '052'),
  ('6', 'Documentación obras', 'OPA 6: Documentación obras', '050', '052'),
  ('7', 'Estudios proyecto de integracion regional capital., Bogotá - Cundinamarca', 'OPA 7: Estudios proyecto de integracion regional capital., Bogotá - Cundinamarca', '050', '052'),
  ('1', 'Sistema de valorización', 'OPA 1: Sistema de valorización', '050', '053'),
  ('2', 'Liquidación de valorización', 'OPA 2: Liquidación de valorización', '050', '053'),
  ('3', 'Atención a contribuyentes en temas de valorización', 'OPA 3: Atención a contribuyentes en temas de valorización', '050', '053')
) AS v(code, name, description, dep_code, subdep_code)
JOIN dependencies d ON d.code = v.dep_code
LEFT JOIN subdependencies s ON s.dependency_id = d.id AND s.code = v.subdep_code
WHERE NOT EXISTS (
    SELECT 1 FROM opas o 
    WHERE o.code = v.code 
    AND o.dependency_id = d.id 
    AND (o.subdependency_id = s.id OR (o.subdependency_id IS NULL AND s.id IS NULL))
);

-- Verificar carga
SELECT 
    'Secretaria de Obras Publicas' as dependencia,
    COUNT(*) as opas_cargados
FROM opas o
JOIN dependencies d ON o.dependency_id = d.id
WHERE d.code = '050';

COMMIT;
