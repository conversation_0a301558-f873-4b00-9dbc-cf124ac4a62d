const fs = require('fs');

// Leer archivo JSON de trámites
const tramites = JSON.parse(fs.readFileSync('tramites_chia_optimo.json', 'utf8'));

// Función para escapar comillas simples en SQL
function escapeSql(str) {
  if (!str) return '';
  return str.replace(/'/g, "''");
}

// Función para parsear el costo
function parseCost(costoStr) {
  if (!costoStr) return null;
  
  // Si contiene 'gratuito' o 'sin costo', es 0
  if (costoStr.toLowerCase().includes('gratuito') || 
      costoStr.toLowerCase().includes('sin costo') ||
      costoStr.toLowerCase().includes('no tiene costo') ||
      costoStr.toLowerCase().includes('no')) {
    return 0;
  }
  
  // Si contiene números, extraer el primer número
  const match = costoStr.match(/\d+/);
  if (match) {
    return parseInt(match[0]);
  }
  
  // Si no se puede determinar, devolver null (costo variable)
  return null;
}

console.log('-- INSERTAR TRÁMITES EN LA BASE DE DATOS');
console.log('-- Total de trámites a procesar:', tramites.length);
console.log('');

// Procesar en lotes de 10 trámites
for (let i = 0; i < tramites.length; i += 10) {
  const lote = tramites.slice(i, i + 10);
  
  console.log('-- LOTE', Math.floor(i/10) + 1, '(trámites', i+1, 'al', Math.min(i+10, tramites.length), ')');
  
  lote.forEach((tramite, index) => {
    const nombre = escapeSql(tramite.Nombre || '');
    const tiempoRespuesta = escapeSql(tramite['Tiempo de respuesta'] || '');
    const costo = parseCost(tramite['¿Tiene pago?']);
    const codigoDep = tramite.codigo_dependencia;
    const codigoSub = tramite.codigo_subdependencia;
    
    console.log('INSERT INTO procedures (name, description, response_time, cost, dependency_id, subdependency_id, is_active)');
    console.log('SELECT');
    console.log('  \'' + nombre + '\',');
    console.log('  \'' + nombre + '\',');
    console.log('  \'' + tiempoRespuesta + '\',');
    console.log('  ' + (costo !== null ? costo : 'NULL') + ',');
    console.log('  d.id,');
    console.log('  s.id,');
    console.log('  true');
    console.log('FROM dependencies d');
    console.log('LEFT JOIN subdependencies s ON s.dependency_id = d.id AND s.code = \'' + codigoSub + '\'');
    console.log('WHERE d.code = \'' + codigoDep + '\';');
    console.log('');
  });
  
  console.log('');
}

console.log('-- ESTADÍSTICAS DE PROCESAMIENTO:');
console.log('-- Total de trámites procesados:', tramites.length);

// Estadísticas de costos
let gratuitos = 0;
let conCosto = 0;
let costoVariable = 0;

tramites.forEach(tramite => {
  const costo = parseCost(tramite['¿Tiene pago?']);
  if (costo === 0) gratuitos++;
  else if (costo > 0) conCosto++;
  else costoVariable++;
});

console.log('-- Trámites gratuitos:', gratuitos);
console.log('-- Trámites con costo fijo:', conCosto);
console.log('-- Trámites con costo variable:', costoVariable);
