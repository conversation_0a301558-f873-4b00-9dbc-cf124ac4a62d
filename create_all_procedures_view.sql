-- =====================================================
-- CREACIÓN DE LA VISTA UNIFICADA all_procedures
-- Esta vista combina trámites (procedures) y OPAs (administrative_procedures)
-- Fecha: 2025-01-01
-- =====================================================

-- Primero, verificar si necesitamos crear la tabla opas como alias de administrative_procedures
-- Los archivos de carga usan 'opas' pero el esquema define 'administrative_procedures'

-- Crear tabla opas como vista materializada o alias si no existe
DO $$
BEGIN
    -- Verificar si la tabla opas existe
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'opas') THEN
        -- Crear la tabla opas con la misma estructura que administrative_procedures
        CREATE TABLE opas (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            dependency_id UUID REFERENCES dependencies(id) NOT NULL,
            subdependency_id UUID REFERENCES subdependencies(id),
            code VARCHAR(20) NOT NULL,
            name VARCHAR(300) NOT NULL,  -- Campo adicional para el nombre
            description TEXT NOT NULL,
            category VARCHAR(100),
            requirements TEXT,
            response_time VARCHAR(100),
            contact_info JSONB DEFAULT '{}',
            is_active BOOLEAN DEFAULT true,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        -- Crear índices para la tabla opas
        CREATE INDEX idx_opas_dependency ON opas(dependency_id);
        CREATE INDEX idx_opas_subdependency ON opas(subdependency_id);
        CREATE INDEX idx_opas_code ON opas(code);
        CREATE INDEX idx_opas_active ON opas(is_active);
        
        RAISE NOTICE 'Tabla opas creada exitosamente';
    ELSE
        RAISE NOTICE 'La tabla opas ya existe';
    END IF;
END $$;

-- Crear la vista unificada all_procedures
CREATE OR REPLACE VIEW all_procedures AS
-- Trámites (procedures)
SELECT 
    'TRAMITE' as type,
    p.id,
    p.name,
    p.description,
    d.name as dependency_name,
    d.code as dependency_code,
    COALESCE(s.name, '') as subdependency_name,
    COALESCE(s.code, '') as subdependency_code,
    p.response_time,
    CASE 
        WHEN p.has_cost = true AND p.cost_description IS NOT NULL THEN p.cost_description
        WHEN p.has_cost = true THEN 'Con costo'
        ELSE 'Gratuito'
    END as cost,
    CASE 
        WHEN p.can_be_digital = true THEN true
        ELSE false
    END as online_available,
    COALESCE(pc.name, 'Sin categoría') as category,
    p.is_active,
    p.created_at
FROM procedures p
JOIN dependencies d ON p.dependency_id = d.id
LEFT JOIN subdependencies s ON p.subdependency_id = s.id
LEFT JOIN procedure_categories pc ON p.category_id = pc.id

UNION ALL

-- OPAs (opas)
SELECT 
    'OPA' as type,
    o.id,
    o.name,
    o.description,
    d.name as dependency_name,
    d.code as dependency_code,
    COALESCE(s.name, '') as subdependency_name,
    COALESCE(s.code, '') as subdependency_code,
    o.response_time,
    'Sin costo definido' as cost,  -- Los OPAs generalmente no tienen costo
    false as online_available,     -- Los OPAs generalmente son presenciales
    COALESCE(o.category, 'OPA') as category,
    o.is_active,
    o.created_at
FROM opas o
JOIN dependencies d ON o.dependency_id = d.id
LEFT JOIN subdependencies s ON o.subdependency_id = s.id;

-- Crear función de búsqueda que usa la vista all_procedures
CREATE OR REPLACE FUNCTION buscar_procedimientos(palabra_clave TEXT)
RETURNS TABLE (
    tipo_procedimiento TEXT,
    nombre TEXT,
    descripcion TEXT,
    dependencia TEXT,
    subdependencia TEXT,
    costo TEXT,
    tiempo_respuesta TEXT,
    modalidad TEXT,
    categoria TEXT,
    estado TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        ap.type as tipo_procedimiento,
        ap.name as nombre,
        ap.description as descripcion,
        ap.dependency_name as dependencia,
        ap.subdependency_name as subdependencia,
        ap.cost as costo,
        COALESCE(ap.response_time, 'No especificado') as tiempo_respuesta,
        CASE 
            WHEN ap.online_available = true THEN 'Disponible en línea'
            WHEN ap.online_available = false THEN 'Solo presencial'
            ELSE 'No especificado'
        END as modalidad,
        ap.category as categoria,
        CASE 
            WHEN ap.is_active = true THEN 'Activo'
            ELSE 'Inactivo'
        END as estado
    FROM all_procedures ap
    WHERE ap.is_active = true
    AND (
        LOWER(ap.name) LIKE LOWER('%' || palabra_clave || '%') OR
        LOWER(ap.description) LIKE LOWER('%' || palabra_clave || '%') OR
        LOWER(ap.dependency_name) LIKE LOWER('%' || palabra_clave || '%') OR
        LOWER(ap.subdependency_name) LIKE LOWER('%' || palabra_clave || '%') OR
        LOWER(ap.category) LIKE LOWER('%' || palabra_clave || '%')
    )
    ORDER BY ap.dependency_name, ap.type, ap.name;
END;
$$ LANGUAGE plpgsql;

-- Crear índices para mejorar el rendimiento de búsquedas
CREATE INDEX IF NOT EXISTS idx_all_procedures_search 
ON procedures USING gin(to_tsvector('spanish', name || ' ' || COALESCE(description, '')));

CREATE INDEX IF NOT EXISTS idx_opas_search 
ON opas USING gin(to_tsvector('spanish', name || ' ' || COALESCE(description, '')));

-- Verificar que la vista funciona correctamente
SELECT 
    'VERIFICACIÓN DE LA VISTA all_procedures' as titulo,
    COUNT(*) as total_procedures,
    SUM(CASE WHEN type = 'TRAMITE' THEN 1 ELSE 0 END) as tramites,
    SUM(CASE WHEN type = 'OPA' THEN 1 ELSE 0 END) as opas,
    COUNT(DISTINCT dependency_name) as dependencias_activas
FROM all_procedures;

-- Ejemplo de uso de la función de búsqueda
-- SELECT * FROM buscar_procedimientos('licencia');
-- SELECT * FROM buscar_procedimientos('certificado');
-- SELECT * FROM buscar_procedimientos('hacienda');

COMMIT;
