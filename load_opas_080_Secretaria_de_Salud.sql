-- CARGA OPAs PARA Secretaria de Salud (38 OPAs)
BEGIN;

INSERT INTO opas (code, name, description, dependency_id, subdependency_id, is_active)
SELECT v.code, v.name, v.description, d.id, s.id, true
FROM (VALUES
  ('1', 'Contribución solidaria- SISBEN', 'OPA 1: Contribución solidaria- SISBEN', '080', '080'),
  ('2', 'Aseguramiento, afilicaciones regimen contributivo y subsidiado', 'OPA 2: Aseguramiento, afilicaciones regimen contributivo y subsidiado', '080', '080'),
  ('3', 'Sistema General de Seguridad Social en Salud. Afiliacion a EPS, Sistema de afiliacion transaccional - SAT', 'OPA 3: Sistema General de Seguridad Social en Salud. Afiliacion a EPS, Sistema de afiliacion transaccional - SAT', '080', '080'),
  ('4', 'Abandono adulto mayor y vulnerabilidad(Derecho de petición)', 'OPA 4: Abandono adulto mayor y vulnerabilidad(Derecho de petición)', '080', '080'),
  ('5', 'Licencia de exhumación, inhumación, cremacion y traslado de cadáveres', 'OPA 5: Licencia de exhumación, inhumación, cremacion y traslado de cadáveres', '080', '080'),
  ('6', 'Vigilancia a hogares geriatricos(excepción Hogar San Rafael que le correponde a Accion social)', 'OPA 6: Vigilancia a hogares geriatricos(excepción Hogar San Rafael que le correponde a Accion social)', '080', '080'),
  ('7', 'PQRS sistema de seguridad social en salud (Los reclamos o quejas por la mala prestación de servicios de salud debe radicarse como derecho de petición y no como reclamo).', 'OPA 7: PQRS sistema de seguridad social en salud (Los reclamos o quejas por la mala prestación de servicios de salud debe radicarse como derecho de petición y no como reclamo).', '080', '080'),
  ('1', 'Novedades y vigilancia epídemiologicas, infecciones', 'OPA 1: Novedades y vigilancia epídemiologicas, infecciones', '080', '081'),
  ('2', 'Vacunacion a humanos. PAI  Plan Ampliado de Vacunación', 'OPA 2: Vacunacion a humanos. PAI  Plan Ampliado de Vacunación', '080', '081'),
  ('3', 'Comité de estadisticas vitales', 'OPA 3: Comité de estadisticas vitales', '080', '081'),
  ('4', 'Registro Único de Afiliados  RUAF, MANGO, SIVIGILA, RIPS', 'OPA 4: Registro Único de Afiliados  RUAF, MANGO, SIVIGILA, RIPS', '080', '081'),
  ('5', 'Visita a IPS municipales. Riesgos asociados a IPS y EPS en el municipio por capacidad instalada y panorama de riesgo.', 'OPA 5: Visita a IPS municipales. Riesgos asociados a IPS y EPS en el municipio por capacidad instalada y panorama de riesgo.', '080', '081'),
  ('6', 'Analisis de situacion en salud', 'OPA 6: Analisis de situacion en salud', '080', '081'),
  ('7', 'Politicas de salud pública (Hábitos saludables, poblaciones vulnerables, enfermedades transmisibles, enfermedades no transmisibles, salud sexual y reproductiva; salud mental, salud ambiental, salud nutricional, vacunación)', 'OPA 7: Politicas de salud pública (Hábitos saludables, poblaciones vulnerables, enfermedades transmisibles, enfermedades no transmisibles, salud sexual y reproductiva; salud mental, salud ambiental, salud nutricional, vacunación)', '080', '081'),
  ('8', 'Plan Decenal de Salud Pública.', 'OPA 8: Plan Decenal de Salud Pública.', '080', '081'),
  ('9', 'Plan de Intervenciones Colectivas PIC', 'OPA 9: Plan de Intervenciones Colectivas PIC', '080', '081'),
  ('10', 'Prevención y promoción  gestantes.', 'OPA 10: Prevención y promoción  gestantes.', '080', '081'),
  ('11', 'AIEPI  Atención en enfermedades prevalentes en la infancia.', 'OPA 11: AIEPI  Atención en enfermedades prevalentes en la infancia.', '080', '081'),
  ('12', 'Enfermedades huerfanas', 'OPA 12: Enfermedades huerfanas', '080', '081'),
  ('13', 'Rutas integrales de atencion en salud', 'OPA 13: Rutas integrales de atencion en salud', '080', '081'),
  ('14', 'REPS- Registro Especial de Prestadores de Servicios de Salud', 'OPA 14: REPS- Registro Especial de Prestadores de Servicios de Salud', '080', '081'),
  ('15', 'Certificado de discapacidad', 'OPA 15: Certificado de discapacidad', '080', '081'),
  ('1', 'Concepto sanitario, Alimentos sanos y seguros, seguridad quimica, esfermedades trasmitidas por alimentos ETAS', 'OPA 1: Concepto sanitario, Alimentos sanos y seguros, seguridad quimica, esfermedades trasmitidas por alimentos ETAS', '080', '082'),
  ('2', 'Eventos transmisibles de origen zoonótico, enfermedades transmitidas por animales', 'OPA 2: Eventos transmisibles de origen zoonótico, enfermedades transmitidas por animales', '080', '082'),
  ('3', 'Protocolos de bioseguridad', 'OPA 3: Protocolos de bioseguridad', '080', '082'),
  ('4', 'Verificación de estándares de habilitación de los establecimientos dispensadores de medicamentos', 'OPA 4: Verificación de estándares de habilitación de los establecimientos dispensadores de medicamentos', '080', '082'),
  ('5', 'Proceso de inspección, vigilancia y control, IVC', 'OPA 5: Proceso de inspección, vigilancia y control, IVC', '080', '082'),
  ('6', 'Control de población canina y felina.', 'OPA 6: Control de población canina y felina.', '080', '082'),
  ('7', 'Vigilancia del agua para consumo humano: control en puntos de vertimiento de agua domestica,', 'OPA 7: Vigilancia del agua para consumo humano: control en puntos de vertimiento de agua domestica,', '080', '082'),
  ('8', 'Radiación electromagnética.', 'OPA 8: Radiación electromagnética.', '080', '082'),
  ('9', 'Esterilizacion y vacunacion antirrabica canina y felina.', 'OPA 9: Esterilizacion y vacunacion antirrabica canina y felina.', '080', '082'),
  ('10', 'Capacitacion sobre tenencia responsable de mascotas', 'OPA 10: Capacitacion sobre tenencia responsable de mascotas', '080', '082'),
  ('11', 'Control de Plagas (artrópodos y roedores) moscos, mosquitos para predios publicos realizan fumigación. Cuando se traten de denuncias relacionadas con predios privados se hace una visita para verificar', 'OPA 11: Control de Plagas (artrópodos y roedores) moscos, mosquitos para predios publicos realizan fumigación. Cuando se traten de denuncias relacionadas con predios privados se hace una visita para verificar', '080', '082'),
  ('12', 'Visitas a Establecimientos y Empresas expendedoras o aplicadores de sustancia químicas', 'OPA 12: Visitas a Establecimientos y Empresas expendedoras o aplicadores de sustancia químicas', '080', '082'),
  ('13', 'Visita a establecimientos de preparación y comercialización  de alimentos y transporte de alimentos.', 'OPA 13: Visita a establecimientos de preparación y comercialización  de alimentos y transporte de alimentos.', '080', '082'),
  ('14', 'Registro de ejemplares caninos potencialmente peligrosos', 'OPA 14: Registro de ejemplares caninos potencialmente peligrosos', '080', '082'),
  ('15', 'Maltrato animal de gatos y perros unicamente (Si es una denuncia radicarlo como proceso policivo a Resolución de Conflictos).', 'OPA 15: Maltrato animal de gatos y perros unicamente (Si es una denuncia radicarlo como proceso policivo a Resolución de Conflictos).', '080', '082'),
  ('16', 'Adopcion canina y felina, albergue municipal', 'OPA 16: Adopcion canina y felina, albergue municipal', '080', '082')
) AS v(code, name, description, dep_code, subdep_code)
JOIN dependencies d ON d.code = v.dep_code
LEFT JOIN subdependencies s ON s.dependency_id = d.id AND s.code = v.subdep_code
WHERE NOT EXISTS (
    SELECT 1 FROM opas o 
    WHERE o.code = v.code 
    AND o.dependency_id = d.id 
    AND (o.subdependency_id = s.id OR (o.subdependency_id IS NULL AND s.id IS NULL))
);

-- Verificar carga
SELECT 
    'Secretaria de Salud' as dependencia,
    COUNT(*) as opas_cargados
FROM opas o
JOIN dependencies d ON o.dependency_id = d.id
WHERE d.code = '080';

COMMIT;
