# Configuración de Coolify para Sistema de Atención Ciudadana

## Información General

**Proyecto:** Sistema de Atención Ciudadana - Chía  
**Versión:** 1.0  
**Fecha:** 2025-06-30  

## 1. Configuración del Servidor

### Especificaciones Mínimas Recomendadas

```yaml
Servidor de Producción:
  CPU: 4 vCPUs (8 vCPUs recomendado)
  RAM: 8GB (16GB recomendado)
  Storage: 100GB SSD (200GB recomendado)
  Bandwidth: 2TB/mes
  OS: Ubuntu 22.04 LTS

Servidor de Staging:
  CPU: 2 vCPUs
  RAM: 4GB
  Storage: 50GB SSD
  Bandwidth: 1TB/mes
  OS: Ubuntu 22.04 LTS
```

### Proveedores Recomendados

1. **DigitalOcean** - Droplets con buen rendimiento/precio
2. **Hetzner** - Excelente relación calidad/precio en Europa
3. **Linode** - Confiable y con buena documentación
4. **Vultr** - <PERSON><PERSON><PERSON>les ubicaciones y precios competitivos

## 2. Instalación de Coolify

### Instalación Inicial

```bash
# Actualizar el sistema
sudo apt update && sudo apt upgrade -y

# Instalar Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# Instalar Coolify
curl -fsSL https://cdn.coollabs.io/coolify/install.sh | bash
```

### Configuración Post-Instalación

```bash
# Acceder a Coolify (por defecto en puerto 8000)
# http://your-server-ip:8000

# Configurar usuario administrador
# Seguir el wizard de configuración inicial
```

## 3. Configuración del Proyecto

### 3.1 Estructura de Archivos

```
proyecto/
├── docker-compose.yml
├── Dockerfile
├── nginx.conf
├── .env.production
├── .env.staging
├── package.json
├── next.config.js
└── coolify/
    ├── build.sh
    ├── deploy.sh
    └── healthcheck.sh
```

### 3.2 Dockerfile Optimizado

```dockerfile
# Dockerfile
FROM node:18-alpine AS base

# Instalar dependencias solo cuando sea necesario
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

# Instalar dependencias basadas en el package manager preferido
COPY package.json yarn.lock* package-lock.json* pnpm-lock.yaml* ./
RUN \
  if [ -f yarn.lock ]; then yarn --frozen-lockfile; \
  elif [ -f package-lock.json ]; then npm ci; \
  elif [ -f pnpm-lock.yaml ]; then yarn global add pnpm && pnpm i --frozen-lockfile; \
  else echo "Lockfile not found." && exit 1; \
  fi

# Rebuild el código fuente solo cuando sea necesario
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Deshabilitar telemetría de Next.js durante el build
ENV NEXT_TELEMETRY_DISABLED 1

RUN npm run build

# Imagen de producción, copiar todos los archivos y ejecutar next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production
ENV NEXT_TELEMETRY_DISABLED 1

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public

# Aprovechar las salidas de trace para reducir el tamaño de la imagen
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

CMD ["node", "server.js"]
```

### 3.3 Docker Compose

```yaml
# docker-compose.yml
version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: atencion-ciudadana-app
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_SUPABASE_URL=${NEXT_PUBLIC_SUPABASE_URL}
      - NEXT_PUBLIC_SUPABASE_ANON_KEY=${NEXT_PUBLIC_SUPABASE_ANON_KEY}
      - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - APP_URL=${APP_URL}
    networks:
      - coolify
    labels:
      - "coolify.managed=true"
      - "traefik.enable=true"
      - "traefik.http.routers.atencion-ciudadana.rule=Host(`atencion.chia.gov.co`)"
      - "traefik.http.routers.atencion-ciudadana.tls=true"
      - "traefik.http.routers.atencion-ciudadana.tls.certresolver=letsencrypt"
      - "traefik.http.services.atencion-ciudadana.loadbalancer.server.port=3000"

  redis:
    image: redis:7-alpine
    container_name: atencion-ciudadana-redis
    restart: unless-stopped
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    networks:
      - coolify
    labels:
      - "coolify.managed=true"

volumes:
  redis_data:

networks:
  coolify:
    external: true
```

### 3.4 Configuración de Nginx (Opcional)

```nginx
# nginx.conf
upstream app {
    server app:3000;
}

server {
    listen 80;
    server_name atencion.chia.gov.co;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name atencion.chia.gov.co;

    # SSL Configuration (manejado por Coolify/Traefik)
    
    # Security Headers
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' https://fonts.gstatic.com; connect-src 'self' https://*.supabase.co wss://*.supabase.co;" always;

    # Gzip Compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied expired no-cache no-store private must-revalidate auth;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

    # Rate Limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=login:10m rate=1r/s;

    location / {
        proxy_pass http://app;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 86400;
    }

    location /api/ {
        limit_req zone=api burst=20 nodelay;
        proxy_pass http://app;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /auth/ {
        limit_req zone=login burst=5 nodelay;
        proxy_pass http://app;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Static files caching
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        proxy_pass http://app;
    }

    # Health check endpoint
    location /health {
        access_log off;
        proxy_pass http://app;
    }
}
```

## 4. Variables de Entorno

### 4.1 Producción (.env.production)

```bash
# Application
NODE_ENV=production
APP_URL=https://atencion.chia.gov.co
APP_NAME="Sistema de Atención Ciudadana"
APP_VERSION=1.0.0

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key-here
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here

# AI Configuration
OPENAI_API_KEY=your-openai-api-key
AI_MODEL=gpt-4-turbo-preview
AI_MAX_TOKENS=2000
AI_TEMPERATURE=0.7

# External APIs
SUIT_API_URL=https://visorsuit.funcionpublica.gov.co
GOV_CO_API_URL=https://www.gov.co
CHIA_API_URL=https://api.chia.gov.co

# Email Configuration (via Supabase)
SMTP_HOST=smtp.supabase.co
SMTP_PORT=587
SMTP_USER=your-smtp-user
SMTP_PASS=your-smtp-password

# Monitoring & Analytics
SENTRY_DSN=your-sentry-dsn
GOOGLE_ANALYTICS_ID=your-ga-id
HOTJAR_ID=your-hotjar-id

# Security
JWT_SECRET=your-jwt-secret-here
ENCRYPTION_KEY=your-encryption-key-here
RATE_LIMIT_MAX=100
RATE_LIMIT_WINDOW=900000

# File Upload
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=pdf,jpg,jpeg,png,doc,docx

# Cache
REDIS_URL=redis://redis:6379
CACHE_TTL=3600
```

### 4.2 Staging (.env.staging)

```bash
# Similar a producción pero con valores de staging
NODE_ENV=staging
APP_URL=https://staging-atencion.chia.gov.co
NEXT_PUBLIC_SUPABASE_URL=https://your-staging-project.supabase.co
# ... otros valores de staging
```

## 5. Scripts de Despliegue

### 5.1 Script de Build (coolify/build.sh)

```bash
#!/bin/bash
set -e

echo "🚀 Iniciando build del proyecto..."

# Verificar que las variables de entorno estén configuradas
if [ -z "$NEXT_PUBLIC_SUPABASE_URL" ]; then
    echo "❌ Error: NEXT_PUBLIC_SUPABASE_URL no está configurada"
    exit 1
fi

# Instalar dependencias
echo "📦 Instalando dependencias..."
npm ci

# Ejecutar tests
echo "🧪 Ejecutando tests..."
npm run test:ci

# Ejecutar linting
echo "🔍 Ejecutando linting..."
npm run lint

# Build del proyecto
echo "🏗️ Construyendo proyecto..."
npm run build

echo "✅ Build completado exitosamente"
```

### 5.2 Script de Deploy (coolify/deploy.sh)

```bash
#!/bin/bash
set -e

echo "🚀 Iniciando despliegue..."

# Verificar que el build fue exitoso
if [ ! -d ".next" ]; then
    echo "❌ Error: No se encontró el directorio .next"
    exit 1
fi

# Crear backup de la versión anterior (si existe)
if docker ps | grep -q "atencion-ciudadana-app"; then
    echo "💾 Creando backup de la versión anterior..."
    docker tag atencion-ciudadana-app:latest atencion-ciudadana-app:backup-$(date +%Y%m%d-%H%M%S)
fi

# Construir nueva imagen
echo "🏗️ Construyendo nueva imagen Docker..."
docker build -t atencion-ciudadana-app:latest .

# Detener contenedores anteriores
echo "🛑 Deteniendo contenedores anteriores..."
docker-compose down

# Iniciar nuevos contenedores
echo "🚀 Iniciando nuevos contenedores..."
docker-compose up -d

# Verificar que la aplicación esté funcionando
echo "🔍 Verificando estado de la aplicación..."
sleep 30

if curl -f http://localhost:3000/health > /dev/null 2>&1; then
    echo "✅ Despliegue completado exitosamente"
else
    echo "❌ Error: La aplicación no responde"
    echo "🔄 Realizando rollback..."
    docker-compose down
    docker tag atencion-ciudadana-app:backup-$(date +%Y%m%d-%H%M%S) atencion-ciudadana-app:latest
    docker-compose up -d
    exit 1
fi
```

### 5.3 Health Check (coolify/healthcheck.sh)

```bash
#!/bin/bash

# Verificar que la aplicación responda
if curl -f http://localhost:3000/health > /dev/null 2>&1; then
    echo "✅ Aplicación funcionando correctamente"
    exit 0
else
    echo "❌ Aplicación no responde"
    exit 1
fi
```

## 6. Configuración de Dominios y SSL

### 6.1 Configuración de DNS

```
# Registros DNS requeridos
atencion.chia.gov.co.        A       YOUR_SERVER_IP
staging-atencion.chia.gov.co. A       YOUR_SERVER_IP
www.atencion.chia.gov.co.    CNAME   atencion.chia.gov.co.
```

### 6.2 Configuración SSL en Coolify

1. **Acceder al panel de Coolify**
2. **Ir a la configuración del proyecto**
3. **Habilitar SSL automático con Let's Encrypt**
4. **Configurar renovación automática**

```yaml
# Configuración SSL automática
ssl:
  enabled: true
  provider: letsencrypt
  email: <EMAIL>
  domains:
    - atencion.chia.gov.co
    - www.atencion.chia.gov.co
  auto_renew: true
```

## 7. Monitoreo y Logging

### 7.1 Configuración de Logs

```yaml
# docker-compose.yml - Sección de logging
logging:
  driver: "json-file"
  options:
    max-size: "10m"
    max-file: "3"
```

### 7.2 Métricas y Alertas

```bash
# Instalar herramientas de monitoreo
docker run -d \
  --name=prometheus \
  -p 9090:9090 \
  -v prometheus.yml:/etc/prometheus/prometheus.yml \
  prom/prometheus

docker run -d \
  --name=grafana \
  -p 3001:3000 \
  grafana/grafana
```

## 8. Backup y Recuperación

### 8.1 Script de Backup

```bash
#!/bin/bash
# backup.sh

BACKUP_DIR="/backups"
DATE=$(date +%Y%m%d_%H%M%S)

# Crear directorio de backup
mkdir -p $BACKUP_DIR

# Backup de configuraciones
tar -czf $BACKUP_DIR/config_$DATE.tar.gz \
  docker-compose.yml \
  .env.production \
  nginx.conf

# Backup de volúmenes Docker
docker run --rm \
  -v atencion-ciudadana_redis_data:/data \
  -v $BACKUP_DIR:/backup \
  alpine tar czf /backup/redis_$DATE.tar.gz -C /data .

echo "✅ Backup completado: $DATE"
```

### 8.2 Script de Restauración

```bash
#!/bin/bash
# restore.sh

if [ -z "$1" ]; then
    echo "Uso: ./restore.sh FECHA_BACKUP"
    exit 1
fi

BACKUP_DATE=$1
BACKUP_DIR="/backups"

# Restaurar configuraciones
tar -xzf $BACKUP_DIR/config_$BACKUP_DATE.tar.gz

# Restaurar volúmenes
docker run --rm \
  -v atencion-ciudadana_redis_data:/data \
  -v $BACKUP_DIR:/backup \
  alpine tar xzf /backup/redis_$BACKUP_DATE.tar.gz -C /data

echo "✅ Restauración completada: $BACKUP_DATE"
```

## 9. Comandos Útiles

### 9.1 Comandos de Coolify

```bash
# Ver logs de la aplicación
coolify logs app

# Reiniciar aplicación
coolify restart app

# Ver estado de servicios
coolify status

# Actualizar Coolify
coolify update
```

### 9.2 Comandos Docker

```bash
# Ver logs en tiempo real
docker-compose logs -f app

# Ejecutar comandos en el contenedor
docker-compose exec app sh

# Ver uso de recursos
docker stats

# Limpiar imágenes no utilizadas
docker system prune -a
```

## 10. Troubleshooting

### 10.1 Problemas Comunes

**Problema:** La aplicación no inicia
```bash
# Verificar logs
docker-compose logs app

# Verificar variables de entorno
docker-compose exec app env | grep SUPABASE
```

**Problema:** SSL no funciona
```bash
# Verificar configuración de Traefik
docker logs traefik

# Renovar certificados manualmente
coolify ssl renew
```

**Problema:** Alto uso de memoria
```bash
# Verificar uso de recursos
docker stats

# Reiniciar servicios
docker-compose restart
```

### 10.2 Contactos de Soporte

- **Documentación Coolify:** https://coolify.io/docs
- **Comunidad Discord:** https://discord.gg/coolify
- **Soporte Técnico:** <EMAIL>
