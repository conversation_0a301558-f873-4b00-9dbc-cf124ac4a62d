// Configuración de Supabase
const SUPABASE_URL = 'https://zeieudvbhlrlnfkwejoh.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InplaWV1ZHZiaGxybG5ma3dlam9oIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEzMDk1MDEsImV4cCI6MjA2Njg4NTUwMX0.sOImH-XXxxVjjUZhWwYt6KK6dpfCBK2wvT2rnPmlC50';

// Inicializar cliente de Supabase (se carga desde CDN)
let supabaseClient = null;

// Datos de ejemplo para demostración (en producción se conectaría a Supabase)
const sampleProcedures = [
    {
        type: 'TRAMITE',
        name: 'Licencia de construcción',
        description: 'Autorización para construcción de obras civiles',
        dependency_name: 'Secretaría de Planeación',
        cost: 419.00,
        response_time: '45 días hábiles'
    },
    {
        type: 'TRAMITE',
        name: 'Certificado de libertad y tradición',
        description: 'Certificado de propiedad inmobiliaria',
        dependency_name: 'Secretaría General',
        cost: 0,
        response_time: '1 día hábil'
    },
    {
        type: 'OPA',
        name: 'Información sobre impuesto predial',
        description: 'Consulta sobre liquidación de impuesto predial',
        dependency_name: 'Secretaría de Hacienda',
        cost: 0,
        response_time: '1 hora'
    },
    {
        type: 'TRAMITE',
        name: 'Licencia de funcionamiento',
        description: 'Autorización para funcionamiento de establecimientos comerciales',
        dependency_name: 'Secretaría de Gobierno',
        cost: 50.00,
        response_time: '15 días hábiles'
    },
    {
        type: 'OPA',
        name: 'Información sobre programas educativos',
        description: 'Consulta sobre programas de educación municipal',
        dependency_name: 'Secretaría de Educación',
        cost: 0,
        response_time: '2 horas'
    },
    {
        type: 'TRAMITE',
        name: 'Permiso de movilización de ganado',
        description: 'Autorización para transporte de ganado',
        dependency_name: 'Secretaría de Desarrollo Económico',
        cost: 25.00,
        response_time: '3 días hábiles'
    },
    {
        type: 'OPA',
        name: 'Información sobre servicios de salud',
        description: 'Consulta sobre centros de salud municipales',
        dependency_name: 'Secretaría de Salud',
        cost: 0,
        response_time: '1 hora'
    },
    {
        type: 'TRAMITE',
        name: 'Licencia ambiental',
        description: 'Autorización para actividades que afecten el medio ambiente',
        dependency_name: 'Secretaría de Medio Ambiente',
        cost: 100.00,
        response_time: '30 días hábiles'
    }
];

// Variables globales
let currentResults = [];
let isSearching = false;
let searchSuggestions = [];
let selectedSuggestionIndex = -1;
let currentPage = 1;
let resultsPerPage = 20;
let totalResults = 0;
let favorites = JSON.parse(localStorage.getItem('procedureFavorites') || '[]');
let searchCache = new Map();
let imageCache = new Map();
let isLazyLoadingEnabled = true;

// Sugerencias de búsqueda comunes
const commonSearchTerms = [
    'licencia de construcción',
    'certificado de libertad',
    'impuesto predial',
    'licencia de funcionamiento',
    'certificado de residencia',
    'permiso de movilización',
    'licencia ambiental',
    'certificado de estratificación',
    'paz y salvo',
    'registro mercantil',
    'permiso de espectáculos',
    'certificado de nomenclatura',
    'licencia sanitaria',
    'permiso de ocupación',
    'certificado de tradición'
];

// Función principal de búsqueda con cache
async function searchProcedures() {
    if (isSearching) return;

    const searchInput = document.getElementById('searchInput');
    const searchTerm = searchInput.value.trim();
    const dependency = document.getElementById('dependencyFilter').value;
    const type = document.getElementById('typeFilter').value;

    // Validación de entrada
    if (searchTerm.length > 0 && searchTerm.length < 3) {
        searchInput.classList.add('form-validation-error');
        showError('Por favor, ingrese al menos 3 caracteres para buscar.');
        return;
    }

    // Limpiar validación
    searchInput.classList.remove('form-validation-error');
    hideSuggestions();

    // Verificar cache
    const cacheKey = `${searchTerm}-${dependency}-${type}-${currentPage}`;
    if (searchCache.has(cacheKey)) {
        const cachedResult = searchCache.get(cacheKey);
        currentResults = cachedResult.data;
        totalResults = cachedResult.totalResults;
        displayResults(currentResults);
        displayPagination();
        console.log('📦 Resultados obtenidos del cache');
        return;
    }

    // Mostrar indicador de carga
    showLoading(true);
    hideNoResults();
    clearResults();

    isSearching = true;

    try {
        let results;

        // Intentar búsqueda real en Supabase si está disponible
        if (supabaseClient) {
            try {
                const searchResult = await searchSupabase(searchTerm, dependency, type, currentPage);
                results = searchResult.data;
                totalResults = searchResult.totalResults;

                console.log(`✅ Búsqueda en Supabase: ${results.length} resultados de ${totalResults} total`);

                // Mostrar mensaje de conexión exitosa
                if (results.length > 0) {
                    console.log('✅ Conectado a base de datos municipal - Datos actualizados');
                }
            } catch (supabaseError) {
                console.warn('Error en Supabase, usando datos de ejemplo:', supabaseError);
                results = searchSampleData(searchTerm, dependency, type);
                totalResults = results.length;

                // Mostrar advertencia de datos de ejemplo
                if (results.length > 0) {
                    console.log('⚠️ Usando datos de ejemplo - Conecte a Supabase para datos reales');
                }
            }
        } else {
            // Usar datos de ejemplo si Supabase no está disponible
            results = searchSampleData(searchTerm, dependency, type);
            totalResults = results.length;
            console.log('📝 Modo demostración - Datos de ejemplo');
        }

        currentResults = results;

        // Guardar en cache
        const cacheKey = `${searchTerm}-${dependency}-${type}-${currentPage}`;
        searchCache.set(cacheKey, {
            data: results,
            totalResults: totalResults,
            timestamp: Date.now()
        });

        // Limpiar cache antiguo (más de 5 minutos)
        cleanOldCache();

        displayResults(results);
        displayPagination();

        // Analytics de búsqueda (opcional)
        logSearchAnalytics(searchTerm, dependency, type, results.length);

    } catch (error) {
        console.error('Error en la búsqueda:', error);
        showError('Error al realizar la búsqueda. Por favor, intenta nuevamente.');
    } finally {
        showLoading(false);
        isSearching = false;
    }
}

// Función de analytics de búsqueda
function logSearchAnalytics(searchTerm, dependency, type, resultCount) {
    const analytics = {
        timestamp: new Date().toISOString(),
        searchTerm: searchTerm,
        dependency: dependency,
        type: type,
        resultCount: resultCount,
        userAgent: navigator.userAgent
    };

    // En producción, esto se enviaría a un servicio de analytics
    console.log('📊 Search Analytics:', analytics);
}

// Función de búsqueda en datos de ejemplo
function searchSampleData(searchTerm, dependency, type) {
    let results = [...sampleProcedures];

    // Filtrar por término de búsqueda
    if (searchTerm) {
        results = results.filter(proc =>
            proc.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            proc.description.toLowerCase().includes(searchTerm.toLowerCase())
        );
    }

    // Filtrar por dependencia
    if (dependency) {
        results = results.filter(proc => proc.dependency_name === dependency);
    }

    // Filtrar por tipo
    if (type) {
        results = results.filter(proc => proc.type === type);
    }

    return results;
}

// Mostrar resultados
function displayResults(results) {
    const container = document.getElementById('resultsContainer');
    
    if (results.length === 0) {
        showNoResults();
        return;
    }
    
    const resultsHTML = results.map(procedure => `
        <div class="procedure-card">
            <div class="d-flex justify-content-between align-items-start mb-2">
                <div>
                    <span class="procedure-type ${procedure.type === 'TRAMITE' ? 'type-tramite' : 'type-opa'}">
                        ${procedure.type}
                    </span>
                    <h5 class="mt-2 mb-1">${procedure.name}</h5>
                </div>
                <div class="text-end">
                    <span class="cost-badge ${procedure.cost === 0 ? 'cost-free' : ''}">
                        ${procedure.cost === 0 ? 'Gratuito' : `$${procedure.cost.toLocaleString()}`}
                    </span>
                </div>
            </div>
            
            <p class="text-muted mb-2">${procedure.description}</p>
            
            <div class="row">
                <div class="col-md-6">
                    <small class="text-muted">
                        <i class="fas fa-building me-1"></i>
                        ${procedure.dependency_name}
                    </small>
                </div>
                <div class="col-md-6 text-end">
                    <small class="text-muted">
                        <i class="fas fa-clock me-1"></i>
                        ${procedure.response_time}
                    </small>
                </div>
            </div>
            
            <div class="mt-3 d-flex justify-content-between align-items-center">
                <div>
                    <button class="btn btn-outline-primary btn-sm me-2" onclick="showProcedureModal(${JSON.stringify(procedure).replace(/"/g, '&quot;')})">
                        <i class="fas fa-info-circle me-1"></i>Ver Detalles
                    </button>
                    <button class="btn btn-primary btn-sm" onclick="startProcedure('${procedure.name}')">
                        <i class="fas fa-play me-1"></i>Iniciar Trámite
                    </button>
                </div>
                <button class="btn btn-link p-0 favorites-btn ${isFavorite(procedure) ? 'active' : ''}"
                        onclick="toggleFavorite(event, ${JSON.stringify(procedure).replace(/"/g, '&quot;')})"
                        title="${isFavorite(procedure) ? 'Quitar de favoritos' : 'Agregar a favoritos'}"
                        aria-label="${isFavorite(procedure) ? 'Quitar de favoritos' : 'Agregar a favoritos'}">
                    <i class="fas fa-star"></i>
                </button>
            </div>
        </div>
    `).join('');
    
    container.innerHTML = `
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h4><i class="fas fa-list me-2"></i>Resultados de Búsqueda</h4>
            <span class="badge bg-primary fs-6">${results.length} procedimiento${results.length !== 1 ? 's' : ''} encontrado${results.length !== 1 ? 's' : ''}</span>
        </div>
        ${resultsHTML}
    `;
}

// Función para mostrar paginación
function displayPagination() {
    const resultsContainer = document.getElementById('searchResults');
    const totalPages = Math.ceil(totalResults / resultsPerPage);

    if (totalPages <= 1) {
        return; // No mostrar paginación si hay una sola página
    }

    const paginationHTML = `
        <div class="d-flex justify-content-between align-items-center mt-4 p-3 bg-light rounded">
            <div class="pagination-info">
                <small class="text-muted">
                    Mostrando ${((currentPage - 1) * resultsPerPage) + 1} - ${Math.min(currentPage * resultsPerPage, totalResults)}
                    de ${totalResults} resultados
                </small>
            </div>
            <nav aria-label="Paginación de resultados">
                <ul class="pagination pagination-sm mb-0">
                    <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
                        <button class="page-link" onclick="changePage(${currentPage - 1})"
                                ${currentPage === 1 ? 'disabled' : ''}>
                            <i class="fas fa-chevron-left"></i> Anterior
                        </button>
                    </li>
                    ${generatePageNumbers(currentPage, totalPages)}
                    <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
                        <button class="page-link" onclick="changePage(${currentPage + 1})"
                                ${currentPage === totalPages ? 'disabled' : ''}>
                            Siguiente <i class="fas fa-chevron-right"></i>
                        </button>
                    </li>
                </ul>
            </nav>
        </div>
    `;

    resultsContainer.insertAdjacentHTML('beforeend', paginationHTML);
}

function generatePageNumbers(current, total) {
    let pages = '';
    const maxVisible = 5;
    let start = Math.max(1, current - Math.floor(maxVisible / 2));
    let end = Math.min(total, start + maxVisible - 1);

    // Ajustar el inicio si estamos cerca del final
    if (end - start < maxVisible - 1) {
        start = Math.max(1, end - maxVisible + 1);
    }

    // Mostrar primera página si no está visible
    if (start > 1) {
        pages += `<li class="page-item">
                    <button class="page-link" onclick="changePage(1)">1</button>
                  </li>`;
        if (start > 2) {
            pages += `<li class="page-item disabled">
                        <span class="page-link">...</span>
                      </li>`;
        }
    }

    // Mostrar páginas visibles
    for (let i = start; i <= end; i++) {
        pages += `<li class="page-item ${i === current ? 'active' : ''}">
                    <button class="page-link" onclick="changePage(${i})">${i}</button>
                  </li>`;
    }

    // Mostrar última página si no está visible
    if (end < total) {
        if (end < total - 1) {
            pages += `<li class="page-item disabled">
                        <span class="page-link">...</span>
                      </li>`;
        }
        pages += `<li class="page-item">
                    <button class="page-link" onclick="changePage(${total})">${total}</button>
                  </li>`;
    }

    return pages;
}

function changePage(page) {
    if (page < 1 || page > Math.ceil(totalResults / resultsPerPage) || page === currentPage) {
        return;
    }

    currentPage = page;
    searchProcedures();

    // Scroll suave hacia arriba
    document.getElementById('searchResults').scrollIntoView({
        behavior: 'smooth',
        block: 'start'
    });
}

// Limpiar búsqueda
function clearSearch() {
    document.getElementById('searchInput').value = '';
    document.getElementById('dependencyFilter').value = '';
    document.getElementById('typeFilter').value = '';
    currentPage = 1;
    totalResults = 0;
    clearResults();
    hideNoResults();
    hideSuggestions();
}

// Funciones auxiliares
function showLoading(show) {
    document.getElementById('loadingIndicator').style.display = show ? 'block' : 'none';
}

function showNoResults() {
    document.getElementById('noResults').style.display = 'block';
}

function hideNoResults() {
    document.getElementById('noResults').style.display = 'none';
}

function clearResults() {
    document.getElementById('resultsContainer').innerHTML = '';
}

function showError(message) {
    const container = document.getElementById('resultsContainer');
    container.innerHTML = `
        <div class="alert alert-danger" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            ${message}
        </div>
    `;
}

// Funciones de interacción
function showProcedureDetails(procedureName) {
    const procedure = currentResults.find(p => p.name === procedureName);
    if (!procedure) return;
    
    alert(`Detalles del procedimiento:\n\nNombre: ${procedure.name}\nDescripción: ${procedure.description}\nDependencia: ${procedure.dependency_name}\nCosto: ${procedure.cost === 0 ? 'Gratuito' : '$' + procedure.cost.toLocaleString()}\nTiempo de respuesta: ${procedure.response_time}`);
}

function startProcedure(procedureName) {
    alert(`Funcionalidad en desarrollo.\n\nPróximamente podrás iniciar el trámite "${procedureName}" directamente desde esta plataforma.\n\nPor ahora, puedes acercarte a la dependencia correspondiente con la información mostrada.`);
}

// Función de autocompletado
function showSuggestions(searchTerm) {
    const suggestionsContainer = document.getElementById('searchSuggestions');

    if (!searchTerm || searchTerm.length < 2) {
        hideSuggestions();
        return;
    }

    // Filtrar sugerencias
    searchSuggestions = commonSearchTerms.filter(term =>
        term.toLowerCase().includes(searchTerm.toLowerCase())
    ).slice(0, 5);

    if (searchSuggestions.length === 0) {
        hideSuggestions();
        return;
    }

    // Mostrar sugerencias
    const suggestionsHTML = searchSuggestions.map((suggestion, index) =>
        `<div class="suggestion-item ${index === selectedSuggestionIndex ? 'active' : ''}"
              onclick="selectSuggestion('${suggestion}')"
              data-index="${index}">
            <i class="fas fa-search me-2"></i>${suggestion}
         </div>`
    ).join('');

    suggestionsContainer.innerHTML = suggestionsHTML;
    suggestionsContainer.style.display = 'block';
}

function hideSuggestions() {
    const suggestionsContainer = document.getElementById('searchSuggestions');
    suggestionsContainer.style.display = 'none';
    selectedSuggestionIndex = -1;
}

function selectSuggestion(suggestion) {
    document.getElementById('searchInput').value = suggestion;
    hideSuggestions();
    searchProcedures();
}

function navigateSuggestions(direction) {
    if (searchSuggestions.length === 0) return;

    if (direction === 'down') {
        selectedSuggestionIndex = (selectedSuggestionIndex + 1) % searchSuggestions.length;
    } else if (direction === 'up') {
        selectedSuggestionIndex = selectedSuggestionIndex <= 0 ?
            searchSuggestions.length - 1 : selectedSuggestionIndex - 1;
    }

    // Actualizar visualización
    const suggestionItems = document.querySelectorAll('.suggestion-item');
    suggestionItems.forEach((item, index) => {
        item.classList.toggle('active', index === selectedSuggestionIndex);
    });
}

// Event listeners
document.addEventListener('DOMContentLoaded', function() {
    // Inicializar Supabase si está disponible
    if (typeof supabase !== 'undefined') {
        supabaseClient = supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
        console.log('Cliente Supabase inicializado correctamente');
    } else {
        console.warn('Supabase no está disponible, usando datos de ejemplo');
    }

    // Verificar si hay parámetros de URL para filtros
    const urlParams = new URLSearchParams(window.location.search);
    const dependencyParam = urlParams.get('dependency');
    if (dependencyParam) {
        document.getElementById('dependencyFilter').value = dependencyParam;
        searchProcedures();
    }

    const searchInput = document.getElementById('searchInput');

    // Autocompletado mientras se escribe
    searchInput.addEventListener('input', function(e) {
        const searchTerm = e.target.value.trim();
        showSuggestions(searchTerm);

        // Validación en tiempo real
        if (searchTerm.length > 0 && searchTerm.length < 3) {
            e.target.classList.add('form-validation-error');
        } else {
            e.target.classList.remove('form-validation-error');
        }
    });

    // Navegación por teclado en sugerencias
    searchInput.addEventListener('keydown', function(e) {
        const suggestionsVisible = document.getElementById('searchSuggestions').style.display === 'block';

        if (suggestionsVisible) {
            if (e.key === 'ArrowDown') {
                e.preventDefault();
                navigateSuggestions('down');
            } else if (e.key === 'ArrowUp') {
                e.preventDefault();
                navigateSuggestions('up');
            } else if (e.key === 'Enter') {
                e.preventDefault();
                if (selectedSuggestionIndex >= 0) {
                    selectSuggestion(searchSuggestions[selectedSuggestionIndex]);
                } else {
                    searchProcedures();
                }
            } else if (e.key === 'Escape') {
                hideSuggestions();
            }
        } else if (e.key === 'Enter') {
            searchProcedures();
        }
    });

    // Ocultar sugerencias al hacer clic fuera
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.position-relative')) {
            hideSuggestions();
        }
    });

    // Optimizaciones de rendimiento
    optimizeResources();
    initLazyLoading();
    initServiceWorker();

    // Cargar estadísticas de filtros al inicializar
    loadFilterStats();

    // Inicializar navegación por teclado
    initKeyboardNavigation();

    // Actualizar contador de favoritos
    updateFavoritesCounter();

    // Optimizar búsqueda con debounce
    const debouncedSearch = debounce(searchProcedures, 300);
    document.getElementById('searchInput').addEventListener('input', debouncedSearch);

    // Monitoreo de rendimiento
    console.log('🚀 Sistema optimizado y listo');

    // Búsqueda automática al cambiar filtros
    document.getElementById('dependencyFilter').addEventListener('change', function() {
        currentPage = 1; // Resetear página al cambiar filtro
        if (this.value || document.getElementById('searchInput').value.trim()) {
            searchProcedures();
        }
    });

    document.getElementById('typeFilter').addEventListener('change', function() {
        currentPage = 1; // Resetear página al cambiar filtro
        if (this.value || document.getElementById('searchInput').value.trim()) {
            searchProcedures();
        }
    });

// Función para cargar estadísticas de filtros
async function loadFilterStats() {
    try {
        const stats = await getFilterStats();
        if (stats) {
            updateFilterCounters(stats);
        }
    } catch (error) {
        console.error('Error cargando estadísticas de filtros:', error);
    }
}

// Función para actualizar contadores en filtros
function updateFilterCounters(stats) {
    // Actualizar contador de tipos
    const typeFilter = document.getElementById('typeFilter');
    const typeOptions = typeFilter.querySelectorAll('option');

    typeOptions.forEach(option => {
        const value = option.value;
        if (value && stats.byType[value]) {
            const currentText = option.textContent;
            if (!currentText.includes('(')) {
                option.textContent = `${currentText} (${stats.byType[value]})`;
            }
        }
    });

    // Actualizar contador de dependencias
    const dependencyFilter = document.getElementById('dependencyFilter');
    const dependencyOptions = dependencyFilter.querySelectorAll('option');

    dependencyOptions.forEach(option => {
        const value = option.value;
        if (value && stats.byDependency[value]) {
            const currentText = option.textContent;
            if (!currentText.includes('(')) {
                option.textContent = `${currentText} (${stats.byDependency[value]})`;
            }
        }
    });

    console.log('📊 Contadores de filtros actualizados:', stats);
}

// Funciones de favoritos
function isFavorite(procedure) {
    return favorites.some(fav => fav.name === procedure.name && fav.dependency_name === procedure.dependency_name);
}

function toggleFavorite(event, procedure) {
    event.stopPropagation();

    const existingIndex = favorites.findIndex(fav =>
        fav.name === procedure.name && fav.dependency_name === procedure.dependency_name
    );

    if (existingIndex >= 0) {
        // Quitar de favoritos
        favorites.splice(existingIndex, 1);
        event.target.closest('.favorites-btn').classList.remove('active');
        event.target.closest('.favorites-btn').title = 'Agregar a favoritos';
        showNotification('Procedimiento removido de favoritos', 'info');
    } else {
        // Agregar a favoritos
        favorites.push({
            name: procedure.name,
            description: procedure.description,
            dependency_name: procedure.dependency_name,
            type: procedure.type,
            cost: procedure.cost,
            response_time: procedure.response_time,
            dateAdded: new Date().toISOString()
        });
        event.target.closest('.favorites-btn').classList.add('active');
        event.target.closest('.favorites-btn').title = 'Quitar de favoritos';
        showNotification('Procedimiento agregado a favoritos', 'success');
    }

    // Guardar en localStorage
    localStorage.setItem('procedureFavorites', JSON.stringify(favorites));

    // Actualizar contador de favoritos si existe
    updateFavoritesCounter();
}

function updateFavoritesCounter() {
    const counter = document.getElementById('favoritesCounter');
    if (counter) {
        counter.textContent = favorites.length;
        counter.style.display = favorites.length > 0 ? 'inline' : 'none';
    }
}

// Modal de detalles de procedimiento
function showProcedureModal(procedure) {
    const modalHTML = `
        <div class="modal fade" id="procedureModal" tabindex="-1" aria-labelledby="procedureModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-procedure modal-dialog-centered modal-dialog-scrollable">
                <div class="modal-content">
                    <div class="procedure-detail-header">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h4 class="modal-title mb-2" id="procedureModalLabel">${procedure.name}</h4>
                                <span class="badge ${procedure.type === 'TRAMITE' ? 'bg-light' : 'bg-success'} text-dark">
                                    ${procedure.type === 'TRAMITE' ? 'Trámite' : 'OPA'}
                                </span>
                            </div>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Cerrar"></button>
                        </div>
                    </div>
                    <div class="procedure-detail-body">
                        <div class="detail-section">
                            <h6><i class="fas fa-info-circle me-2 text-primary"></i>Descripción</h6>
                            <p class="mb-0">${procedure.description}</p>
                        </div>

                        <div class="detail-section">
                            <h6><i class="fas fa-building me-2 text-primary"></i>Dependencia Responsable</h6>
                            <p class="mb-0">${procedure.dependency_name}</p>
                        </div>

                        <div class="row detail-section">
                            <div class="col-md-6">
                                <h6><i class="fas fa-dollar-sign me-2 text-success"></i>Costo</h6>
                                <p class="mb-0 ${procedure.cost === 0 ? 'text-success fw-bold' : ''}">${procedure.cost === 0 ? 'Gratuito' : `$${procedure.cost.toLocaleString()}`}</p>
                            </div>
                            <div class="col-md-6">
                                <h6><i class="fas fa-clock me-2 text-warning"></i>Tiempo de Respuesta</h6>
                                <p class="mb-0">${procedure.response_time}</p>
                            </div>
                        </div>

                        <div class="detail-section">
                            <h6><i class="fas fa-list-check me-2 text-info"></i>Acciones Disponibles</h6>
                            <div class="d-flex gap-2 flex-wrap">
                                <button class="btn btn-primary" onclick="startProcedure('${procedure.name}')">
                                    <i class="fas fa-play me-1"></i>Iniciar Trámite
                                </button>
                                <button class="btn btn-outline-secondary" onclick="downloadProcedureInfo('${procedure.name}')">
                                    <i class="fas fa-download me-1"></i>Descargar Info
                                </button>
                                <button class="btn btn-outline-info" onclick="shareProcedure('${procedure.name}')">
                                    <i class="fas fa-share me-1"></i>Compartir
                                </button>
                                <button class="btn ${isFavorite(procedure) ? 'btn-warning' : 'btn-outline-warning'}"
                                        onclick="toggleFavorite(event, ${JSON.stringify(procedure).replace(/"/g, '&quot;')})">
                                    <i class="fas fa-star me-1"></i>${isFavorite(procedure) ? 'En Favoritos' : 'Agregar a Favoritos'}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remover modal existente si existe
    const existingModal = document.getElementById('procedureModal');
    if (existingModal) {
        existingModal.remove();
    }

    // Agregar modal al DOM
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // Mostrar modal
    const modal = new bootstrap.Modal(document.getElementById('procedureModal'));
    modal.show();

    // Limpiar modal del DOM cuando se cierre
    document.getElementById('procedureModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

// Función de notificaciones
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 1060; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;

    document.body.appendChild(notification);

    // Auto-remover después de 3 segundos
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 3000);
}

// Funciones auxiliares para el modal
function startProcedure(procedureName) {
    showNotification(`Iniciando procedimiento: ${procedureName}`, 'info');
    // Aquí se implementaría la lógica para iniciar el procedimiento
}

function downloadProcedureInfo(procedureName) {
    showNotification(`Descargando información de: ${procedureName}`, 'info');
    // Aquí se implementaría la lógica para descargar la información
}

function shareProcedure(procedureName) {
    if (navigator.share) {
        navigator.share({
            title: `Procedimiento: ${procedureName}`,
            text: `Consulta este procedimiento en el sistema municipal de Chía`,
            url: window.location.href
        });
    } else {
        // Fallback para navegadores que no soportan Web Share API
        navigator.clipboard.writeText(window.location.href);
        showNotification('Enlace copiado al portapapeles', 'success');
    }
}

// Navegación por teclado
function initKeyboardNavigation() {
    let keyboardHelpVisible = false;

    document.addEventListener('keydown', function(e) {
        // Mostrar/ocultar ayuda de navegación con F1
        if (e.key === 'F1') {
            e.preventDefault();
            toggleKeyboardHelp();
            return;
        }

        // Atajos de teclado globales
        if (e.ctrlKey || e.metaKey) {
            switch(e.key) {
                case 'k': // Ctrl+K para enfocar búsqueda
                    e.preventDefault();
                    document.getElementById('searchInput').focus();
                    break;
                case 'Enter': // Ctrl+Enter para buscar
                    e.preventDefault();
                    searchProcedures();
                    break;
                case 'l': // Ctrl+L para limpiar
                    e.preventDefault();
                    clearSearch();
                    break;
            }
        }

        // Navegación en resultados con flechas
        if (e.key === 'ArrowDown' || e.key === 'ArrowUp') {
            const procedureCards = document.querySelectorAll('.procedure-card');
            if (procedureCards.length > 0) {
                navigateResults(e.key === 'ArrowDown' ? 'down' : 'up', procedureCards);
                e.preventDefault();
            }
        }

        // Enter para abrir detalles del elemento seleccionado
        if (e.key === 'Enter') {
            const selectedCard = document.querySelector('.procedure-card.keyboard-selected');
            if (selectedCard) {
                const detailsBtn = selectedCard.querySelector('.btn-outline-primary');
                if (detailsBtn) {
                    detailsBtn.click();
                }
            }
        }

        // Escape para cerrar modales o limpiar selección
        if (e.key === 'Escape') {
            const selectedCard = document.querySelector('.procedure-card.keyboard-selected');
            if (selectedCard) {
                selectedCard.classList.remove('keyboard-selected');
            }
            hideSuggestions();
        }
    });
}

function navigateResults(direction, cards) {
    const currentSelected = document.querySelector('.procedure-card.keyboard-selected');
    let newIndex = 0;

    if (currentSelected) {
        const currentIndex = Array.from(cards).indexOf(currentSelected);
        currentSelected.classList.remove('keyboard-selected');

        if (direction === 'down') {
            newIndex = (currentIndex + 1) % cards.length;
        } else {
            newIndex = currentIndex === 0 ? cards.length - 1 : currentIndex - 1;
        }
    }

    cards[newIndex].classList.add('keyboard-selected');
    cards[newIndex].scrollIntoView({ behavior: 'smooth', block: 'center' });
}

function toggleKeyboardHelp() {
    let helpDiv = document.getElementById('keyboardHelp');

    if (!helpDiv) {
        helpDiv = document.createElement('div');
        helpDiv.id = 'keyboardHelp';
        helpDiv.className = 'keyboard-navigation-help';
        helpDiv.innerHTML = `
            <h6><i class="fas fa-keyboard me-2"></i>Atajos de Teclado</h6>
            <div class="mb-2"><kbd>Ctrl+K</kbd> Enfocar búsqueda</div>
            <div class="mb-2"><kbd>Ctrl+Enter</kbd> Buscar</div>
            <div class="mb-2"><kbd>Ctrl+L</kbd> Limpiar filtros</div>
            <div class="mb-2"><kbd>↑/↓</kbd> Navegar resultados</div>
            <div class="mb-2"><kbd>Enter</kbd> Ver detalles</div>
            <div class="mb-2"><kbd>Esc</kbd> Cancelar</div>
            <div class="mb-2"><kbd>F1</kbd> Mostrar/ocultar ayuda</div>
            <button type="button" class="btn btn-sm btn-light mt-2" onclick="toggleKeyboardHelp()">
                <i class="fas fa-times"></i> Cerrar
            </button>
        `;
        document.body.appendChild(helpDiv);
    }

    helpDiv.style.display = helpDiv.style.display === 'none' ? 'block' : 'none';
}

// Funciones de optimización y cache
function cleanOldCache() {
    const now = Date.now();
    const maxAge = 5 * 60 * 1000; // 5 minutos

    for (const [key, value] of searchCache.entries()) {
        if (now - value.timestamp > maxAge) {
            searchCache.delete(key);
        }
    }

    // Limitar tamaño del cache a 50 entradas
    if (searchCache.size > 50) {
        const entries = Array.from(searchCache.entries());
        entries.sort((a, b) => a[1].timestamp - b[1].timestamp);

        // Eliminar las 10 entradas más antiguas
        for (let i = 0; i < 10; i++) {
            searchCache.delete(entries[i][0]);
        }
    }
}

function clearAllCache() {
    searchCache.clear();
    imageCache.clear();
    localStorage.removeItem('searchCache');
    console.log('🧹 Cache limpiado completamente');
}

// Lazy loading para imágenes y contenido
function initLazyLoading() {
    if ('IntersectionObserver' in window) {
        const lazyImageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                    lazyImageObserver.unobserve(img);
                }
            });
        });

        document.querySelectorAll('img[data-src]').forEach(img => {
            lazyImageObserver.observe(img);
        });
    }
}

// Optimización de recursos
function optimizeResources() {
    // Precargar recursos críticos
    const criticalResources = [
        'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css',
        'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css'
    ];

    criticalResources.forEach(url => {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.as = 'style';
        link.href = url;
        document.head.appendChild(link);
    });
}

// Debounce para búsquedas
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Throttle para scroll events
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// Minimización de reflows y repaints
function batchDOMUpdates(updates) {
    requestAnimationFrame(() => {
        updates.forEach(update => update());
    });
}

// Monitoreo de rendimiento
function measurePerformance(name, fn) {
    const start = performance.now();
    const result = fn();
    const end = performance.now();
    console.log(`⚡ ${name}: ${(end - start).toFixed(2)}ms`);
    return result;
}

// Optimización de memoria
function optimizeMemory() {
    // Limpiar referencias no utilizadas
    if (window.gc && typeof window.gc === 'function') {
        window.gc();
    }

    // Limpiar event listeners huérfanos
    const elements = document.querySelectorAll('[data-cleanup]');
    elements.forEach(el => {
        el.removeEventListener('click', el._clickHandler);
        el.removeAttribute('data-cleanup');
    });
}

// Service Worker para cache offline (si está disponible)
function initServiceWorker() {
    if ('serviceWorker' in navigator) {
        navigator.serviceWorker.register('/sw.js')
            .then(registration => {
                console.log('🔧 Service Worker registrado:', registration);
            })
            .catch(error => {
                console.log('❌ Error registrando Service Worker:', error);
            });
    }
}
});

// Función para conectar con Supabase con paginación
async function searchSupabase(searchTerm, dependency, type, page = 1) {
    if (!supabaseClient) {
        throw new Error('Cliente Supabase no inicializado');
    }

    // Consulta para contar total de resultados
    let countQuery = supabaseClient
        .from('all_procedures')
        .select('*', { count: 'exact', head: true });

    // Consulta para obtener datos paginados
    let dataQuery = supabaseClient
        .from('all_procedures')
        .select('type, name, description, dependency_name, cost, response_time');

    // Aplicar filtros a ambas consultas
    if (searchTerm) {
        const searchFilter = `name.ilike.%${searchTerm}%,description.ilike.%${searchTerm}%`;
        countQuery = countQuery.or(searchFilter);
        dataQuery = dataQuery.or(searchFilter);
    }

    if (dependency) {
        countQuery = countQuery.eq('dependency_name', dependency);
        dataQuery = dataQuery.eq('dependency_name', dependency);
    }

    if (type) {
        countQuery = countQuery.eq('type', type);
        dataQuery = dataQuery.eq('type', type);
    }

    // Ejecutar consulta de conteo
    const { count, error: countError } = await countQuery;
    if (countError) {
        console.error('Error en conteo Supabase:', countError);
        throw countError;
    }

    totalResults = count || 0;

    // Calcular offset para paginación
    const offset = (page - 1) * resultsPerPage;

    // Ejecutar consulta de datos con paginación
    const { data, error } = await dataQuery
        .order('type')
        .order('name')
        .range(offset, offset + resultsPerPage - 1);

    if (error) {
        console.error('Error en consulta Supabase:', error);
        throw error;
    }

    return {
        data: data || [],
        totalResults: totalResults,
        currentPage: page,
        totalPages: Math.ceil(totalResults / resultsPerPage)
    };
}

// Función para obtener estadísticas de filtros
async function getFilterStats() {
    if (!supabaseClient) return null;

    try {
        const { data, error } = await supabaseClient
            .from('all_procedures')
            .select('type, dependency_name');

        if (error) throw error;

        const stats = {
            byType: {},
            byDependency: {}
        };

        data.forEach(item => {
            stats.byType[item.type] = (stats.byType[item.type] || 0) + 1;
            stats.byDependency[item.dependency_name] = (stats.byDependency[item.dependency_name] || 0) + 1;
        });

        return stats;
    } catch (error) {
        console.error('Error obteniendo estadísticas:', error);
        return null;
    }
}
