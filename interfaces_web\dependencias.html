<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dependencias Municipales - Sistema de Trámites Chía</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2E7D32;
            --secondary-color: #4CAF50;
            --accent-color: #81C784;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #E8F5E8 0%, #F1F8E9 100%);
            min-height: 100vh;
        }
        
        .header-section {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            padding: 2rem 0;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .dependency-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border-left: 5px solid var(--secondary-color);
        }
        
        .dependency-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }
        
        .dependency-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
            margin-bottom: 1rem;
        }
        
        .stats-badge {
            background: var(--accent-color);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 600;
            display: inline-block;
            margin: 0.25rem;
        }
        
        .procedure-count {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary-color);
        }
        
        .btn-view-procedures {
            background: var(--secondary-color);
            border: none;
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-view-procedures:hover {
            background: var(--primary-color);
            color: white;
            transform: translateY(-1px);
        }
        
        .breadcrumb {
            background: rgba(255,255,255,0.9);
            border-radius: 10px;
            padding: 1rem;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-2">
                        <i class="fas fa-sitemap me-3"></i>
                        Dependencias Municipales
                    </h1>
                    <h4 class="mb-0 opacity-90">Organización Administrativa - Chía</h4>
                </div>
                <div class="col-md-4 text-end">
                    <a href="index.html" class="btn btn-light btn-lg">
                        <i class="fas fa-search me-2"></i>Buscar Trámites
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Breadcrumb -->
    <div class="container mt-4">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="index.html">Inicio</a></li>
                <li class="breadcrumb-item active" aria-current="page">Dependencias</li>
            </ol>
        </nav>
    </div>

    <!-- Dependencies Grid -->
    <div class="container mt-4">
        <div class="row">
            <!-- Despacho del Alcalde -->
            <div class="col-lg-6 col-xl-4">
                <div class="dependency-card">
                    <div class="dependency-icon">
                        <i class="fas fa-crown"></i>
                    </div>
                    <h4>Despacho del Alcalde</h4>
                    <p class="text-muted">Dirección ejecutiva y coordinación general del municipio</p>
                    
                    <div class="mb-3">
                        <span class="stats-badge">42 OPAs</span>
                    </div>
                    
                    <div class="procedure-count mb-2">42</div>
                    <small class="text-muted">Procedimientos Disponibles</small>
                    
                    <div class="mt-3">
                        <button class="btn btn-view-procedures" onclick="viewDependencyProcedures('Despacho del Alcalde')">
                            <i class="fas fa-list me-2"></i>Ver Procedimientos
                        </button>
                    </div>
                </div>
            </div>

            <!-- Secretaría General -->
            <div class="col-lg-6 col-xl-4">
                <div class="dependency-card">
                    <div class="dependency-icon">
                        <i class="fas fa-cogs"></i>
                    </div>
                    <h4>Secretaría General</h4>
                    <p class="text-muted">Gestión administrativa, jurídica y de archivo municipal</p>
                    
                    <div class="mb-3">
                        <span class="stats-badge">12 Trámites</span>
                        <span class="stats-badge">147 OPAs</span>
                    </div>
                    
                    <div class="procedure-count mb-2">159</div>
                    <small class="text-muted">Procedimientos Disponibles</small>
                    
                    <div class="mt-3">
                        <button class="btn btn-view-procedures" onclick="viewDependencyProcedures('Secretaría General')">
                            <i class="fas fa-list me-2"></i>Ver Procedimientos
                        </button>
                    </div>
                </div>
            </div>

            <!-- Secretaría de Hacienda -->
            <div class="col-lg-6 col-xl-4">
                <div class="dependency-card">
                    <div class="dependency-icon">
                        <i class="fas fa-coins"></i>
                    </div>
                    <h4>Secretaría de Hacienda</h4>
                    <p class="text-muted">Gestión financiera, tributaria y presupuestal</p>
                    
                    <div class="mb-3">
                        <span class="stats-badge">25 Trámites</span>
                        <span class="stats-badge">109 OPAs</span>
                    </div>
                    
                    <div class="procedure-count mb-2">134</div>
                    <small class="text-muted">Procedimientos Disponibles</small>
                    
                    <div class="mt-3">
                        <button class="btn btn-view-procedures" onclick="viewDependencyProcedures('Secretaría de Hacienda')">
                            <i class="fas fa-list me-2"></i>Ver Procedimientos
                        </button>
                    </div>
                </div>
            </div>

            <!-- Secretaría de Gobierno -->
            <div class="col-lg-6 col-xl-4">
                <div class="dependency-card">
                    <div class="dependency-icon">
                        <i class="fas fa-balance-scale"></i>
                    </div>
                    <h4>Secretaría de Gobierno</h4>
                    <p class="text-muted">Orden público, convivencia y participación ciudadana</p>
                    
                    <div class="mb-3">
                        <span class="stats-badge">15 Trámites</span>
                        <span class="stats-badge">93 OPAs</span>
                    </div>
                    
                    <div class="procedure-count mb-2">108</div>
                    <small class="text-muted">Procedimientos Disponibles</small>
                    
                    <div class="mt-3">
                        <button class="btn btn-view-procedures" onclick="viewDependencyProcedures('Secretaría de Gobierno')">
                            <i class="fas fa-list me-2"></i>Ver Procedimientos
                        </button>
                    </div>
                </div>
            </div>

            <!-- Secretaría de Educación -->
            <div class="col-lg-6 col-xl-4">
                <div class="dependency-card">
                    <div class="dependency-icon">
                        <i class="fas fa-graduation-cap"></i>
                    </div>
                    <h4>Secretaría de Educación</h4>
                    <p class="text-muted">Gestión educativa y desarrollo académico municipal</p>
                    
                    <div class="mb-3">
                        <span class="stats-badge">8 Trámites</span>
                        <span class="stats-badge">69 OPAs</span>
                    </div>
                    
                    <div class="procedure-count mb-2">77</div>
                    <small class="text-muted">Procedimientos Disponibles</small>
                    
                    <div class="mt-3">
                        <button class="btn btn-view-procedures" onclick="viewDependencyProcedures('Secretaría de Educación')">
                            <i class="fas fa-list me-2"></i>Ver Procedimientos
                        </button>
                    </div>
                </div>
            </div>

            <!-- Secretaría de Desarrollo Social -->
            <div class="col-lg-6 col-xl-4">
                <div class="dependency-card">
                    <div class="dependency-icon">
                        <i class="fas fa-hands-helping"></i>
                    </div>
                    <h4>Secretaría de Desarrollo Social</h4>
                    <p class="text-muted">Programas sociales y bienestar comunitario</p>
                    
                    <div class="mb-3">
                        <span class="stats-badge">6 Trámites</span>
                        <span class="stats-badge">57 OPAs</span>
                    </div>
                    
                    <div class="procedure-count mb-2">63</div>
                    <small class="text-muted">Procedimientos Disponibles</small>
                    
                    <div class="mt-3">
                        <button class="btn btn-view-procedures" onclick="viewDependencyProcedures('Secretaría de Desarrollo Social')">
                            <i class="fas fa-list me-2"></i>Ver Procedimientos
                        </button>
                    </div>
                </div>
            </div>

            <!-- Secretaría de Planeación -->
            <div class="col-lg-6 col-xl-4">
                <div class="dependency-card">
                    <div class="dependency-icon">
                        <i class="fas fa-drafting-compass"></i>
                    </div>
                    <h4>Secretaría de Planeación</h4>
                    <p class="text-muted">Ordenamiento territorial y desarrollo urbano</p>
                    
                    <div class="mb-3">
                        <span class="stats-badge">20 Trámites</span>
                        <span class="stats-badge">45 OPAs</span>
                    </div>
                    
                    <div class="procedure-count mb-2">65</div>
                    <small class="text-muted">Procedimientos Disponibles</small>
                    
                    <div class="mt-3">
                        <button class="btn btn-view-procedures" onclick="viewDependencyProcedures('Secretaría de Planeación')">
                            <i class="fas fa-list me-2"></i>Ver Procedimientos
                        </button>
                    </div>
                </div>
            </div>

            <!-- Secretaría de Movilidad -->
            <div class="col-lg-6 col-xl-4">
                <div class="dependency-card">
                    <div class="dependency-icon">
                        <i class="fas fa-traffic-light"></i>
                    </div>
                    <h4>Secretaría de Movilidad</h4>
                    <p class="text-muted">Tránsito, transporte y movilidad urbana</p>
                    
                    <div class="mb-3">
                        <span class="stats-badge">10 Trámites</span>
                        <span class="stats-badge">49 OPAs</span>
                    </div>
                    
                    <div class="procedure-count mb-2">59</div>
                    <small class="text-muted">Procedimientos Disponibles</small>
                    
                    <div class="mt-3">
                        <button class="btn btn-view-procedures" onclick="viewDependencyProcedures('Secretaría de Movilidad')">
                            <i class="fas fa-list me-2"></i>Ver Procedimientos
                        </button>
                    </div>
                </div>
            </div>

            <!-- Secretaría de Medio Ambiente -->
            <div class="col-lg-6 col-xl-4">
                <div class="dependency-card">
                    <div class="dependency-icon">
                        <i class="fas fa-leaf"></i>
                    </div>
                    <h4>Secretaría de Medio Ambiente</h4>
                    <p class="text-muted">Gestión ambiental y sostenibilidad</p>
                    
                    <div class="mb-3">
                        <span class="stats-badge">5 Trámites</span>
                        <span class="stats-badge">46 OPAs</span>
                    </div>
                    
                    <div class="procedure-count mb-2">51</div>
                    <small class="text-muted">Procedimientos Disponibles</small>
                    
                    <div class="mt-3">
                        <button class="btn btn-view-procedures" onclick="viewDependencyProcedures('Secretaría de Medio Ambiente')">
                            <i class="fas fa-list me-2"></i>Ver Procedimientos
                        </button>
                    </div>
                </div>
            </div>

            <!-- Secretaría de Obras Públicas -->
            <div class="col-lg-6 col-xl-4">
                <div class="dependency-card">
                    <div class="dependency-icon">
                        <i class="fas fa-hard-hat"></i>
                    </div>
                    <h4>Secretaría de Obras Públicas</h4>
                    <p class="text-muted">Infraestructura y construcción municipal</p>
                    
                    <div class="mb-3">
                        <span class="stats-badge">4 Trámites</span>
                        <span class="stats-badge">39 OPAs</span>
                    </div>
                    
                    <div class="procedure-count mb-2">43</div>
                    <small class="text-muted">Procedimientos Disponibles</small>
                    
                    <div class="mt-3">
                        <button class="btn btn-view-procedures" onclick="viewDependencyProcedures('Secretaría de Obras Públicas')">
                            <i class="fas fa-list me-2"></i>Ver Procedimientos
                        </button>
                    </div>
                </div>
            </div>

            <!-- Secretaría de Salud -->
            <div class="col-lg-6 col-xl-4">
                <div class="dependency-card">
                    <div class="dependency-icon">
                        <i class="fas fa-heartbeat"></i>
                    </div>
                    <h4>Secretaría de Salud</h4>
                    <p class="text-muted">Salud pública y servicios sanitarios</p>
                    
                    <div class="mb-3">
                        <span class="stats-badge">3 Trámites</span>
                        <span class="stats-badge">38 OPAs</span>
                    </div>
                    
                    <div class="procedure-count mb-2">41</div>
                    <small class="text-muted">Procedimientos Disponibles</small>
                    
                    <div class="mt-3">
                        <button class="btn btn-view-procedures" onclick="viewDependencyProcedures('Secretaría de Salud')">
                            <i class="fas fa-list me-2"></i>Ver Procedimientos
                        </button>
                    </div>
                </div>
            </div>

            <!-- Secretaría de Desarrollo Económico -->
            <div class="col-lg-6 col-xl-4">
                <div class="dependency-card">
                    <div class="dependency-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h4>Secretaría de Desarrollo Económico</h4>
                    <p class="text-muted">Fomento empresarial y desarrollo económico local</p>
                    
                    <div class="mb-3">
                        <span class="stats-badge">29 OPAs</span>
                    </div>
                    
                    <div class="procedure-count mb-2">29</div>
                    <small class="text-muted">Procedimientos Disponibles</small>
                    
                    <div class="mt-3">
                        <button class="btn btn-view-procedures" onclick="viewDependencyProcedures('Secretaría de Desarrollo Económico')">
                            <i class="fas fa-list me-2"></i>Ver Procedimientos
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="mt-5 py-4" style="background: var(--primary-color); color: white;">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h6><i class="fas fa-building me-2"></i>Alcaldía Municipal de Chía</h6>
                    <p class="mb-0">Sistema de Trámites y Procedimientos Administrativos</p>
                </div>
                <div class="col-md-6 text-end">
                    <p class="mb-0">
                        <i class="fas fa-phone me-2"></i>Línea de Atención: (*************
                    </p>
                    <p class="mb-0">
                        <i class="fas fa-envelope me-2"></i><EMAIL>
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function viewDependencyProcedures(dependencyName) {
            // Redirigir a la página principal con filtro de dependencia
            window.location.href = `index.html?dependency=${encodeURIComponent(dependencyName)}`;
        }
    </script>
</body>
</html>
