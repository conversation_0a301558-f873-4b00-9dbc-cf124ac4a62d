<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sistema de Trámites Municipales - Chía</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2E7D32;
            --secondary-color: #4CAF50;
            --accent-color: #81C784;
            --text-dark: #1B5E20;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #E8F5E8 0%, #F1F8E9 100%);
            min-height: 100vh;
        }
        
        .header-section {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            padding: 2rem 0;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .search-container {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            margin-top: -50px;
            position: relative;
            z-index: 10;
        }
        
        .procedure-card {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            border-left: 4px solid var(--secondary-color);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        
        .procedure-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .procedure-type {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .type-tramite {
            background: #E3F2FD;
            color: #1976D2;
        }
        
        .type-opa {
            background: #F3E5F5;
            color: #7B1FA2;
        }
        
        .cost-badge {
            background: var(--accent-color);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-weight: 600;
        }
        
        .cost-free {
            background: #4CAF50;
        }
        
        .stats-card {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            height: 100%;
        }
        
        .stats-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary-color);
        }
        
        .btn-search {
            background: var(--secondary-color);
            border: none;
            padding: 0.75rem 2rem;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-search:hover {
            background: var(--primary-color);
            transform: translateY(-1px);
        }
        
        .dependency-filter {
            background: #F8F9FA;
            border: 1px solid #E9ECEF;
            border-radius: 8px;
            padding: 0.5rem;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 2rem;
            color: var(--secondary-color);
        }

        .loading-spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid var(--secondary-color);
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .no-results {
            display: none;
            text-align: center;
            padding: 3rem;
            color: #6c757d;
        }

        .footer-custom {
            background: var(--primary-color);
            color: white;
        }

        .form-validation-error {
            border-color: #dc3545;
            box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
        }

        .search-suggestions {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #ddd;
            border-top: none;
            border-radius: 0 0 8px 8px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            z-index: 1000;
            max-height: 200px;
            overflow-y: auto;
            display: none;
        }

        .suggestion-item {
            padding: 0.75rem 1rem;
            cursor: pointer;
            border-bottom: 1px solid #eee;
            transition: background-color 0.2s;
        }

        .suggestion-item:hover,
        .suggestion-item.active {
            background-color: #f8f9fa;
        }

        .suggestion-item:last-child {
            border-bottom: none;
        }

        .breadcrumb-custom {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 8px;
            padding: 0.75rem 1rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .breadcrumb-custom .breadcrumb-item + .breadcrumb-item::before {
            content: "›";
            color: var(--secondary-color);
            font-weight: bold;
        }

        .favorites-btn {
            position: relative;
            transition: all 0.3s ease;
        }

        .favorites-btn.active {
            color: #ffc107;
        }

        .favorites-btn:hover {
            transform: scale(1.1);
        }

        .modal-procedure {
            max-width: 800px;
        }

        .procedure-detail-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border-radius: 8px 8px 0 0;
            padding: 1.5rem;
        }

        .procedure-detail-body {
            padding: 1.5rem;
        }

        .detail-section {
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #eee;
        }

        .detail-section:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }

        .keyboard-navigation-help {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: rgba(0,0,0,0.9);
            color: white;
            padding: 1rem;
            border-radius: 8px;
            font-size: 0.8rem;
            display: none;
            z-index: 1050;
            max-width: 250px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        }

        .procedure-card.keyboard-selected {
            border: 2px solid var(--primary-color);
            box-shadow: 0 0 0 3px rgba(46, 125, 50, 0.2);
            transform: scale(1.02);
            transition: all 0.2s ease;
        }

        .procedure-card {
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .procedure-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        /* Optimizaciones de rendimiento */
        .lazy { opacity: 0; transition: opacity 0.3s; }
        .lazy.loaded { opacity: 1; }

        /* Reducir animaciones para usuarios que lo prefieren */
        @media (prefers-reduced-motion: reduce) {
            *, *::before, *::after {
                animation-duration: 0.01ms !important;
                transition-duration: 0.01ms !important;
            }
        }

        /* Optimización GPU */
        .gpu-optimized {
            will-change: transform;
            transform: translateZ(0);
            backface-visibility: hidden;
        }

        /* Optimizaciones de rendimiento */
        .lazy {
            opacity: 0;
            transition: opacity 0.3s;
        }

        .lazy.loaded {
            opacity: 1;
        }

        /* Optimización de animaciones */
        @media (prefers-reduced-motion: reduce) {
            *, *::before, *::after {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }

        /* Mejoras de rendimiento para scroll */
        .scroll-optimized {
            will-change: transform;
            transform: translateZ(0);
        }

        /* Cache de fuentes */
        @font-face {
            font-family: 'FontAwesome';
            font-display: swap;
        }

        /* Optimización de repaints */
        .no-repaint {
            backface-visibility: hidden;
            perspective: 1000px;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-2">
                        <i class="fas fa-building me-3"></i>
                        Sistema de Trámites Municipales
                    </h1>
                    <h4 class="mb-0 opacity-90">Municipio de Chía, Cundinamarca</h4>
                </div>
                <div class="col-md-4 text-end">
                    <div class="d-flex justify-content-end align-items-center">
                        <div class="me-3">
                            <div class="stats-number text-white">829</div>
                            <small>Procedimientos Disponibles</small>
                        </div>
                        <a href="dependencias.html" class="btn btn-light btn-lg">
                            <i class="fas fa-sitemap me-2"></i>Ver Dependencias
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Breadcrumbs -->
    <div class="container mt-3">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb breadcrumb-custom" id="breadcrumbNav">
                <li class="breadcrumb-item">
                    <a href="index.html" class="text-decoration-none">
                        <i class="fas fa-home me-1"></i>Inicio
                    </a>
                </li>
                <li class="breadcrumb-item active" aria-current="page">
                    <i class="fas fa-search me-1"></i>Búsqueda de Procedimientos
                </li>
            </ol>
        </nav>
    </div>

    <!-- Search Section -->
    <div class="container">
        <div class="search-container">
            <div class="row">
                <div class="col-12">
                    <h3 class="text-center mb-4 text-dark">
                        <i class="fas fa-search me-2"></i>
                        Buscar Trámites y Procedimientos
                    </h3>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6 mb-3 position-relative">
                    <label class="form-label fw-bold" for="searchInput">Buscar por palabra clave</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-search"></i></span>
                        <input type="text" class="form-control" id="searchInput"
                               placeholder="Ej: licencia, impuesto, certificado..."
                               aria-label="Campo de búsqueda de procedimientos"
                               title="Ingrese palabras clave para buscar procedimientos"
                               autocomplete="off">
                    </div>
                    <div class="search-suggestions" id="searchSuggestions"></div>
                </div>
                
                <div class="col-md-4 mb-3">
                    <label class="form-label fw-bold" for="dependencyFilter">Filtrar por dependencia</label>
                    <select class="form-select dependency-filter" id="dependencyFilter"
                            aria-label="Filtrar procedimientos por dependencia"
                            title="Seleccione una dependencia para filtrar los procedimientos">
                        <option value="">Todas las dependencias</option>
                        <option value="Secretaría General">Secretaría General</option>
                        <option value="Secretaría de Hacienda">Secretaría de Hacienda</option>
                        <option value="Secretaría de Gobierno">Secretaría de Gobierno</option>
                        <option value="Secretaría de Educación">Secretaría de Educación</option>
                        <option value="Secretaría de Desarrollo Social">Secretaría de Desarrollo Social</option>
                        <option value="Secretaría de Planeación">Secretaría de Planeación</option>
                        <option value="Secretaría de Movilidad">Secretaría de Movilidad</option>
                        <option value="Secretaría de Medio Ambiente">Secretaría de Medio Ambiente</option>
                        <option value="Secretaría de Obras Públicas">Secretaría de Obras Públicas</option>
                        <option value="Secretaría de Salud">Secretaría de Salud</option>
                        <option value="Secretaría de Desarrollo Económico">Secretaría de Desarrollo Económico</option>
                        <option value="Despacho del Alcalde">Despacho del Alcalde</option>
                    </select>
                </div>
                
                <div class="col-md-2 mb-3">
                    <label class="form-label fw-bold" for="typeFilter">Tipo</label>
                    <select class="form-select" id="typeFilter"
                            aria-label="Filtrar por tipo de procedimiento"
                            title="Seleccione el tipo de procedimiento: Trámites o OPAs">
                        <option value="">Todos</option>
                        <option value="TRAMITE">Trámites</option>
                        <option value="OPA">OPAs</option>
                    </select>
                </div>
            </div>
            
            <div class="row">
                <div class="col-12 text-center">
                    <button type="button" class="btn btn-search btn-primary" onclick="searchProcedures()"
                            aria-label="Buscar procedimientos con los criterios especificados">
                        <i class="fas fa-search me-2"></i>Buscar Procedimientos
                    </button>
                    <button type="button" class="btn btn-outline-secondary ms-2" onclick="clearSearch()"
                            aria-label="Limpiar todos los filtros de búsqueda">
                        <i class="fas fa-times me-2"></i>Limpiar
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Section -->
    <div class="container mt-5">
        <div class="row">
            <div class="col-md-3 mb-4">
                <div class="stats-card">
                    <div class="stats-number">108</div>
                    <h6 class="text-muted">Trámites</h6>
                    <small class="text-muted">Procedimientos formales</small>
                </div>
            </div>
            <div class="col-md-3 mb-4">
                <div class="stats-card">
                    <div class="stats-number">721</div>
                    <h6 class="text-muted">OPAs</h6>
                    <small class="text-muted">Otros procedimientos</small>
                </div>
            </div>
            <div class="col-md-3 mb-4">
                <div class="stats-card">
                    <div class="stats-number">12</div>
                    <h6 class="text-muted">Dependencias</h6>
                    <small class="text-muted">Secretarías activas</small>
                </div>
            </div>
            <div class="col-md-3 mb-4">
                <div class="stats-card">
                    <div class="stats-number">85</div>
                    <h6 class="text-muted">Gratuitos</h6>
                    <small class="text-muted">Trámites sin costo</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Results Section -->
    <div class="container mt-4">
        <div class="loading" id="loadingIndicator">
            <div class="loading-spinner"></div>
            <p class="mt-2">Buscando procedimientos...</p>
            <small class="text-muted">Consultando base de datos municipal...</small>
        </div>
        
        <div id="resultsContainer">
            <!-- Results will be populated here -->
        </div>
        
        <div class="no-results" id="noResults">
            <i class="fas fa-search fa-3x text-muted mb-3"></i>
            <h4>No se encontraron resultados</h4>
            <p>Intenta con otros términos de búsqueda o selecciona una dependencia diferente.</p>
        </div>
    </div>

    <!-- Footer -->
    <footer class="mt-5 py-4 footer-custom">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h6><i class="fas fa-building me-2"></i>Alcaldía Municipal de Chía</h6>
                    <p class="mb-0">Sistema de Trámites y Procedimientos Administrativos</p>
                </div>
                <div class="col-md-6 text-end">
                    <p class="mb-0">
                        <i class="fas fa-phone me-2"></i>Línea de Atención: (*************
                    </p>
                    <p class="mb-0">
                        <i class="fas fa-envelope me-2"></i><EMAIL>
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="search.js"></script>
</body>
</html>
