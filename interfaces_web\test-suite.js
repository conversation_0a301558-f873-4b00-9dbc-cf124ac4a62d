// Suite de Testing Automatizado para Sistema de Trámites Municipales
// Ejecutar en la consola del navegador para validar funcionalidad

class TestSuite {
    constructor() {
        this.results = [];
        this.totalTests = 0;
        this.passedTests = 0;
        this.failedTests = 0;
    }

    // Método principal para ejecutar todos los tests
    async runAllTests() {
        console.log('🧪 Iniciando Suite de Testing Automatizado');
        console.log('=' .repeat(50));
        
        await this.testSupabaseConnection();
        await this.testSearchFunctionality();
        await this.testFilters();
        await this.testPagination();
        await this.testFavorites();
        await this.testKeyboardNavigation();
        await this.testResponsiveness();
        await this.testAccessibility();
        await this.testPerformance();
        
        this.generateReport();
    }

    // Test 1: Conexión a Supabase
    async testSupabaseConnection() {
        console.log('🔌 Testing: Conexión a Supabase');
        
        try {
            const { data, error } = await supabaseClient
                .from('all_procedures')
                .select('*', { count: 'exact', head: true });
            
            if (error) throw error;
            
            const count = data?.length || 0;
            this.assert(count >= 829, `Debe haber al menos 829 procedimientos, encontrados: ${count}`, 'Conexión Supabase');
            
        } catch (error) {
            this.assert(false, `Error de conexión: ${error.message}`, 'Conexión Supabase');
        }
    }

    // Test 2: Funcionalidad de Búsqueda
    async testSearchFunctionality() {
        console.log('🔍 Testing: Funcionalidad de Búsqueda');
        
        // Test búsqueda básica
        const searchInput = document.getElementById('searchInput');
        searchInput.value = 'licencia';
        
        await this.simulateSearch();
        
        const results = document.querySelectorAll('.procedure-card');
        this.assert(results.length > 0, 'Búsqueda debe retornar resultados', 'Búsqueda Básica');
        
        // Test búsqueda vacía
        searchInput.value = '';
        await this.simulateSearch();
        
        // Test validación mínimo 3 caracteres
        searchInput.value = 'ab';
        const hasError = searchInput.classList.contains('form-validation-error');
        this.assert(hasError, 'Debe mostrar error con menos de 3 caracteres', 'Validación Búsqueda');
    }

    // Test 3: Filtros
    async testFilters() {
        console.log('🎛️ Testing: Filtros');
        
        const dependencyFilter = document.getElementById('dependencyFilter');
        const typeFilter = document.getElementById('typeFilter');
        
        // Test filtro por dependencia
        if (dependencyFilter.options.length > 1) {
            dependencyFilter.selectedIndex = 1;
            await this.simulateSearch();
            this.assert(true, 'Filtro por dependencia funcional', 'Filtro Dependencia');
        }
        
        // Test filtro por tipo
        typeFilter.value = 'TRAMITE';
        await this.simulateSearch();
        
        const tramiteResults = document.querySelectorAll('.procedure-card .type-tramite');
        this.assert(tramiteResults.length >= 0, 'Filtro por tipo funcional', 'Filtro Tipo');
    }

    // Test 4: Paginación
    async testPagination() {
        console.log('📄 Testing: Paginación');
        
        // Simular búsqueda que genere múltiples páginas
        const searchInput = document.getElementById('searchInput');
        searchInput.value = 'certificado';
        await this.simulateSearch();
        
        const paginationContainer = document.getElementById('paginationContainer');
        const hasPagination = paginationContainer && paginationContainer.children.length > 0;
        
        this.assert(hasPagination, 'Paginación debe aparecer con múltiples resultados', 'Paginación');
    }

    // Test 5: Sistema de Favoritos
    async testFavorites() {
        console.log('⭐ Testing: Sistema de Favoritos');
        
        const favoritesBefore = JSON.parse(localStorage.getItem('procedureFavorites') || '[]').length;
        
        // Simular click en favorito
        const favoriteBtn = document.querySelector('.favorites-btn');
        if (favoriteBtn) {
            favoriteBtn.click();
            
            const favoritesAfter = JSON.parse(localStorage.getItem('procedureFavorites') || '[]').length;
            this.assert(favoritesAfter !== favoritesBefore, 'Sistema de favoritos debe funcionar', 'Favoritos');
        } else {
            this.assert(false, 'Botón de favoritos no encontrado', 'Favoritos');
        }
    }

    // Test 6: Navegación por Teclado
    async testKeyboardNavigation() {
        console.log('⌨️ Testing: Navegación por Teclado');
        
        // Test Ctrl+K para enfocar búsqueda
        const searchInput = document.getElementById('searchInput');
        const event = new KeyboardEvent('keydown', { key: 'k', ctrlKey: true });
        document.dispatchEvent(event);
        
        this.assert(document.activeElement === searchInput, 'Ctrl+K debe enfocar búsqueda', 'Navegación Teclado');
        
        // Test F1 para ayuda
        const f1Event = new KeyboardEvent('keydown', { key: 'F1' });
        document.dispatchEvent(f1Event);
        
        const helpDiv = document.getElementById('keyboardHelp');
        this.assert(helpDiv !== null, 'F1 debe mostrar ayuda de teclado', 'Ayuda Teclado');
    }

    // Test 7: Responsividad
    async testResponsiveness() {
        console.log('📱 Testing: Responsividad');
        
        const originalWidth = window.innerWidth;
        
        // Simular viewport móvil
        Object.defineProperty(window, 'innerWidth', { value: 375, configurable: true });
        window.dispatchEvent(new Event('resize'));
        
        const searchContainer = document.querySelector('.search-container');
        const isMobileResponsive = window.getComputedStyle(searchContainer).display !== 'none';
        
        this.assert(isMobileResponsive, 'Interfaz debe ser responsive en móvil', 'Responsividad Móvil');
        
        // Restaurar viewport original
        Object.defineProperty(window, 'innerWidth', { value: originalWidth, configurable: true });
        window.dispatchEvent(new Event('resize'));
    }

    // Test 8: Accesibilidad
    async testAccessibility() {
        console.log('♿ Testing: Accesibilidad');
        
        // Test ARIA labels
        const searchInput = document.getElementById('searchInput');
        const hasAriaLabel = searchInput.hasAttribute('aria-label');
        this.assert(hasAriaLabel, 'Input de búsqueda debe tener aria-label', 'ARIA Labels');
        
        // Test navegación por tab
        const focusableElements = document.querySelectorAll('button, input, select, a[href]');
        this.assert(focusableElements.length > 0, 'Elementos deben ser navegables por tab', 'Navegación Tab');
        
        // Test contraste de colores (básico)
        const body = document.body;
        const styles = window.getComputedStyle(body);
        const hasGoodContrast = styles.color !== styles.backgroundColor;
        this.assert(hasGoodContrast, 'Debe haber contraste de colores', 'Contraste');
    }

    // Test 9: Performance
    async testPerformance() {
        console.log('⚡ Testing: Performance');
        
        const start = performance.now();
        
        // Simular búsqueda y medir tiempo
        const searchInput = document.getElementById('searchInput');
        searchInput.value = 'impuesto';
        await this.simulateSearch();
        
        const end = performance.now();
        const searchTime = end - start;
        
        this.assert(searchTime < 2000, `Búsqueda debe completarse en menos de 2s (${searchTime.toFixed(2)}ms)`, 'Performance Búsqueda');
        
        // Test cache
        const cacheSize = searchCache ? searchCache.size : 0;
        this.assert(cacheSize >= 0, 'Sistema de cache debe estar activo', 'Cache');
    }

    // Método auxiliar para simular búsqueda
    async simulateSearch() {
        return new Promise(resolve => {
            if (typeof searchProcedures === 'function') {
                searchProcedures();
                setTimeout(resolve, 500); // Esperar a que complete la búsqueda
            } else {
                resolve();
            }
        });
    }

    // Método para assertions
    assert(condition, message, testName) {
        this.totalTests++;
        
        if (condition) {
            this.passedTests++;
            console.log(`✅ ${testName}: ${message}`);
            this.results.push({ test: testName, status: 'PASS', message });
        } else {
            this.failedTests++;
            console.log(`❌ ${testName}: ${message}`);
            this.results.push({ test: testName, status: 'FAIL', message });
        }
    }

    // Generar reporte final
    generateReport() {
        console.log('\n' + '='.repeat(50));
        console.log('📊 REPORTE FINAL DE TESTING');
        console.log('='.repeat(50));
        console.log(`Total de Tests: ${this.totalTests}`);
        console.log(`✅ Exitosos: ${this.passedTests}`);
        console.log(`❌ Fallidos: ${this.failedTests}`);
        console.log(`📈 Porcentaje de Éxito: ${((this.passedTests / this.totalTests) * 100).toFixed(1)}%`);
        
        if (this.failedTests > 0) {
            console.log('\n❌ Tests Fallidos:');
            this.results.filter(r => r.status === 'FAIL').forEach(result => {
                console.log(`  - ${result.test}: ${result.message}`);
            });
        }
        
        console.log('\n🎯 Resumen por Categoría:');
        const categories = [...new Set(this.results.map(r => r.test))];
        categories.forEach(category => {
            const categoryResults = this.results.filter(r => r.test === category);
            const passed = categoryResults.filter(r => r.status === 'PASS').length;
            const total = categoryResults.length;
            console.log(`  ${category}: ${passed}/${total} (${((passed/total)*100).toFixed(1)}%)`);
        });
        
        // Recomendaciones
        console.log('\n💡 Recomendaciones:');
        if (this.failedTests === 0) {
            console.log('  🎉 ¡Excelente! Todos los tests pasaron correctamente.');
        } else {
            console.log('  🔧 Revisar y corregir los tests fallidos antes del despliegue.');
        }
        
        return {
            total: this.totalTests,
            passed: this.passedTests,
            failed: this.failedTests,
            percentage: ((this.passedTests / this.totalTests) * 100).toFixed(1),
            results: this.results
        };
    }
}

// Función para ejecutar tests desde la consola
async function runTests() {
    const testSuite = new TestSuite();
    return await testSuite.runAllTests();
}

// Auto-ejecutar si se carga el script
if (typeof window !== 'undefined') {
    console.log('🧪 Suite de Testing cargada. Ejecuta runTests() para comenzar.');
}

// Exportar para uso en Node.js si es necesario
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { TestSuite, runTests };
}
