-- =====================================================
-- ESQUEMA DE BASE DE DATOS - SISTEMA ATENCIÓN CIUDADANA
-- =====================================================
-- Versión: 1.0
-- Fecha: 2025-06-30
-- Descripción: Esquema completo para PostgreSQL en Supabase

-- =====================================================
-- EXTENSIONES REQUERIDAS
-- =====================================================

-- Extensión para UUIDs
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Extensión para encriptación
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Extensión para búsqueda vectorial (IA)
CREATE EXTENSION IF NOT EXISTS "vector";

-- Extensión para búsqueda de texto
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Extensión para funciones de fecha/tiempo
CREATE EXTENSION IF NOT EXISTS "btree_gist";

-- =====================================================
-- TABLAS DE CONFIGURACIÓN Y ROLES
-- =====================================================

-- Tabla de roles del sistema
CREATE TABLE roles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(50) UNIQUE NOT NULL,
    display_name VARCHAR(100) NOT NULL,
    description TEXT,
    permissions JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insertar roles por defecto
INSERT INTO roles (name, display_name, description, permissions) VALUES
('citizen', 'Ciudadano', 'Usuario ciudadano con acceso básico', '{"read": ["own_data"], "write": ["own_profile"]}'),
('admin', 'Administrador de Dependencia', 'Administrador con acceso a su dependencia', '{"read": ["dependency_data"], "write": ["dependency_content"], "manage": ["dependency_users"]}'),
('super_admin', 'Superadministrador', 'Acceso completo al sistema', '{"read": ["all"], "write": ["all"], "manage": ["all"]}');

-- =====================================================
-- ESTRUCTURA ORGANIZACIONAL
-- =====================================================

-- Dependencias gubernamentales
CREATE TABLE dependencies (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    code VARCHAR(10) UNIQUE NOT NULL,
    name VARCHAR(200) NOT NULL,
    acronym VARCHAR(20),
    description TEXT,
    parent_id UUID REFERENCES dependencies(id),
    contact_email VARCHAR(100),
    contact_phone VARCHAR(20),
    address TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Subdependencias
CREATE TABLE subdependencies (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    dependency_id UUID REFERENCES dependencies(id) NOT NULL,
    code VARCHAR(10) NOT NULL,
    name VARCHAR(200) NOT NULL,
    acronym VARCHAR(20),
    description TEXT,
    contact_email VARCHAR(100),
    contact_phone VARCHAR(20),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(dependency_id, code)
);

-- =====================================================
-- GESTIÓN DE USUARIOS
-- =====================================================

-- Perfiles extendidos de usuarios
CREATE TABLE profiles (
    id UUID REFERENCES auth.users(id) PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    full_name VARCHAR(200),
    document_type VARCHAR(20) CHECK (document_type IN ('CC', 'CE', 'TI', 'PP', 'NIT')),
    document_number VARCHAR(20),
    phone VARCHAR(20),
    address TEXT,
    birth_date DATE,
    gender VARCHAR(10) CHECK (gender IN ('M', 'F', 'O')),
    role_id UUID REFERENCES roles(id) NOT NULL,
    dependency_id UUID REFERENCES dependencies(id),
    subdependency_id UUID REFERENCES subdependencies(id),
    is_active BOOLEAN DEFAULT true,
    email_verified BOOLEAN DEFAULT false,
    phone_verified BOOLEAN DEFAULT false,
    last_login TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(document_type, document_number)
);

-- =====================================================
-- GESTIÓN DE TRÁMITES Y SERVICIOS
-- =====================================================

-- Categorías de trámites
CREATE TABLE procedure_categories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    icon VARCHAR(50),
    color VARCHAR(7), -- Código hexadecimal
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Trámites principales
CREATE TABLE procedures (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(300) NOT NULL,
    description TEXT,
    category_id UUID REFERENCES procedure_categories(id),
    dependency_id UUID REFERENCES dependencies(id) NOT NULL,
    subdependency_id UUID REFERENCES subdependencies(id),
    response_time VARCHAR(100),
    has_cost BOOLEAN DEFAULT false,
    cost_description TEXT,
    requirements JSONB DEFAULT '[]',
    documents_required JSONB DEFAULT '[]',
    steps JSONB DEFAULT '[]',
    suit_url TEXT,
    gov_url TEXT,
    form_required BOOLEAN DEFAULT false,
    can_be_digital BOOLEAN DEFAULT false,
    is_automated BOOLEAN DEFAULT false,
    automation_config JSONB DEFAULT '{}',
    tags TEXT[],
    search_vector tsvector,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Índice para búsqueda de texto completo
CREATE INDEX idx_procedures_search ON procedures USING GIN(search_vector);
CREATE INDEX idx_procedures_tags ON procedures USING GIN(tags);

-- OPA (Otros Procedimientos Administrativos)
CREATE TABLE administrative_procedures (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    dependency_id UUID REFERENCES dependencies(id) NOT NULL,
    subdependency_id UUID REFERENCES subdependencies(id),
    code VARCHAR(20) NOT NULL,
    description TEXT NOT NULL,
    category VARCHAR(100),
    requirements TEXT,
    response_time VARCHAR(100),
    contact_info JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- SISTEMA DE TRÁMITES CIUDADANOS
-- =====================================================

-- Estados de trámites
CREATE TABLE procedure_statuses (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(50) UNIQUE NOT NULL,
    display_name VARCHAR(100) NOT NULL,
    description TEXT,
    color VARCHAR(7),
    is_final BOOLEAN DEFAULT false,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insertar estados por defecto
INSERT INTO procedure_statuses (name, display_name, description, color, is_final, sort_order) VALUES
('draft', 'Borrador', 'Trámite en preparación', '#6B7280', false, 1),
('submitted', 'Radicado', 'Trámite enviado y radicado', '#3B82F6', false, 2),
('in_review', 'En Revisión', 'Documentos en proceso de revisión', '#F59E0B', false, 3),
('pending_docs', 'Documentos Pendientes', 'Faltan documentos por entregar', '#EF4444', false, 4),
('approved', 'Aprobado', 'Trámite aprobado', '#10B981', false, 5),
('completed', 'Completado', 'Trámite finalizado exitosamente', '#059669', true, 6),
('rejected', 'Rechazado', 'Trámite rechazado', '#DC2626', true, 7),
('cancelled', 'Cancelado', 'Trámite cancelado por el usuario', '#6B7280', true, 8);

-- Instancias de trámites ciudadanos
CREATE TABLE citizen_procedures (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    procedure_id UUID REFERENCES procedures(id) NOT NULL,
    citizen_id UUID REFERENCES profiles(id) NOT NULL,
    status_id UUID REFERENCES procedure_statuses(id) NOT NULL,
    tracking_number VARCHAR(20) UNIQUE NOT NULL,
    form_data JSONB DEFAULT '{}',
    documents JSONB DEFAULT '[]',
    notes TEXT,
    assigned_to UUID REFERENCES profiles(id),
    estimated_completion DATE,
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Generar número de seguimiento automáticamente
CREATE OR REPLACE FUNCTION generate_tracking_number()
RETURNS TRIGGER AS $$
BEGIN
    NEW.tracking_number := 'TR' || TO_CHAR(NOW(), 'YYYY') || '-' || LPAD(nextval('tracking_sequence')::text, 6, '0');
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE SEQUENCE tracking_sequence START 1;
CREATE TRIGGER set_tracking_number BEFORE INSERT ON citizen_procedures FOR EACH ROW EXECUTE FUNCTION generate_tracking_number();

-- Historial de cambios de estado
CREATE TABLE procedure_status_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    citizen_procedure_id UUID REFERENCES citizen_procedures(id) NOT NULL,
    from_status_id UUID REFERENCES procedure_statuses(id),
    to_status_id UUID REFERENCES procedure_statuses(id) NOT NULL,
    changed_by UUID REFERENCES profiles(id) NOT NULL,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- SISTEMA DE INTELIGENCIA ARTIFICIAL
-- =====================================================

-- Base de conocimientos para IA
CREATE TABLE knowledge_base (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title VARCHAR(300) NOT NULL,
    content TEXT NOT NULL,
    content_type VARCHAR(50) DEFAULT 'text', -- text, json, pdf, etc.
    source_type VARCHAR(50) NOT NULL, -- procedure, opa, faq, manual
    source_id UUID, -- ID del registro fuente
    tags TEXT[],
    embedding vector(1536), -- Para OpenAI embeddings
    metadata JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Índice para búsqueda vectorial
CREATE INDEX idx_knowledge_base_embedding ON knowledge_base USING ivfflat (embedding vector_cosine_ops);

-- Conversaciones del chatbot
CREATE TABLE chat_conversations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES profiles(id),
    session_id VARCHAR(100) NOT NULL,
    title VARCHAR(200),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Mensajes del chat
CREATE TABLE chat_messages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    conversation_id UUID REFERENCES chat_conversations(id) NOT NULL,
    role VARCHAR(20) NOT NULL CHECK (role IN ('user', 'assistant', 'system')),
    content TEXT NOT NULL,
    metadata JSONB DEFAULT '{}',
    tokens_used INTEGER DEFAULT 0,
    response_time_ms INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Feedback de respuestas de IA
CREATE TABLE ai_feedback (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    message_id UUID REFERENCES chat_messages(id) NOT NULL,
    user_id UUID REFERENCES profiles(id),
    rating INTEGER CHECK (rating BETWEEN 1 AND 5),
    feedback_type VARCHAR(20) CHECK (feedback_type IN ('helpful', 'not_helpful', 'incorrect', 'incomplete')),
    comments TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- SISTEMA DE NOTIFICACIONES
-- =====================================================

-- Tipos de notificaciones
CREATE TABLE notification_types (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(50) UNIQUE NOT NULL,
    display_name VARCHAR(100) NOT NULL,
    description TEXT,
    template_subject VARCHAR(200),
    template_body TEXT,
    channels TEXT[] DEFAULT ARRAY['email'], -- email, sms, push, in_app
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Notificaciones
CREATE TABLE notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES profiles(id) NOT NULL,
    type_id UUID REFERENCES notification_types(id) NOT NULL,
    title VARCHAR(200) NOT NULL,
    message TEXT NOT NULL,
    data JSONB DEFAULT '{}',
    channels TEXT[],
    is_read BOOLEAN DEFAULT false,
    sent_at TIMESTAMP WITH TIME ZONE,
    read_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- AUDITORÍA Y LOGS
-- =====================================================

-- Log de auditoría
CREATE TABLE audit_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES profiles(id),
    action VARCHAR(100) NOT NULL,
    table_name VARCHAR(100),
    record_id UUID,
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Índices para auditoría
CREATE INDEX idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX idx_audit_logs_action ON audit_logs(action);
CREATE INDEX idx_audit_logs_created_at ON audit_logs(created_at);

-- =====================================================
-- CONFIGURACIÓN DEL SISTEMA
-- =====================================================

-- Configuraciones generales
CREATE TABLE system_settings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    key VARCHAR(100) UNIQUE NOT NULL,
    value JSONB NOT NULL,
    description TEXT,
    is_public BOOLEAN DEFAULT false,
    updated_by UUID REFERENCES profiles(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Configuraciones por defecto
INSERT INTO system_settings (key, value, description, is_public) VALUES
('app_name', '"Sistema de Atención Ciudadana"', 'Nombre de la aplicación', true),
('app_version', '"1.0.0"', 'Versión actual de la aplicación', true),
('maintenance_mode', 'false', 'Modo de mantenimiento', false),
('max_file_size', '10485760', 'Tamaño máximo de archivo en bytes (10MB)', false),
('allowed_file_types', '["pdf", "jpg", "jpeg", "png", "doc", "docx"]', 'Tipos de archivo permitidos', true);
