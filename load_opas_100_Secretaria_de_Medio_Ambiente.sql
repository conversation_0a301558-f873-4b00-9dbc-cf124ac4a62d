-- CARGA OPAs PARA Secretaria de Medio Ambiente (46 OPAs)
BEGIN;

INSERT INTO opas (code, name, description, dependency_id, subdependency_id, is_active)
SELECT v.code, v.name, v.description, d.id, s.id, true
FROM (VALUES
  ('1', 'Manejo de Fau<PERSON>  (zarigüeyas, cuando es invación se traslada a CAR)', 'OPA 1: <PERSON><PERSON><PERSON> de <PERSON>  (zarigüeyas, cuando es invación se traslada a CAR)', '100', '106'),
  ('2', 'Tenencia ilegal y maltrato  de fauna silvestre (la Secretaria de Medio Ambiente, solicita operativo a la CAR)', 'OPA 2: Tenencia ilegal y maltrato  de fauna silvestre (la Secretaria de Medio Ambiente, solicita operativo a la CAR)', '100', '106'),
  ('3', 'Animales silvestres heridos o en estado de emergencia (la Secretaria de Medio Ambiente solicita Asistencia a la CAR)', 'OPA 3: Animales silvestres heridos o en estado de emergencia (la Secretaria de Medio Ambiente solicita Asistencia a la CAR)', '100', '106'),
  ('4', 'Control y vigilancia de los recursos naturales renovables y no renovables de fauna silvestre', 'OPA 4: Control y vigilancia de los recursos naturales renovables y no renovables de fauna silvestre', '100', '106'),
  ('1', 'Plan de Manejo Ambiental Municipal (competencia CAR )', 'OPA 1: Plan de Manejo Ambiental Municipal (competencia CAR )', '100', '102'),
  ('2', 'Plan de Manejo de Recuperación y Restauración Ambiental PMRAA (competencia CAR),  a la Secretaria de Medio Ambiente solamente información del estado del  Plan; requerimientos relacionados con impacto ambiental por minería, canteras', 'OPA 2: Plan de Manejo de Recuperación y Restauración Ambiental PMRAA (competencia CAR),  a la Secretaria de Medio Ambiente solamente información del estado del  Plan; requerimientos relacionados con impacto ambiental por minería, canteras', '100', '102'),
  ('3', 'Estructura ecológica del Municipio', 'OPA 3: Estructura ecológica del Municipio', '100', '102'),
  ('4', 'Control y Vigilancia de los recursos naturales renovables (no renovables No aplica para el Municipio de Chía)', 'OPA 4: Control y Vigilancia de los recursos naturales renovables (no renovables No aplica para el Municipio de Chía)', '100', '102'),
  ('1', 'Sensibilización en educación ambiental en Instituciones educativas, conjuntos residenciales, juntas comunales, comunidad', 'OPA 1: Sensibilización en educación ambiental en Instituciones educativas, conjuntos residenciales, juntas comunales, comunidad', '100', '107'),
  ('2', 'Proyectos educativos ambientales (asesoria y aprobación por el comité CIDEA)', 'OPA 2: Proyectos educativos ambientales (asesoria y aprobación por el comité CIDEA)', '100', '107'),
  ('1', 'Concepto para aprovechamiento forestal en espacio público', 'OPA 1: Concepto para aprovechamiento forestal en espacio público', '100', '101'),
  ('2', 'Visita Técnica  de inspección ocular para registrar el estado físico y sanitario de individuo arbóreo para establecer recomendaciones de manejo', 'OPA 2: Visita Técnica  de inspección ocular para registrar el estado físico y sanitario de individuo arbóreo para establecer recomendaciones de manejo', '100', '101'),
  ('3', 'Jornadas de reforestación', 'OPA 3: Jornadas de reforestación', '100', '101'),
  ('4', 'Control y vigilancia  de los recursos naturales renovables y no renovables', 'OPA 4: Control y vigilancia  de los recursos naturales renovables y no renovables', '100', '101'),
  ('5', 'Control y vigilancia del manejo de la vegetación', 'OPA 5: Control y vigilancia del manejo de la vegetación', '100', '101'),
  ('6', 'Poda de árboles en espacio público y privado área urbana y rural: La secretaría de Ambiente emite concepto y remite  el informe a la autoridad competente de requerirse.', 'OPA 6: Poda de árboles en espacio público y privado área urbana y rural: La secretaría de Ambiente emite concepto y remite  el informe a la autoridad competente de requerirse.', '100', '101'),
  ('7', 'Tala de árboles en espacio público área urbana: Medio Ambiente puede ordenar la tala.
Tala de árboles en propiedad privada Urbana y Rural: La CAR es quien autoriza la tala.', 'OPA 7: Tala de árboles en espacio público área urbana: Medio Ambiente puede ordenar la tala.
Tala de árboles en propiedad privada Urbana y Rural: La CAR es quien autoriza la tala.', '100', '101'),
  ('8', 'Poda de Césped urbano corresponde a EMSERCHIA', 'OPA 8: Poda de Césped urbano corresponde a EMSERCHIA', '100', '101'),
  ('9', 'Poda de Césped rural corresponde a Medio Ambiente', 'OPA 9: Poda de Césped rural corresponde a Medio Ambiente', '100', '101'),
  ('10', 'Poda de Parques y manzanas Institucionales corresponde a Obras Públicas', 'OPA 10: Poda de Parques y manzanas Institucionales corresponde a Obras Públicas', '100', '101'),
  ('11', 'Glorieta de Jumbo corresponde a Medio Ambiente', 'OPA 11: Glorieta de Jumbo corresponde a Medio Ambiente', '100', '101'),
  ('12', 'Árboles en riesgo: el concepto lo da Medio Ambiente y ejecuta Bomberos', 'OPA 12: Árboles en riesgo: el concepto lo da Medio Ambiente y ejecuta Bomberos', '100', '101'),
  ('13', 'Arboles con redes de servicios públicos o telemática: Dirección de servicios públicos', 'OPA 13: Arboles con redes de servicios públicos o telemática: Dirección de servicios públicos', '100', '101'),
  ('1', 'Certificado paisajístico (trámite de licenciamiento urbanísitico)', 'OPA 1: Certificado paisajístico (trámite de licenciamiento urbanísitico)', '100', '104'),
  ('2', 'Informe Técnico para beneficio de exención de impuesto predial', 'OPA 2: Informe Técnico para beneficio de exención de impuesto predial', '100', '104'),
  ('3', 'Informe Técnico de presunta infracción de los recursos naturales: agua, suelo, flora, aire', 'OPA 3: Informe Técnico de presunta infracción de los recursos naturales: agua, suelo, flora, aire', '100', '104'),
  ('4', 'Informe  Técnico de palomas (no erradicación y se comparte responsabilidad  con Secretaria de Salud, por enfermedades que genera)', 'OPA 4: Informe  Técnico de palomas (no erradicación y se comparte responsabilidad  con Secretaria de Salud, por enfermedades que genera)', '100', '104'),
  ('5', 'Informe Técnico Residuos construcción y demolición (PGIRS)', 'OPA 5: Informe Técnico Residuos construcción y demolición (PGIRS)', '100', '104'),
  ('6', 'Informe Técnico de puntos críticos  ubicados en predios privados  (PGIRS); competencia de EMSERCHIA espacio público', 'OPA 6: Informe Técnico de puntos críticos  ubicados en predios privados  (PGIRS); competencia de EMSERCHIA espacio público', '100', '104'),
  ('1', 'Información referente a Asociaciones gremiales  de recuperadores de oficio en proceso de formalización a empresas prestadoras de servicio de aprovechamiento reconocidas  por el municipio e inscritas  ante la Superintendencia de Servicios Públicos Domiciliarios', 'OPA 1: Información referente a Asociaciones gremiales  de recuperadores de oficio en proceso de formalización a empresas prestadoras de servicio de aprovechamiento reconocidas  por el municipio e inscritas  ante la Superintendencia de Servicios Públicos Domiciliarios', '100', '105'),
  ('2', 'Seguimiento a rutas, microrutas  y puntos de recolección de residuos potencialmente aprovechables en propiedades horizontales, viviendas  (no rutas habituales de recolección de basuras)', 'OPA 2: Seguimiento a rutas, microrutas  y puntos de recolección de residuos potencialmente aprovechables en propiedades horizontales, viviendas  (no rutas habituales de recolección de basuras)', '100', '105'),
  ('3', 'Solicitud de visitas de inspección  y vigilancia a estaciones de Clasificación  y aprovechamiento a Estaciones de de clasificación  y aprovechamiento - ECAS', 'OPA 3: Solicitud de visitas de inspección  y vigilancia a estaciones de Clasificación  y aprovechamiento a Estaciones de de clasificación  y aprovechamiento - ECAS', '100', '105'),
  ('4', 'Implementación de proyectos de autogestión de aprovechamiento orgánicos (composteras, tierra verde)', 'OPA 4: Implementación de proyectos de autogestión de aprovechamiento orgánicos (composteras, tierra verde)', '100', '105'),
  ('1', 'Visita de inspección ocular', 'OPA 1: Visita de inspección ocular', '100', '109'),
  ('2', 'Trámite e impuesto de permiso de publicidad exterior. Aplica para todo tipo de publicidad que supere los 8 mts2 constituyéndose como vallas', 'OPA 2: Trámite e impuesto de permiso de publicidad exterior. Aplica para todo tipo de publicidad que supere los 8 mts2 constituyéndose como vallas', '100', '109'),
  ('3', 'Notificación Resolución publicidad (DCAC debe llamar a la SMA para realizar la notificación)', 'OPA 3: Notificación Resolución publicidad (DCAC debe llamar a la SMA para realizar la notificación)', '100', '109'),
  ('4', 'Registro de avisos publicidad menor a 8mts2', 'OPA 4: Registro de avisos publicidad menor a 8mts2', '100', '109'),
  ('5', 'Trámite de publicidad temporal  PERIFONEO (por mes ) PERIFONEO (quincenal) PUBLICIDAD ESTÁTICA MENOR A 8 mts2 (por m2 anual), PUBLICIDAD EXTERIOR MÓVIL (por mes o fracción de mes) PUBLICIDAD DINÁMICA Y/O DIGITAL MENOR A 8 M2 (por m2 anual), PASACALLES, PUBLICIDAD COMBINADA, PENDONES, PUBLICIDAD AEREA, PUBLICIDAD  MEDIANTE PÁNELES DE CERRAMIENTOS TEMPORALES', 'OPA 5: Trámite de publicidad temporal  PERIFONEO (por mes ) PERIFONEO (quincenal) PUBLICIDAD ESTÁTICA MENOR A 8 mts2 (por m2 anual), PUBLICIDAD EXTERIOR MÓVIL (por mes o fracción de mes) PUBLICIDAD DINÁMICA Y/O DIGITAL MENOR A 8 M2 (por m2 anual), PASACALLES, PUBLICIDAD COMBINADA, PENDONES, PUBLICIDAD AEREA, PUBLICIDAD  MEDIANTE PÁNELES DE CERRAMIENTOS TEMPORALES', '100', '109'),
  ('6', 'Notificación resolución publicidad temporal  (DCAC debe llamar a la SMA para realizar la notificación)', 'OPA 6: Notificación resolución publicidad temporal  (DCAC debe llamar a la SMA para realizar la notificación)', '100', '109'),
  ('1', 'Planes de ordenación y manejo de las cuencas hidrográficas - POMCA', 'OPA 1: Planes de ordenación y manejo de las cuencas hidrográficas - POMCA', '100', '103'),
  ('2', 'Curva de lluvias, intensidad, duración y frecuencia "Curva de IDF" (IDEAM-CAR)', 'OPA 2: Curva de lluvias, intensidad, duración y frecuencia "Curva de IDF" (IDEAM-CAR)', '100', '103'),
  ('3', 'Conservación y protección de fuentes hídricas  de origen natural (ronda hídrica, cauce de rios, quebradas, nacimientos, chucua, humedales)', 'OPA 3: Conservación y protección de fuentes hídricas  de origen natural (ronda hídrica, cauce de rios, quebradas, nacimientos, chucua, humedales)', '100', '103'),
  ('4', 'Informe de autorización intervención de vallados - antrópico (acceso a predios y restitución de tramos obstruidos)', 'OPA 4: Informe de autorización intervención de vallados - antrópico (acceso a predios y restitución de tramos obstruidos)', '100', '103'),
  ('5', 'Mantenimento  manual de vallados  (con personal operativo)', 'OPA 5: Mantenimento  manual de vallados  (con personal operativo)', '100', '103'),
  ('1', 'Emisión de ruido por fuentes fijas', 'OPA 1: Emisión de ruido por fuentes fijas', '100', '108'),
  ('2', 'Material particulado, olores ofensivos', 'OPA 2: Material particulado, olores ofensivos', '100', '108')
) AS v(code, name, description, dep_code, subdep_code)
JOIN dependencies d ON d.code = v.dep_code
LEFT JOIN subdependencies s ON s.dependency_id = d.id AND s.code = v.subdep_code
WHERE NOT EXISTS (
    SELECT 1 FROM opas o 
    WHERE o.code = v.code 
    AND o.dependency_id = d.id 
    AND (o.subdependency_id = s.id OR (o.subdependency_id IS NULL AND s.id IS NULL))
);

-- Verificar carga
SELECT 
    'Secretaria de Medio Ambiente' as dependencia,
    COUNT(*) as opas_cargados
FROM opas o
JOIN dependencies d ON o.dependency_id = d.id
WHERE d.code = '100';

COMMIT;
