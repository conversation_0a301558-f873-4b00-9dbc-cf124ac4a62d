-- CARGA OPAs PARA Secretaria de Desarrollo Social (57 OPAs)
BEGIN;

INSERT INTO opas (code, name, description, dependency_id, subdependency_id, is_active)
SELECT v.code, v.name, v.description, d.id, s.id, true
FROM (VALUES
  ('1', 'Consejo municipal de politica fiscal', 'OPA 1: Consejo municipal de politica fiscal', '060', '060'),
  ('2', 'Lucha contra la pobreza extrema', 'OPA 2: Lucha contra la pobreza extrema', '060', '060'),
  ('3', 'Temas realacionados con voluntariados', 'OPA 3: Temas realacionados con voluntariados', '060', '060'),
  ('4', 'Grupos vulnerables', 'OPA 4: Grupos vulnerables', '060', '060'),
  ('5', 'Préstamo de la Biblioteca HOQABIGA', 'OPA 5: Préstamo de la Biblioteca HOQABIGA', '060', '060'),
  ('6', 'Comunicaciones dirigidas a la Gestora Social', 'OPA 6: Comunicaciones dirigidas a la Gestora Social', '060', '060'),
  ('1', 'solicitud de espacios para procesos juveniles', 'OPA 1: solicitud de espacios para procesos juveniles', '060', '061'),
  ('2', 'Sistema nacional de información sobre la juventud', 'OPA 2: Sistema nacional de información sobre la juventud', '060', '061'),
  ('3', 'Bienestar integral de los y las jóvenes', 'OPA 3: Bienestar integral de los y las jóvenes', '060', '061'),
  ('4', 'Derechos de las juventudes', 'OPA 4: Derechos de las juventudes', '060', '061'),
  ('5', 'Plataforma municipal de juventudes', 'OPA 5: Plataforma municipal de juventudes', '060', '061'),
  ('6', 'Gobierno municipal estudiantil', 'OPA 6: Gobierno municipal estudiantil', '060', '061'),
  ('7', 'Consejo municipal de juventud', 'OPA 7: Consejo municipal de juventud', '060', '061'),
  ('8', 'Jovenes en accion exclusivo de esta dirección', 'OPA 8: Jovenes en accion exclusivo de esta dirección', '060', '061'),
  ('1', 'Acciones de responsabilidad social empresarial, academica y gubernamental', 'OPA 1: Acciones de responsabilidad social empresarial, academica y gubernamental', '060', '062'),
  ('2', 'programas y proyectos mujer y género', 'OPA 2: programas y proyectos mujer y género', '060', '062'),
  ('3', 'concejo consultivo de la mujer', 'OPA 3: concejo consultivo de la mujer', '060', '062'),
  ('4', 'Programas y proyectos infancia', 'OPA 4: Programas y proyectos infancia', '060', '062'),
  ('5', 'Programas y proyectos adolescencia', 'OPA 5: Programas y proyectos adolescencia', '060', '062'),
  ('6', 'Programas y proyectos familia', 'OPA 6: Programas y proyectos familia', '060', '062'),
  ('7', 'Formación de familias', 'OPA 7: Formación de familias', '060', '062'),
  ('8', 'Pautas de crianza', 'OPA 8: Pautas de crianza', '060', '062'),
  ('9', 'Programas y proyectos adulto mayor', 'OPA 9: Programas y proyectos adulto mayor', '060', '062'),
  ('10', 'Uso del tiempo libre adulto mayor', 'OPA 10: Uso del tiempo libre adulto mayor', '060', '062'),
  ('11', 'Adulto mayor en estado de abandono', 'OPA 11: Adulto mayor en estado de abandono', '060', '062'),
  ('12', 'Beneficencia', 'OPA 12: Beneficencia', '060', '062'),
  ('13', 'Colombia mayor', 'OPA 13: Colombia mayor', '060', '062'),
  ('14', 'Familias en acción', 'OPA 14: Familias en acción', '060', '062'),
  ('15', 'Programas y proyectos población con discapacidad, población indigena, etnias', 'OPA 15: Programas y proyectos población con discapacidad, población indigena, etnias', '060', '062'),
  ('16', 'actualizacion de sistemas de información de la población frente al desarrollo humano y social', 'OPA 16: actualizacion de sistemas de información de la población frente al desarrollo humano y social', '060', '062'),
  ('17', 'Politícas sociales de envejecimiento, seguridad alimentaria y nutricional', 'OPA 17: Politícas sociales de envejecimiento, seguridad alimentaria y nutricional', '060', '062'),
  ('18', 'Jardines sociales', 'OPA 18: Jardines sociales', '060', '062'),
  ('19', 'Centro de Desarrollo Infantil', 'OPA 19: Centro de Desarrollo Infantil', '060', '062'),
  ('20', 'Madres Gestantes', 'OPA 20: Madres Gestantes', '060', '062'),
  ('21', 'Desnutrición de niños', 'OPA 21: Desnutrición de niños', '060', '062'),
  ('22', 'Ingreso solidario', 'OPA 22: Ingreso solidario', '060', '062'),
  ('23', 'Vigilancia y control Hogar Geriatrico San Rafael', 'OPA 23: Vigilancia y control Hogar Geriatrico San Rafael', '060', '062'),
  ('24', 'INFORMACIÓN SOBRE RENTA CIUDADANA', 'OPA 24: INFORMACIÓN SOBRE RENTA CIUDADANA', '060', '062'),
  ('25', 'Certificado de habitante de calle', 'OPA 25: Certificado de habitante de calle', '060', '062'),
  ('26', 'Abandono adulto mayor y vulnerabilidad(Derecho de petición)', 'OPA 26: Abandono adulto mayor y vulnerabilidad(Derecho de petición)', '060', '062'),
  ('1', 'Desarrollo cultural , acceso a los bienes y servicios de cultura', 'OPA 1: Desarrollo cultural , acceso a los bienes y servicios de cultura', '060', '063'),
  ('2', 'prestamo de trajes- presentaciones artisticas', 'OPA 2: prestamo de trajes- presentaciones artisticas', '060', '063'),
  ('3', 'Sistema de información Cultural', 'OPA 3: Sistema de información Cultural', '060', '063'),
  ('4', 'incentivo de la libre creación cultural', 'OPA 4: incentivo de la libre creación cultural', '060', '063'),
  ('5', 'Declaratoria y manejo de bienes de interés cultural', 'OPA 5: Declaratoria y manejo de bienes de interés cultural', '060', '063'),
  ('6', 'Asesoría cultural a instituciones del municipio', 'OPA 6: Asesoría cultural a instituciones del municipio', '060', '063'),
  ('7', 'Escuela de técnica de artes del Municipio', 'OPA 7: Escuela de técnica de artes del Municipio', '060', '063'),
  ('8', 'Biblioteca y su red', 'OPA 8: Biblioteca y su red', '060', '063'),
  ('9', 'Consejos de cultura y sistema nacional de cultura', 'OPA 9: Consejos de cultura y sistema nacional de cultura', '060', '063'),
  ('10', 'Estimulos y premios para el reconocimiento de artistas y autores.', 'OPA 10: Estimulos y premios para el reconocimiento de artistas y autores.', '060', '063'),
  ('11', 'Registro de bienes de interes cultural', 'OPA 11: Registro de bienes de interes cultural', '060', '063'),
  ('12', 'Protección y conservación del patrimonio cultural', 'OPA 12: Protección y conservación del patrimonio cultural', '060', '063'),
  ('13', 'oferta artistica', 'OPA 13: oferta artistica', '060', '063'),
  ('14', 'eventos artisticos', 'OPA 14: eventos artisticos', '060', '063'),
  ('15', 'prestamo de tarima', 'OPA 15: prestamo de tarima', '060', '063'),
  ('16', 'Permisos  para pintura de murales', 'OPA 16: Permisos  para pintura de murales', '060', '063'),
  ('17', 'Madres Gestantes', 'OPA 17: Madres Gestantes', '060', '063')
) AS v(code, name, description, dep_code, subdep_code)
JOIN dependencies d ON d.code = v.dep_code
LEFT JOIN subdependencies s ON s.dependency_id = d.id AND s.code = v.subdep_code
WHERE NOT EXISTS (
    SELECT 1 FROM opas o 
    WHERE o.code = v.code 
    AND o.dependency_id = d.id 
    AND (o.subdependency_id = s.id OR (o.subdependency_id IS NULL AND s.id IS NULL))
);

-- Verificar carga
SELECT 
    'Secretaria de Desarrollo Social' as dependencia,
    COUNT(*) as opas_cargados
FROM opas o
JOIN dependencies d ON o.dependency_id = d.id
WHERE d.code = '060';

COMMIT;
