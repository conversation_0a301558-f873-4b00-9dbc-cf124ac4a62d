-- CARGAR TRÁMITES RESTANTES (desde el 21 en adelante)

-- Pensiones y beneficios docentes
INSERT INTO procedures (name, description, response_time, cost, dependency_id, subdependency_id, is_active)
SELECT 'Pensión post-mortem para beneficiarios de docentes oficiales', 'Pensión post-mortem para beneficiarios de docentes oficiales', '2 meses', 0, d.id, s.id, true
FROM dependencies d LEFT JOIN subdependencies s ON s.dependency_id = d.id AND s.code = '073' WHERE d.code = '070';

INSERT INTO procedures (name, description, response_time, cost, dependency_id, subdependency_id, is_active)
SELECT 'Reliquidación pensional para docentes oficiales', 'Reliquidación pensional para docentes oficiales', '2 meses', 0, d.id, s.id, true
FROM dependencies d LEFT JOIN subdependencies s ON s.dependency_id = d.id AND s.code = '073' WHERE d.code = '070';

INSERT INTO procedures (name, description, response_time, cost, dependency_id, subdependency_id, is_active)
SELECT 'Seguro por muerte a beneficiarios de docentes oficiales', 'Seguro por muerte a beneficiarios de docentes oficiales', '2 meses', 0, d.id, s.id, true
FROM dependencies d LEFT JOIN subdependencies s ON s.dependency_id = d.id AND s.code = '073' WHERE d.code = '070';

INSERT INTO procedures (name, description, response_time, cost, dependency_id, subdependency_id, is_active)
SELECT 'Sustitución pensional para docentes oficiales', 'Sustitución pensional para docentes oficiales', '2 meses', 0, d.id, s.id, true
FROM dependencies d LEFT JOIN subdependencies s ON s.dependency_id = d.id AND s.code = '073' WHERE d.code = '070';

-- Servicios educativos
INSERT INTO procedures (name, description, response_time, cost, dependency_id, subdependency_id, is_active)
SELECT 'Ampliación del servicio educativo', 'Ampliación del servicio educativo', '60 días hábiles', 0, d.id, s.id, true
FROM dependencies d LEFT JOIN subdependencies s ON s.dependency_id = d.id AND s.code = '071' WHERE d.code = '070';

INSERT INTO procedures (name, description, response_time, cost, dependency_id, subdependency_id, is_active)
SELECT 'Cambio de nombre o razón social de un establecimiento educativo privado', 'Cambio de nombre o razón social de un establecimiento educativo privado', '30 días hábiles', 0, d.id, s.id, true
FROM dependencies d LEFT JOIN subdependencies s ON s.dependency_id = d.id AND s.code = '071' WHERE d.code = '070';

INSERT INTO procedures (name, description, response_time, cost, dependency_id, subdependency_id, is_active)
SELECT 'Cambio de propietario de un establecimiento educativo', 'Cambio de propietario de un establecimiento educativo', '30 días hábiles', 0, d.id, s.id, true
FROM dependencies d LEFT JOIN subdependencies s ON s.dependency_id = d.id AND s.code = '071' WHERE d.code = '070';

INSERT INTO procedures (name, description, response_time, cost, dependency_id, subdependency_id, is_active)
SELECT 'Cambio de sede de un establecimiento educativo', 'Cambio de sede de un establecimiento educativo', '60 días hábiles', 0, d.id, s.id, true
FROM dependencies d LEFT JOIN subdependencies s ON s.dependency_id = d.id AND s.code = '071' WHERE d.code = '070';

INSERT INTO procedures (name, description, response_time, cost, dependency_id, subdependency_id, is_active)
SELECT 'Certificado de existencia y representación legal de las instituciones de educación para el trabajo y el desarrollo humano', 'Certificado de existencia y representación legal de las instituciones de educación para el trabajo y el desarrollo humano', '15 días hábiles', 0, d.id, s.id, true
FROM dependencies d LEFT JOIN subdependencies s ON s.dependency_id = d.id AND s.code = '071' WHERE d.code = '070';

INSERT INTO procedures (name, description, response_time, cost, dependency_id, subdependency_id, is_active)
SELECT 'Cierre temporal o definitivo de programas de educación para el trabajo y el desarrollo humano', 'Cierre temporal o definitivo de programas de educación para el trabajo y el desarrollo humano', '30 días hábiles', 0, d.id, s.id, true
FROM dependencies d LEFT JOIN subdependencies s ON s.dependency_id = d.id AND s.code = '071' WHERE d.code = '070';
