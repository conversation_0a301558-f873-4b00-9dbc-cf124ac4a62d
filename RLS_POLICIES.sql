-- =====================================================
-- POLÍTICAS DE SEGURIDAD RLS (ROW LEVEL SECURITY)
-- =====================================================
-- Versión: 1.0
-- Fecha: 2025-06-30
-- Descripción: Políticas de seguridad a nivel de fila para Supabase

-- =====================================================
-- HABILITAR RLS EN TODAS LAS TABLAS
-- =====================================================

-- Tablas de configuración
ALTER TABLE roles ENABLE ROW LEVEL SECURITY;
ALTER TABLE dependencies ENABLE ROW LEVEL SECURITY;
ALTER TABLE subdependencies ENABLE ROW LEVEL SECURITY;
ALTER TABLE procedure_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE procedure_statuses ENABLE ROW LEVEL SECURITY;
ALTER TABLE notification_types ENABLE ROW LEVEL SECURITY;
ALTER TABLE system_settings ENABLE ROW LEVEL SECURITY;

-- Tablas de usuarios
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- Tablas de trámites
ALTER TABLE procedures ENABLE ROW LEVEL SECURITY;
ALTER TABLE administrative_procedures ENABLE ROW LEVEL SECURITY;
ALTER TABLE citizen_procedures ENABLE ROW LEVEL SECURITY;
ALTER TABLE procedure_status_history ENABLE ROW LEVEL SECURITY;

-- Tablas de IA
ALTER TABLE knowledge_base ENABLE ROW LEVEL SECURITY;
ALTER TABLE chat_conversations ENABLE ROW LEVEL SECURITY;
ALTER TABLE chat_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_feedback ENABLE ROW LEVEL SECURITY;

-- Tablas de notificaciones
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;

-- Tablas de auditoría
ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- FUNCIONES AUXILIARES PARA POLÍTICAS
-- =====================================================

-- Función para obtener el rol del usuario actual
CREATE OR REPLACE FUNCTION get_user_role()
RETURNS TEXT AS $$
BEGIN
    RETURN (
        SELECT r.name
        FROM profiles p
        JOIN roles r ON p.role_id = r.id
        WHERE p.id = auth.uid()
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Función para verificar si el usuario es super admin
CREATE OR REPLACE FUNCTION is_super_admin()
RETURNS BOOLEAN AS $$
BEGIN
    RETURN get_user_role() = 'super_admin';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Función para verificar si el usuario es admin de una dependencia
CREATE OR REPLACE FUNCTION is_dependency_admin(dep_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN (
        SELECT COUNT(*) > 0
        FROM profiles p
        JOIN roles r ON p.role_id = r.id
        WHERE p.id = auth.uid()
        AND r.name IN ('admin', 'super_admin')
        AND (r.name = 'super_admin' OR p.dependency_id = dep_id)
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- POLÍTICAS PARA TABLAS DE CONFIGURACIÓN
-- =====================================================

-- Roles: Solo lectura para todos, escritura para super_admin
CREATE POLICY "Anyone can read roles" ON roles
    FOR SELECT USING (true);

CREATE POLICY "Only super_admin can modify roles" ON roles
    FOR ALL USING (is_super_admin());

-- Dependencias: Lectura para todos, escritura para super_admin
CREATE POLICY "Anyone can read dependencies" ON dependencies
    FOR SELECT USING (is_active = true);

CREATE POLICY "Only super_admin can modify dependencies" ON dependencies
    FOR ALL USING (is_super_admin());

-- Subdependencias: Lectura para todos, escritura para admins de la dependencia
CREATE POLICY "Anyone can read subdependencies" ON subdependencies
    FOR SELECT USING (is_active = true);

CREATE POLICY "Dependency admins can modify subdependencies" ON subdependencies
    FOR ALL USING (is_dependency_admin(dependency_id));

-- Categorías de trámites: Lectura para todos, escritura para super_admin
CREATE POLICY "Anyone can read procedure categories" ON procedure_categories
    FOR SELECT USING (is_active = true);

CREATE POLICY "Only super_admin can modify procedure categories" ON procedure_categories
    FOR ALL USING (is_super_admin());

-- Estados de trámites: Solo lectura para todos
CREATE POLICY "Anyone can read procedure statuses" ON procedure_statuses
    FOR SELECT USING (true);

CREATE POLICY "Only super_admin can modify procedure statuses" ON procedure_statuses
    FOR ALL USING (is_super_admin());

-- =====================================================
-- POLÍTICAS PARA USUARIOS
-- =====================================================

-- Perfiles: Los usuarios pueden ver y editar su propio perfil
CREATE POLICY "Users can view own profile" ON profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON profiles
    FOR UPDATE USING (auth.uid() = id);

-- Los admins pueden ver perfiles de su dependencia
CREATE POLICY "Admins can view dependency profiles" ON profiles
    FOR SELECT USING (
        get_user_role() IN ('admin', 'super_admin') AND
        (is_super_admin() OR 
         dependency_id = (SELECT dependency_id FROM profiles WHERE id = auth.uid()))
    );

-- Solo super_admin puede crear/eliminar perfiles
CREATE POLICY "Only super_admin can insert profiles" ON profiles
    FOR INSERT WITH CHECK (is_super_admin());

CREATE POLICY "Only super_admin can delete profiles" ON profiles
    FOR DELETE USING (is_super_admin());

-- =====================================================
-- POLÍTICAS PARA TRÁMITES
-- =====================================================

-- Trámites: Lectura para todos los activos, escritura para admins de dependencia
CREATE POLICY "Anyone can read active procedures" ON procedures
    FOR SELECT USING (is_active = true);

CREATE POLICY "Dependency admins can modify procedures" ON procedures
    FOR ALL USING (is_dependency_admin(dependency_id));

-- OPA: Similar a trámites
CREATE POLICY "Anyone can read active OPA" ON administrative_procedures
    FOR SELECT USING (is_active = true);

CREATE POLICY "Dependency admins can modify OPA" ON administrative_procedures
    FOR ALL USING (is_dependency_admin(dependency_id));

-- Trámites ciudadanos: Los ciudadanos solo ven los suyos, admins ven los de su dependencia
CREATE POLICY "Citizens can view own procedures" ON citizen_procedures
    FOR SELECT USING (
        citizen_id = auth.uid() OR
        (get_user_role() IN ('admin', 'super_admin') AND
         (is_super_admin() OR 
          EXISTS (
              SELECT 1 FROM procedures p 
              WHERE p.id = procedure_id 
              AND is_dependency_admin(p.dependency_id)
          )))
    );

CREATE POLICY "Citizens can create own procedures" ON citizen_procedures
    FOR INSERT WITH CHECK (citizen_id = auth.uid());

CREATE POLICY "Citizens can update own procedures" ON citizen_procedures
    FOR UPDATE USING (
        citizen_id = auth.uid() OR
        (get_user_role() IN ('admin', 'super_admin') AND
         EXISTS (
             SELECT 1 FROM procedures p 
             WHERE p.id = procedure_id 
             AND is_dependency_admin(p.dependency_id)
         ))
    );

-- Historial de estados: Solo lectura para ciudadanos, escritura para admins
CREATE POLICY "Users can view procedure history" ON procedure_status_history
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM citizen_procedures cp
            WHERE cp.id = citizen_procedure_id
            AND (cp.citizen_id = auth.uid() OR
                 (get_user_role() IN ('admin', 'super_admin') AND
                  EXISTS (
                      SELECT 1 FROM procedures p 
                      WHERE p.id = cp.procedure_id 
                      AND is_dependency_admin(p.dependency_id)
                  )))
        )
    );

CREATE POLICY "Admins can create procedure history" ON procedure_status_history
    FOR INSERT WITH CHECK (
        get_user_role() IN ('admin', 'super_admin') AND
        EXISTS (
            SELECT 1 FROM citizen_procedures cp
            JOIN procedures p ON cp.procedure_id = p.id
            WHERE cp.id = citizen_procedure_id
            AND is_dependency_admin(p.dependency_id)
        )
    );

-- =====================================================
-- POLÍTICAS PARA SISTEMA DE IA
-- =====================================================

-- Base de conocimientos: Lectura para todos los activos, escritura para admins
CREATE POLICY "Anyone can read active knowledge base" ON knowledge_base
    FOR SELECT USING (is_active = true);

CREATE POLICY "Admins can modify knowledge base" ON knowledge_base
    FOR ALL USING (get_user_role() IN ('admin', 'super_admin'));

-- Conversaciones de chat: Los usuarios solo ven las suyas
CREATE POLICY "Users can view own conversations" ON chat_conversations
    FOR SELECT USING (user_id = auth.uid() OR user_id IS NULL);

CREATE POLICY "Users can create conversations" ON chat_conversations
    FOR INSERT WITH CHECK (user_id = auth.uid() OR user_id IS NULL);

CREATE POLICY "Users can update own conversations" ON chat_conversations
    FOR UPDATE USING (user_id = auth.uid() OR user_id IS NULL);

-- Mensajes de chat: Los usuarios solo ven los de sus conversaciones
CREATE POLICY "Users can view own chat messages" ON chat_messages
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM chat_conversations cc
            WHERE cc.id = conversation_id
            AND (cc.user_id = auth.uid() OR cc.user_id IS NULL)
        )
    );

CREATE POLICY "Users can create chat messages" ON chat_messages
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM chat_conversations cc
            WHERE cc.id = conversation_id
            AND (cc.user_id = auth.uid() OR cc.user_id IS NULL)
        )
    );

-- Feedback de IA: Los usuarios pueden dar feedback en sus mensajes
CREATE POLICY "Users can view own AI feedback" ON ai_feedback
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can create AI feedback" ON ai_feedback
    FOR INSERT WITH CHECK (
        user_id = auth.uid() AND
        EXISTS (
            SELECT 1 FROM chat_messages cm
            JOIN chat_conversations cc ON cm.conversation_id = cc.id
            WHERE cm.id = message_id
            AND (cc.user_id = auth.uid() OR cc.user_id IS NULL)
        )
    );

-- =====================================================
-- POLÍTICAS PARA NOTIFICACIONES
-- =====================================================

-- Tipos de notificaciones: Solo lectura para todos
CREATE POLICY "Anyone can read notification types" ON notification_types
    FOR SELECT USING (is_active = true);

CREATE POLICY "Only super_admin can modify notification types" ON notification_types
    FOR ALL USING (is_super_admin());

-- Notificaciones: Los usuarios solo ven las suyas
CREATE POLICY "Users can view own notifications" ON notifications
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can update own notifications" ON notifications
    FOR UPDATE USING (user_id = auth.uid());

-- Los admins pueden crear notificaciones
CREATE POLICY "Admins can create notifications" ON notifications
    FOR INSERT WITH CHECK (get_user_role() IN ('admin', 'super_admin'));

-- =====================================================
-- POLÍTICAS PARA AUDITORÍA
-- =====================================================

-- Logs de auditoría: Los usuarios pueden ver sus propias acciones, admins ven más
CREATE POLICY "Users can view own audit logs" ON audit_logs
    FOR SELECT USING (
        user_id = auth.uid() OR
        (get_user_role() IN ('admin', 'super_admin') AND
         (is_super_admin() OR 
          EXISTS (
              SELECT 1 FROM profiles p
              WHERE p.id = audit_logs.user_id
              AND p.dependency_id = (SELECT dependency_id FROM profiles WHERE id = auth.uid())
          )))
    );

-- Solo el sistema puede insertar logs de auditoría
CREATE POLICY "System can insert audit logs" ON audit_logs
    FOR INSERT WITH CHECK (true);

-- =====================================================
-- POLÍTICAS PARA CONFIGURACIÓN
-- =====================================================

-- Configuraciones del sistema: Lectura pública para algunas, escritura solo para super_admin
CREATE POLICY "Anyone can read public settings" ON system_settings
    FOR SELECT USING (is_public = true);

CREATE POLICY "Admins can read all settings" ON system_settings
    FOR SELECT USING (get_user_role() IN ('admin', 'super_admin'));

CREATE POLICY "Only super_admin can modify settings" ON system_settings
    FOR ALL USING (is_super_admin());

-- =====================================================
-- TRIGGERS PARA AUDITORÍA AUTOMÁTICA
-- =====================================================

-- Función para crear logs de auditoría automáticamente
CREATE OR REPLACE FUNCTION create_audit_log()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO audit_logs (
        user_id,
        action,
        table_name,
        record_id,
        old_values,
        new_values,
        ip_address
    ) VALUES (
        auth.uid(),
        TG_OP,
        TG_TABLE_NAME,
        COALESCE(NEW.id, OLD.id),
        CASE WHEN TG_OP = 'DELETE' THEN to_jsonb(OLD) ELSE NULL END,
        CASE WHEN TG_OP IN ('INSERT', 'UPDATE') THEN to_jsonb(NEW) ELSE NULL END,
        inet_client_addr()
    );
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Aplicar triggers de auditoría a tablas críticas
CREATE TRIGGER audit_profiles AFTER INSERT OR UPDATE OR DELETE ON profiles
    FOR EACH ROW EXECUTE FUNCTION create_audit_log();

CREATE TRIGGER audit_citizen_procedures AFTER INSERT OR UPDATE OR DELETE ON citizen_procedures
    FOR EACH ROW EXECUTE FUNCTION create_audit_log();

CREATE TRIGGER audit_procedures AFTER INSERT OR UPDATE OR DELETE ON procedures
    FOR EACH ROW EXECUTE FUNCTION create_audit_log();

-- =====================================================
-- FUNCIONES PARA ACTUALIZACIÓN AUTOMÁTICA DE TIMESTAMPS
-- =====================================================

-- Función para actualizar updated_at automáticamente
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Aplicar trigger de updated_at a todas las tablas que lo necesiten
CREATE TRIGGER update_profiles_updated_at BEFORE UPDATE ON profiles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_procedures_updated_at BEFORE UPDATE ON procedures
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_citizen_procedures_updated_at BEFORE UPDATE ON citizen_procedures
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_dependencies_updated_at BEFORE UPDATE ON dependencies
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_subdependencies_updated_at BEFORE UPDATE ON subdependencies
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- ÍNDICES ADICIONALES PARA RENDIMIENTO
-- =====================================================

-- Índices para consultas frecuentes
CREATE INDEX idx_profiles_role_dependency ON profiles(role_id, dependency_id);
CREATE INDEX idx_citizen_procedures_citizen_status ON citizen_procedures(citizen_id, status_id);
CREATE INDEX idx_citizen_procedures_tracking ON citizen_procedures(tracking_number);
CREATE INDEX idx_procedures_dependency ON procedures(dependency_id, is_active);
CREATE INDEX idx_notifications_user_read ON notifications(user_id, is_read);
CREATE INDEX idx_chat_conversations_user ON chat_conversations(user_id, is_active);

-- Índices para búsqueda de texto
CREATE INDEX idx_procedures_name_trgm ON procedures USING gin(name gin_trgm_ops);
CREATE INDEX idx_knowledge_base_content_trgm ON knowledge_base USING gin(content gin_trgm_ops);
