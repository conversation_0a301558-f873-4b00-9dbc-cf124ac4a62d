#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para completar la carga de todos los OPAs restantes
Ejecuta los archivos SQL generados uno por uno
"""

import json
import sys
import re
import os
import time

def get_remaining_dependencies():
    """Obtiene las dependencias que faltan por cargar"""
    
    # Lista de archivos SQL generados
    sql_files = [
        ('010', 'load_opas_010_Secretaria_de_Planeacion.sql', 45),
        ('020', 'load_opas_020_Secretaria_General.sql', 81),
        ('030', 'load_opas_030_Secretaria_de_Gobierno.sql', 93),
        ('040', 'load_opas_040_Secretaria_de_Hacienda.sql', 109),
        ('050', 'load_opas_050_Secretaria_de_Obras_Publicas.sql', 39),
        ('060', 'load_opas_060_Secretaria_de_Desarrollo_Social.sql', 57),
        ('070', 'load_opas_070_Secretaria_de_Educacion.sql', 69),
        ('080', 'load_opas_080_Secretaria_de_Salud.sql', 38),
        ('090', 'load_opas_090_Secretaria_para_el_Desarrollo_Economico.sql', 29),
        ('100', 'load_opas_100_Secretaria_de_Medio_Ambiente.sql', 46),
        ('110', 'load_opas_110_Secretaria_de_Movilidad.sql', 49)
    ]
    
    return sql_files

def extract_sql_content(filename):
    """Extrae el contenido SQL de un archivo"""
    
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Extraer solo la parte del INSERT
        lines = content.split('\n')
        sql_lines = []
        in_insert = False
        
        for line in lines:
            if line.strip().startswith('INSERT INTO opas'):
                in_insert = True
            
            if in_insert:
                sql_lines.append(line)
                
            if in_insert and line.strip() == ');':
                break
        
        return '\n'.join(sql_lines)
        
    except Exception as e:
        print(f"❌ Error leyendo {filename}: {e}")
        return None

def create_batch_sql():
    """Crea archivos SQL en lotes más pequeños"""
    
    remaining_deps = get_remaining_dependencies()
    
    # Dividir en lotes de 3 dependencias
    batch_size = 3
    batches = []
    
    for i in range(0, len(remaining_deps), batch_size):
        batch = remaining_deps[i:i + batch_size]
        batches.append(batch)
    
    print(f"🔄 Creando {len(batches)} lotes para carga...")
    
    for batch_num, batch in enumerate(batches, 1):
        batch_sql = f"""-- LOTE {batch_num} DE CARGA DE OPAs
BEGIN;

"""
        total_opas_batch = 0
        
        for dep_code, filename, opa_count in batch:
            if os.path.exists(filename):
                sql_content = extract_sql_content(filename)
                if sql_content:
                    batch_sql += f"""
-- {filename} ({opa_count} OPAs)
{sql_content}

"""
                    total_opas_batch += opa_count
                else:
                    print(f"⚠️  No se pudo extraer SQL de {filename}")
            else:
                print(f"⚠️  Archivo no encontrado: {filename}")
        
        batch_sql += f"""
-- Verificar carga del lote {batch_num}
SELECT 
    'LOTE {batch_num} COMPLETADO' as estado,
    COUNT(*) as total_opas_en_lote
FROM opas o
JOIN dependencies d ON o.dependency_id = d.id
WHERE d.code IN ({', '.join([f"'{dep[0]}'" for dep in batch])});

COMMIT;
"""
        
        # Guardar archivo del lote
        batch_filename = f"batch_opa_load_{batch_num:02d}.sql"
        with open(batch_filename, 'w', encoding='utf-8') as f:
            f.write(batch_sql)
        
        print(f"✅ {batch_filename} - {total_opas_batch} OPAs")
    
    # Crear script de verificación final
    verification_sql = """-- VERIFICACIÓN FINAL DE CARGA COMPLETA
SELECT 
    'CARGA COMPLETA DE OPAs' as titulo,
    COUNT(*) as total_opas_cargados,
    COUNT(DISTINCT dependency_id) as dependencias_con_opas,
    COUNT(DISTINCT subdependency_id) as subdependencias_con_opas
FROM opas;

-- Estadísticas finales por dependencia
SELECT 
    d.name as dependencia,
    d.code as codigo,
    COUNT(o.id) as opas_cargados,
    ROUND(COUNT(o.id) * 100.0 / 721, 2) as porcentaje_del_total
FROM dependencies d
LEFT JOIN opas o ON o.dependency_id = d.id
WHERE o.id IS NOT NULL
GROUP BY d.id, d.name, d.code
ORDER BY opas_cargados DESC;

-- Progreso final
SELECT 
    721 as total_opas_esperados,
    COUNT(*) as opas_cargados,
    ROUND(COUNT(*) * 100.0 / 721, 2) as porcentaje_completado,
    (721 - COUNT(*)) as opas_pendientes,
    CASE 
        WHEN COUNT(*) = 721 THEN '🎉 CARGA COMPLETA!'
        ELSE '⚠️ Faltan OPAs por cargar'
    END as estado
FROM opas;

-- Vista unificada de todos los procedimientos
SELECT 
    'RESUMEN FINAL' as titulo,
    COUNT(*) as total_procedures,
    SUM(CASE WHEN type = 'TRAMITE' THEN 1 ELSE 0 END) as tramites,
    SUM(CASE WHEN type = 'OPA' THEN 1 ELSE 0 END) as opas
FROM all_procedures;
"""
    
    with open('verify_complete_load.sql', 'w', encoding='utf-8') as f:
        f.write(verification_sql)
    
    print(f"\n🎉 Archivos de lote creados exitosamente!")
    print(f"📁 Ejecutar en orden: batch_opa_load_01.sql, batch_opa_load_02.sql, etc.")
    print(f"🔍 Verificación final: verify_complete_load.sql")

if __name__ == "__main__":
    create_batch_sql()
