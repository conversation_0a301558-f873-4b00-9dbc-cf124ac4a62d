#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Análisis de estructura del archivo OPA-chia-optimo.json
Cuenta y analiza todos los OPAs por dependencia
"""

import json
import sys

def analyze_opa_structure():
    """Analiza la estructura del archivo OPA y cuenta elementos"""
    
    try:
        # Leer el archivo JSON
        with open('OPA-chia-optimo.json', 'r', encoding='utf-8') as file:
            data = json.load(file)
        
        total_opas = 0
        dependencies_with_opas = 0
        subdependencies_with_opas = 0
        
        print("=== ANÁLISIS DE ESTRUCTURA OPA ===\n")
        
        # Analizar cada dependencia
        for dep_code, dep_data in data['dependencias'].items():
            dep_name = dep_data['nombre']
            dep_opa_count = 0
            dep_subdeps = 0
            
            print(f"📁 DEPENDENCIA {dep_code}: {dep_name}")
            
            # <PERSON><PERSON><PERSON> subdependencias
            if 'subdependencias' in dep_data:
                for subdep_code, subdep_data in dep_data['subdependencias'].items():
                    subdep_name = subdep_data['nombre']
                    
                    if 'OPA' in subdep_data and subdep_data['OPA']:
                        opa_count = len(subdep_data['OPA'])
                        dep_opa_count += opa_count
                        dep_subdeps += 1
                        subdependencies_with_opas += 1
                        
                        print(f"  └── {subdep_code}: {subdep_name} ({opa_count} OPAs)")
                        
                        # Mostrar algunos ejemplos de OPAs
                        for i, opa in enumerate(subdep_data['OPA'][:3]):  # Solo primeros 3
                            opa_text = opa['OPA'][:80] + "..." if len(opa['OPA']) > 80 else opa['OPA']
                            print(f"      • {opa['codigo_OPA']}: {opa_text}")
                        
                        if len(subdep_data['OPA']) > 3:
                            print(f"      ... y {len(subdep_data['OPA']) - 3} más")
            
            if dep_opa_count > 0:
                dependencies_with_opas += 1
                total_opas += dep_opa_count
                print(f"  📊 Total OPAs en {dep_name}: {dep_opa_count}")
            else:
                print(f"  ❌ Sin OPAs")
            
            print()
        
        # Resumen final
        print("=== RESUMEN FINAL ===")
        print(f"📈 Total de OPAs encontrados: {total_opas}")
        print(f"🏛️ Dependencias con OPAs: {dependencies_with_opas}")
        print(f"📂 Subdependencias con OPAs: {subdependencies_with_opas}")
        print(f"📊 Promedio OPAs por subdependencia: {total_opas/subdependencies_with_opas:.2f}")
        
        return {
            'total_opas': total_opas,
            'dependencies_with_opas': dependencies_with_opas,
            'subdependencies_with_opas': subdependencies_with_opas,
            'data': data
        }
        
    except Exception as e:
        print(f"❌ Error al procesar archivo: {e}")
        return None

if __name__ == "__main__":
    result = analyze_opa_structure()
    if result:
        print(f"\n✅ Análisis completado exitosamente!")
        print(f"📋 Datos listos para carga en base de datos")
