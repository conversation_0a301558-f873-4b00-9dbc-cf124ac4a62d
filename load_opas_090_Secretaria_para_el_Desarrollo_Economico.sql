-- CARGA OPAs PARA Secretaria para el Desarrollo Economico (29 OPAs)
BEGIN;

INSERT INTO opas (code, name, description, dependency_id, subdependency_id, is_active)
SELECT v.code, v.name, v.description, d.id, s.id, true
FROM (VALUES
  ('1', 'General', 'OPA 1: General', '090', '090'),
  ('1', 'Administracion plaza de mercado, vivero (centro de investigacion y desarrollo y tecnologia agropecuaria), Planta de Sacrificio y Faenado', 'OPA 1: Administracion plaza de mercado, vivero (centro de investigacion y desarrollo y tecnologia agropecuaria), Planta de Sacrificio y Faenado', '090', '091'),
  ('2', 'Centro de bienestar animal y COSO municipal', 'OPA 2: Centro de bienestar animal y COSO municipal', '090', '091'),
  ('3', 'Plan estrategico agropecuario, estadisticas agricolas', 'OPA 3: Plan estrategico agropecuario, estadisticas agricolas', '090', '091'),
  ('4', 'Asistencia tecnica rural para el sector agropecuario:  animales para consumo humano(pollos, conejos, codornices, lombrices, patos, piscos, pavos, vacas, cerdos)', 'OPA 4: Asistencia tecnica rural para el sector agropecuario:  animales para consumo humano(pollos, conejos, codornices, lombrices, patos, piscos, pavos, vacas, cerdos)', '090', '091'),
  ('5', 'Huertas urbanas', 'OPA 5: Huertas urbanas', '090', '091'),
  ('6', 'Seguridad y sostenibilidad alimentaria, plantulas y semillas de arboles frutales, hortalizas. *Semillas de plantas de produccion (arboles aromaticos (cidron, laurel, tomillo, plantulas como lechuga, cilantro)', 'OPA 6: Seguridad y sostenibilidad alimentaria, plantulas y semillas de arboles frutales, hortalizas. *Semillas de plantas de produccion (arboles aromaticos (cidron, laurel, tomillo, plantulas como lechuga, cilantro)', '090', '091'),
  ('7', 'Cadenas productivas y asociatividad empresarial, economia campesina', 'OPA 7: Cadenas productivas y asociatividad empresarial, economia campesina', '090', '091'),
  ('8', 'Programa Chia Emprende : Proyectos productivos de victimas, emprendimiento, ferias de emprendimiento, encuentros empresariales', 'OPA 8: Programa Chia Emprende : Proyectos productivos de victimas, emprendimiento, ferias de emprendimiento, encuentros empresariales', '090', '091'),
  ('9', 'Agencia publica de empleo: Desarrollo de competencias laborales, hojas de vida', 'OPA 9: Agencia publica de empleo: Desarrollo de competencias laborales, hojas de vida', '090', '091'),
  ('10', 'Bancarizacion y generacion de lineas de credito', 'OPA 10: Bancarizacion y generacion de lineas de credito', '090', '091'),
  ('11', 'Asistencia tecnica empresarial, proyectos de incubacion', 'OPA 11: Asistencia tecnica empresarial, proyectos de incubacion', '090', '091'),
  ('12', 'Solicitud de certificacion de actrividades agricolas predios  dedicados a actividades agrícolas y ganaderas', 'OPA 12: Solicitud de certificacion de actrividades agricolas predios  dedicados a actividades agrícolas y ganaderas', '090', '091'),
  ('13', 'Abandono o maltrato de especies mayores- pecuarias: (Vacas, cerdos, caballos, etc)', 'OPA 13: Abandono o maltrato de especies mayores- pecuarias: (Vacas, cerdos, caballos, etc)', '090', '091'),
  ('14', 'Impuesto al degüello de ganado mayor', 'OPA 14: Impuesto al degüello de ganado mayor', '090', '091'),
  ('15', 'Informacion general para apertura de establecimientos comerciales en el municipio', 'OPA 15: Informacion general para apertura de establecimientos comerciales en el municipio', '090', '091'),
  ('16', 'Solicitud de apoyo economico por reactivacion economica', 'OPA 16: Solicitud de apoyo economico por reactivacion economica', '090', '091'),
  ('1', 'Plan de desarrollo Turistico, proyectos de desarrollo turistico', 'OPA 1: Plan de desarrollo Turistico, proyectos de desarrollo turistico', '090', '092'),
  ('2', 'Fondo Nacional de Turismo- FONTUR', 'OPA 2: Fondo Nacional de Turismo- FONTUR', '090', '092'),
  ('3', 'Consejo municipal de Turismo, Consejo regional de seguridad turistica, policia de turismo', 'OPA 3: Consejo municipal de Turismo, Consejo regional de seguridad turistica, policia de turismo', '090', '092'),
  ('4', 'Festival Chia Gastronomica, ferias gastronomicas', 'OPA 4: Festival Chia Gastronomica, ferias gastronomicas', '090', '092'),
  ('5', 'Infraestructura turistica, señalizacion turistica', 'OPA 5: Infraestructura turistica, señalizacion turistica', '090', '092'),
  ('6', 'Vigilancia y control de prestadores de servicios turisticos frente al servicio (hoteles, agencias de viajes, guias turisticos, plataformas de comercializacion en linea, booking, Airbnb, apartamentos alquilados por dias), formalizacion turistica, superintendencia de industria y comercio en relacion con el turismo', 'OPA 6: Vigilancia y control de prestadores de servicios turisticos frente al servicio (hoteles, agencias de viajes, guias turisticos, plataformas de comercializacion en linea, booking, Airbnb, apartamentos alquilados por dias), formalizacion turistica, superintendencia de industria y comercio en relacion con el turismo', '090', '092'),
  ('7', 'Inventario de atractivos turisticos, rutas turisticas', 'OPA 7: Inventario de atractivos turisticos, rutas turisticas', '090', '092'),
  ('8', 'Zonas de desarrollo turistico prioritario', 'OPA 8: Zonas de desarrollo turistico prioritario', '090', '092'),
  ('9', 'Campañas de mercadeo y promocion turistica', 'OPA 9: Campañas de mercadeo y promocion turistica', '090', '092'),
  ('10', 'Capacitaciones para el sector turistico', 'OPA 10: Capacitaciones para el sector turistico', '090', '092'),
  ('11', 'Punto de informacion turistica', 'OPA 11: Punto de informacion turistica', '090', '092'),
  ('12', 'Registro nacional de turismo', 'OPA 12: Registro nacional de turismo', '090', '092')
) AS v(code, name, description, dep_code, subdep_code)
JOIN dependencies d ON d.code = v.dep_code
LEFT JOIN subdependencies s ON s.dependency_id = d.id AND s.code = v.subdep_code
WHERE NOT EXISTS (
    SELECT 1 FROM opas o 
    WHERE o.code = v.code 
    AND o.dependency_id = d.id 
    AND (o.subdependency_id = s.id OR (o.subdependency_id IS NULL AND s.id IS NULL))
);

-- Verificar carga
SELECT 
    'Secretaria para el Desarrollo Economico' as dependencia,
    COUNT(*) as opas_cargados
FROM opas o
JOIN dependencies d ON o.dependency_id = d.id
WHERE d.code = '090';

COMMIT;
