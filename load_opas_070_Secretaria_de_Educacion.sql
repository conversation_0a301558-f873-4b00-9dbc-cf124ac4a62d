-- CARGA OPAs PARA Secretaria de Educacion (69 OPAs)
BEGIN;

INSERT INTO opas (code, name, description, dependency_id, subdependency_id, is_active)
SELECT v.code, v.name, v.description, d.id, s.id, true
FROM (VALUES
  ('1', 'Se creó el tipo documental "COMUNICACIONES PARA SEC. EDUCACIÓN", el cual se debe seleccionar cuando lleguen derechos de petición o cualquier comunicación excepto tutelas para la Secretaría de Educación.', 'OPA 1: Se creó el tipo documental "COMUNICACIONES PARA SEC. EDUCACIÓN", el cual se debe seleccionar cuando lleguen derechos de petición o cualquier comunicación excepto tutelas para la Secretaría de Educación.', '070', '070'),
  ('1', 'Inspección, vigilancia y control instituciones educativas publicas', 'OPA 1: Inspección, vigilancia y control instituciones educativas publicas', '070', '071'),
  ('2', 'Inspección, vigilancia y control instituciones educativas privadas', 'OPA 2: Inspección, vigilancia y control instituciones educativas privadas', '070', '071'),
  ('3', 'Inspección, vigilancia y control entidades sin animo de lucro con fines educativos', 'OPA 3: Inspección, vigilancia y control entidades sin animo de lucro con fines educativos', '070', '071'),
  ('4', 'Inspección, vigilancia y control asociación de padres de familia', 'OPA 4: Inspección, vigilancia y control asociación de padres de familia', '070', '071'),
  ('5', 'Personerias juridicas instituciones educativas', 'OPA 5: Personerias juridicas instituciones educativas', '070', '071'),
  ('6', 'Investigaciones a establecimientos de educación formal', 'OPA 6: Investigaciones a establecimientos de educación formal', '070', '071'),
  ('7', 'Investigaciones instituciones de formación para el trabajo y desarrollo humano', 'OPA 7: Investigaciones instituciones de formación para el trabajo y desarrollo humano', '070', '071'),
  ('8', 'Investigaciones a entidades sin animo de lucro con fines educativos', 'OPA 8: Investigaciones a entidades sin animo de lucro con fines educativos', '070', '071'),
  ('9', 'Investigaciones a asociación de padres de familia', 'OPA 9: Investigaciones a asociación de padres de familia', '070', '071'),
  ('10', 'Costos educativos', 'OPA 10: Costos educativos', '070', '071'),
  ('11', 'Sanciones instituciones educativas', 'OPA 11: Sanciones instituciones educativas', '070', '071'),
  ('12', 'Actualización proyectos educativos institucionales', 'OPA 12: Actualización proyectos educativos institucionales', '070', '071'),
  ('13', 'Normas de convivencia escolar', 'OPA 13: Normas de convivencia escolar', '070', '071'),
  ('14', 'Seguimiento jornada escolar', 'OPA 14: Seguimiento jornada escolar', '070', '071'),
  ('15', 'Seguimiento jornada laboral establecimientos educativos oficiales', 'OPA 15: Seguimiento jornada laboral establecimientos educativos oficiales', '070', '071'),
  ('16', 'Sistemas de infomarmación sobre instituciones educativas', 'OPA 16: Sistemas de infomarmación sobre instituciones educativas', '070', '071'),
  ('17', 'Cierre de establecimientos educativos', 'OPA 17: Cierre de establecimientos educativos', '070', '071'),
  ('1', 'Procesos de aprendizaje', 'OPA 1: Procesos de aprendizaje', '070', '072'),
  ('2', 'Fortalecimiento de la educación', 'OPA 2: Fortalecimiento de la educación', '070', '072'),
  ('3', 'Procesos evaluativos', 'OPA 3: Procesos evaluativos', '070', '072'),
  ('4', 'Estrategias y metodologias', 'OPA 4: Estrategias y metodologias', '070', '072'),
  ('5', 'Programas de formación docente', 'OPA 5: Programas de formación docente', '070', '072'),
  ('6', 'Actualización docente', 'OPA 6: Actualización docente', '070', '072'),
  ('7', 'Integración de población campesina al sistema educativo', 'OPA 7: Integración de población campesina al sistema educativo', '070', '072'),
  ('8', 'Integración de población con talentos excepcionales al sistema educativo', 'OPA 8: Integración de población con talentos excepcionales al sistema educativo', '070', '072'),
  ('9', 'Integración de adultos al sistema educativo', 'OPA 9: Integración de adultos al sistema educativo', '070', '072'),
  ('10', 'Integración de población rural al sistema educativo', 'OPA 10: Integración de población rural al sistema educativo', '070', '072'),
  ('11', 'Integración de población con limitaciones al sistema educativo', 'OPA 11: Integración de población con limitaciones al sistema educativo', '070', '072'),
  ('12', 'Modelos de educación técnica', 'OPA 12: Modelos de educación técnica', '070', '072'),
  ('13', 'Modelos de educación tecnológica', 'OPA 13: Modelos de educación tecnológica', '070', '072'),
  ('14', 'Articulación con educación media', 'OPA 14: Articulación con educación media', '070', '072'),
  ('15', 'Articulación con educación superior', 'OPA 15: Articulación con educación superior', '070', '072'),
  ('16', 'Planes de mejoramiento de las instituciones educativas', 'OPA 16: Planes de mejoramiento de las instituciones educativas', '070', '072'),
  ('17', 'Calidad de la educación', 'OPA 17: Calidad de la educación', '070', '072'),
  ('18', 'Estrategias de acceso y permanencia', 'OPA 18: Estrategias de acceso y permanencia', '070', '072'),
  ('19', 'Creditos educación superior (FOES)', 'OPA 19: Creditos educación superior (FOES)', '070', '072'),
  ('20', 'Plan educativo institucional', 'OPA 20: Plan educativo institucional', '070', '072'),
  ('21', 'Programa de alimentación escolar  (PAE)', 'OPA 21: Programa de alimentación escolar  (PAE)', '070', '072'),
  ('22', 'Mantenimiento instituciones educativas oficiales', 'OPA 22: Mantenimiento instituciones educativas oficiales', '070', '072'),
  ('23', 'Trasporte escolar', 'OPA 23: Trasporte escolar', '070', '072'),
  ('24', 'Dotación escolar', 'OPA 24: Dotación escolar', '070', '072'),
  ('25', 'El sistema integrado de matrícula SIMAT', 'OPA 25: El sistema integrado de matrícula SIMAT', '070', '072'),
  ('1', 'Estados financieros instituciones educativas oficiales', 'OPA 1: Estados financieros instituciones educativas oficiales', '070', '073'),
  ('2', 'Atención al ciudadano de la secretaría de educación', 'OPA 2: Atención al ciudadano de la secretaría de educación', '070', '073'),
  ('3', 'Archivo de la secretaría de educación', 'OPA 3: Archivo de la secretaría de educación', '070', '073'),
  ('4', 'Reclutamiento docentes', 'OPA 4: Reclutamiento docentes', '070', '073'),
  ('5', 'Concursos docentes', 'OPA 5: Concursos docentes', '070', '073'),
  ('6', 'Selección docentes', 'OPA 6: Selección docentes', '070', '073'),
  ('7', 'Inducción docentes', 'OPA 7: Inducción docentes', '070', '073'),
  ('8', 'Formación docentes', 'OPA 8: Formación docentes', '070', '073'),
  ('9', 'Evaluación de docentes', 'OPA 9: Evaluación de docentes', '070', '073'),
  ('10', 'Salud ocupacional docentes', 'OPA 10: Salud ocupacional docentes', '070', '073'),
  ('11', 'Bienestar docentes', 'OPA 11: Bienestar docentes', '070', '073'),
  ('12', 'Capacitación docentes', 'OPA 12: Capacitación docentes', '070', '073'),
  ('13', 'Reinducción docentes', 'OPA 13: Reinducción docentes', '070', '073'),
  ('14', 'Escalafon docentes', 'OPA 14: Escalafon docentes', '070', '073'),
  ('15', 'Carrera administrativa docentes', 'OPA 15: Carrera administrativa docentes', '070', '073'),
  ('16', 'Información de personal docente', 'OPA 16: Información de personal docente', '070', '073'),
  ('17', 'Historias laborales docentes', 'OPA 17: Historias laborales docentes', '070', '073'),
  ('18', 'Certificación de personal docentes', 'OPA 18: Certificación de personal docentes', '070', '073'),
  ('19', 'Planta docente', 'OPA 19: Planta docente', '070', '073'),
  ('20', 'Novedades nómina', 'OPA 20: Novedades nómina', '070', '073'),
  ('21', 'Cesantias docentes', 'OPA 21: Cesantias docentes', '070', '073'),
  ('22', 'Embargos docentes', 'OPA 22: Embargos docentes', '070', '073'),
  ('23', 'Libranzas docentes', 'OPA 23: Libranzas docentes', '070', '073'),
  ('24', 'Liquidación nómina docente y administrativa de las instituciones educativas oficiales', 'OPA 24: Liquidación nómina docente y administrativa de las instituciones educativas oficiales', '070', '073'),
  ('25', 'Prestaciones sociales docentes', 'OPA 25: Prestaciones sociales docentes', '070', '073'),
  ('26', 'Sindicato docentes', 'OPA 26: Sindicato docentes', '070', '073')
) AS v(code, name, description, dep_code, subdep_code)
JOIN dependencies d ON d.code = v.dep_code
LEFT JOIN subdependencies s ON s.dependency_id = d.id AND s.code = v.subdep_code
WHERE NOT EXISTS (
    SELECT 1 FROM opas o 
    WHERE o.code = v.code 
    AND o.dependency_id = d.id 
    AND (o.subdependency_id = s.id OR (o.subdependency_id IS NULL AND s.id IS NULL))
);

-- Verificar carga
SELECT 
    'Secretaria de Educacion' as dependencia,
    COUNT(*) as opas_cargados
FROM opas o
JOIN dependencies d ON o.dependency_id = d.id
WHERE d.code = '070';

COMMIT;
