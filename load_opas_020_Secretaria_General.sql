-- CARGA OPAs PARA Secretaria General (81 OPAs)
BEGIN;

INSERT INTO opas (code, name, description, dependency_id, subdependency_id, is_active)
SELECT v.code, v.name, v.description, d.id, s.id, true
FROM (VALUES
  ('1', 'Comunicaciones de los sindicatos', 'OPA 1: Comunicaciones de los sindicatos', '020', '020'),
  ('1', 'Empleo publico', 'OPA 1: Empleo publico', '020', '021'),
  ('2', 'Cargas laborales', 'OPA 2: Cargas laborales', '020', '021'),
  ('3', 'Empleos provisionales', 'OPA 3: Empleos provisionales', '020', '021'),
  ('4', 'Listas de elegibles, concursos de meritos', 'OPA 4: Listas de elegibles, concursos de meritos', '020', '021'),
  ('5', 'Pre pensionados', 'OPA 5: Pre pensionados', '020', '021'),
  ('6', 'Calamidad de funcionario', 'OPA 6: Calamidad de funcionario', '020', '021'),
  ('7', 'Renuncia de funcionarios', 'OPA 7: Renuncia de funcionarios', '020', '021'),
  ('8', 'Horario flexible, teletrabajo, trabajo en casa,', 'OPA 8: Horario flexible, teletrabajo, trabajo en casa,', '020', '021'),
  ('9', 'Manual de funciones', 'OPA 9: Manual de funciones', '020', '021'),
  ('10', 'Escalas salariales - empleados de la alcaldía, Planta de personal de la alcaldía', 'OPA 10: Escalas salariales - empleados de la alcaldía, Planta de personal de la alcaldía', '020', '021'),
  ('11', 'Competencias de los empleados de la alcaldía', 'OPA 11: Competencias de los empleados de la alcaldía', '020', '021'),
  ('12', 'Inducción y capacitación de los empleados de la alcaldía', 'OPA 12: Inducción y capacitación de los empleados de la alcaldía', '020', '021'),
  ('13', 'Bienestar social de los empleados de la alcaldía', 'OPA 13: Bienestar social de los empleados de la alcaldía', '020', '021'),
  ('14', 'Encargos de funcionarios, traslados de funcionarios', 'OPA 14: Encargos de funcionarios, traslados de funcionarios', '020', '021'),
  ('15', 'Licencias de funcionarios, permisos de funcionarios, formato de ausencias de funcionarios', 'OPA 15: Licencias de funcionarios, permisos de funcionarios, formato de ausencias de funcionarios', '020', '021'),
  ('16', 'Documentos hojas de vida', 'OPA 16: Documentos hojas de vida', '020', '021'),
  ('17', 'Historia laboral', 'OPA 17: Historia laboral', '020', '021'),
  ('18', 'Solicitud de certificación laboral', 'OPA 18: Solicitud de certificación laboral', '020', '021'),
  ('19', 'Apoyos educativos para funcionarios', 'OPA 19: Apoyos educativos para funcionarios', '020', '021'),
  ('20', 'Salud y seguridad en el trabajo para funcionarios,', 'OPA 20: Salud y seguridad en el trabajo para funcionarios,', '020', '021'),
  ('21', 'Brigadistas', 'OPA 21: Brigadistas', '020', '021'),
  ('22', 'Dotación de ley para funcionarios de la alcaldia', 'OPA 22: Dotación de ley para funcionarios de la alcaldia', '020', '021'),
  ('23', 'Exámenes ocupacionales', 'OPA 23: Exámenes ocupacionales', '020', '021'),
  ('24', 'Recomendaciones medico laborales,  perdida de capacidad laboral', 'OPA 24: Recomendaciones medico laborales,  perdida de capacidad laboral', '020', '021'),
  ('25', 'Solicitud de cesantías', 'OPA 25: Solicitud de cesantías', '020', '021'),
  ('26', 'Incapacidades de funcionarios( MAYOR A 3 DIAS ES NOVEDADES DE NOMINA)(INFERIOR A 1 DIA SITUACIONES ADMINISTRATIVAS)', 'OPA 26: Incapacidades de funcionarios( MAYOR A 3 DIAS ES NOVEDADES DE NOMINA)(INFERIOR A 1 DIA SITUACIONES ADMINISTRATIVAS)', '020', '021'),
  ('27', 'Prestaciones sociales de funcionarios públicos', 'OPA 27: Prestaciones sociales de funcionarios públicos', '020', '021'),
  ('28', 'Cuotas partes pensionales', 'OPA 28: Cuotas partes pensionales', '020', '021'),
  ('29', 'Solicitud de vacaciones, indemnización de vacaciones, suspensión de vacaciones', 'OPA 29: Solicitud de vacaciones, indemnización de vacaciones, suspensión de vacaciones', '020', '021'),
  ('30', 'Entrega de citación jurados de votación', 'OPA 30: Entrega de citación jurados de votación', '020', '021'),
  ('31', 'Solictud cambio de extinguidores', 'OPA 31: Solictud cambio de extinguidores', '020', '021'),
  ('32', 'Novedades de ingreso o retiro de sindicatos', 'OPA 32: Novedades de ingreso o retiro de sindicatos', '020', '021'),
  ('33', 'Certificados CETIL', 'OPA 33: Certificados CETIL', '020', '021'),
  ('34', 'Comité de convivencia laboral', 'OPA 34: Comité de convivencia laboral', '020', '021'),
  ('35', 'Compensatorio de un día', 'OPA 35: Compensatorio de un día', '020', '021'),
  ('36', 'Evaluación de desempeño empleados de la alcaldía', 'OPA 36: Evaluación de desempeño empleados de la alcaldía', '020', '021'),
  ('37', 'Solicitud de pasantia (estudiantes) independientemente de la dependencia, practicas universitarias, tecnicas o tecnologicas', 'OPA 37: Solicitud de pasantia (estudiantes) independientemente de la dependencia, practicas universitarias, tecnicas o tecnologicas', '020', '021'),
  ('38', 'Notificación accidente de trabajo, accidente de trabajo', 'OPA 38: Notificación accidente de trabajo, accidente de trabajo', '020', '021'),
  ('39', 'Novedades de nómina, libranzas, descuentos de nomina, aclaraciones', 'OPA 39: Novedades de nómina, libranzas, descuentos de nomina, aclaraciones', '020', '021'),
  ('40', 'Certificado de existencia y dependencia economica para el beneficio tributario', 'OPA 40: Certificado de existencia y dependencia economica para el beneficio tributario', '020', '021'),
  ('41', 'Solictud de información para prácticas - pasantias', 'OPA 41: Solictud de información para prácticas - pasantias', '020', '021'),
  ('1', 'Plan anual de adquisiciones', 'OPA 1: Plan anual de adquisiciones', '020', '022'),
  ('2', 'Manejo de inventarios de la alcaldia', 'OPA 2: Manejo de inventarios de la alcaldia', '020', '022'),
  ('3', 'Almacén', 'OPA 3: Almacén', '020', '022'),
  ('4', 'Bienes de consumo', 'OPA 4: Bienes de consumo', '020', '022'),
  ('5', 'Papelería', 'OPA 5: Papelería', '020', '022'),
  ('6', 'Aseguramiento de bienes - pólizas', 'OPA 6: Aseguramiento de bienes - pólizas', '020', '022'),
  ('7', 'Comité de bajas', 'OPA 7: Comité de bajas', '020', '022'),
  ('8', 'Parque automotor', 'OPA 8: Parque automotor', '020', '022'),
  ('9', 'Servicios Públicos', 'OPA 9: Servicios Públicos', '020', '022'),
  ('10', 'Aseo', 'OPA 10: Aseo', '020', '022'),
  ('11', 'Vigilancia', 'OPA 11: Vigilancia', '020', '022'),
  ('12', 'Guardas de seguridad', 'OPA 12: Guardas de seguridad', '020', '022'),
  ('13', 'Producción documental, gestión documental', 'OPA 13: Producción documental, gestión documental', '020', '022'),
  ('14', 'Conservación y administración de archivos', 'OPA 14: Conservación y administración de archivos', '020', '022'),
  ('15', 'Solicitud de copias de archivos (contratos', 'OPA 15: Solicitud de copias de archivos (contratos', '020', '022'),
  ('16', 'Conductores', 'OPA 16: Conductores', '020', '022'),
  ('17', 'Copia de planos (anterior a 2015)', 'OPA 17: Copia de planos (anterior a 2015)', '020', '022'),
  ('18', 'Solicitud de vehículos oficiales (buses, camionetas, camiones, busetas, minivan), servicios de transporte', 'OPA 18: Solicitud de vehículos oficiales (buses, camionetas, camiones, busetas, minivan), servicios de transporte', '020', '022'),
  ('19', 'Perifoneo', 'OPA 19: Perifoneo', '020', '022'),
  ('20', 'Centro administrativo municipal - CAM', 'OPA 20: Centro administrativo municipal - CAM', '020', '022'),
  ('21', 'Constancia ejecutoria (214 hacias atras)', 'OPA 21: Constancia ejecutoria (214 hacias atras)', '020', '022'),
  ('1', 'Canales de atención', 'OPA 1: Canales de atención', '020', '023'),
  ('2', 'Atención telefónica', 'OPA 2: Atención telefónica', '020', '023'),
  ('3', 'Atención virtual', 'OPA 3: Atención virtual', '020', '023'),
  ('4', 'Atención presencial', 'OPA 4: Atención presencial', '020', '023'),
  ('5', 'Punto de atención y orientación PACO', 'OPA 5: Punto de atención y orientación PACO', '020', '023'),
  ('6', 'PQRSDF', 'OPA 6: PQRSDF', '020', '023'),
  ('7', 'Trazabilidad de correspondencia', 'OPA 7: Trazabilidad de correspondencia', '020', '023'),
  ('8', 'Racionalización de trámites', 'OPA 8: Racionalización de trámites', '020', '023'),
  ('9', 'Protocolo de atención al ciudadano', 'OPA 9: Protocolo de atención al ciudadano', '020', '023'),
  ('10', 'boletas', 'OPA 10: boletas', '020', '023'),
  ('11', 'Citaciones', 'OPA 11: Citaciones', '020', '023'),
  ('12', 'Servicios de mensajería, correo certificado', 'OPA 12: Servicios de mensajería, correo certificado', '020', '023'),
  ('13', 'sistema de radicación', 'OPA 13: sistema de radicación', '020', '023'),
  ('1', 'Investigaciones procesos disciplinarios', 'OPA 1: Investigaciones procesos disciplinarios', '020', '024'),
  ('2', 'Quejas de funcionarios o ex funcionarios', 'OPA 2: Quejas de funcionarios o ex funcionarios', '020', '024'),
  ('3', 'Remisión de expedientes', 'OPA 3: Remisión de expedientes', '020', '024'),
  ('4', 'Solicitud de expediente', 'OPA 4: Solicitud de expediente', '020', '024'),
  ('5', 'Solicitud de pruebas', 'OPA 5: Solicitud de pruebas', '020', '024')
) AS v(code, name, description, dep_code, subdep_code)
JOIN dependencies d ON d.code = v.dep_code
LEFT JOIN subdependencies s ON s.dependency_id = d.id AND s.code = v.subdep_code
WHERE NOT EXISTS (
    SELECT 1 FROM opas o 
    WHERE o.code = v.code 
    AND o.dependency_id = d.id 
    AND (o.subdependency_id = s.id OR (o.subdependency_id IS NULL AND s.id IS NULL))
);

-- Verificar carga
SELECT 
    'Secretaria General' as dependencia,
    COUNT(*) as opas_cargados
FROM opas o
JOIN dependencies d ON o.dependency_id = d.id
WHERE d.code = '020';

COMMIT;
