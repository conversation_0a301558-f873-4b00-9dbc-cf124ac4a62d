-- CARGA OPAs PARA Secretaria de Gobierno (93 OPAs)
BEGIN;

INSERT INTO opas (code, name, description, dependency_id, subdependency_id, is_active)
SELECT v.code, v.name, v.description, d.id, s.id, true
FROM (VALUES
  ('1', 'Recuperación y defensa del espacio publico', 'OPA 1: Recuperación y defensa del espacio publico', '030', '030'),
  ('2', 'Ventas ambulantes', 'OPA 2: Ventas ambulantes', '030', '030'),
  ('3', 'Saneamiento de bienes constitutivos como patrimonio inmobiliario', 'OPA 3: Saneamiento de bienes constitutivos como patrimonio inmobiliario', '030', '030'),
  ('4', 'Programación de gestión de riesgos', 'OPA 4: Programación de gestión de riesgos', '030', '030'),
  ('5', 'Cultura de prevención de emergencias y desastres', 'OPA 5: Cultura de prevención de emergencias y desastres', '030', '030'),
  ('6', 'Sistemas de alerta y monitoreo de fenomenos naturales', 'OPA 6: Sistemas de alerta y monitoreo de fenomenos naturales', '030', '030'),
  ('7', 'Mitigación de riesgos', 'OPA 7: Mitigación de riesgos', '030', '030'),
  ('8', 'Red de comunicaciones de emergencias', 'OPA 8: Red de comunicaciones de emergencias', '030', '030'),
  ('9', 'Protección al consumidor', 'OPA 9: Protección al consumidor', '030', '030'),
  ('10', 'Control de precios, pesas y medidas', 'OPA 10: Control de precios, pesas y medidas', '030', '030'),
  ('11', 'Rifas juegos y espectaculos', 'OPA 11: Rifas juegos y espectaculos', '030', '030'),
  ('12', 'Supervisión delegado sorteos y concursos', 'OPA 12: Supervisión delegado sorteos y concursos', '030', '030'),
  ('13', 'Coordinación con el concejo municipal', 'OPA 13: Coordinación con el concejo municipal', '030', '030'),
  ('14', 'Proyectos de integración regional', 'OPA 14: Proyectos de integración regional', '030', '030'),
  ('15', 'Comité de conocimientos y protección de riesgos para eventos masivos y no masivos', 'OPA 15: Comité de conocimientos y protección de riesgos para eventos masivos y no masivos', '030', '030'),
  ('16', 'Restitución de inmueble', 'OPA 16: Restitución de inmueble', '030', '030'),
  ('17', 'Permiso para la realización de espectáculos públicos', 'OPA 17: Permiso para la realización de espectáculos públicos', '030', '030'),
  ('18', 'Todas las peticiones enviadas por la policia nacional se remiten directamente a gobierno independientemente del tema', 'OPA 18: Todas las peticiones enviadas por la policia nacional se remiten directamente a gobierno independientemente del tema', '030', '030'),
  ('19', 'Temas realcionados con el INPEC o carceles del municipio (PPL)', 'OPA 19: Temas realcionados con el INPEC o carceles del municipio (PPL)', '030', '030'),
  ('20', 'Estacion de policia de Chia', 'OPA 20: Estacion de policia de Chia', '030', '030'),
  ('1', 'Prevención de delitos, contravenciones, problemas de convivencia y seguridad ciudadana', 'OPA 1: Prevención de delitos, contravenciones, problemas de convivencia y seguridad ciudadana', '030', '031'),
  ('2', 'Estudios sobre desplazamiento por violencia en el municipio', 'OPA 2: Estudios sobre desplazamiento por violencia en el municipio', '030', '031'),
  ('3', 'Observatorio de convivencia y seguridad', 'OPA 3: Observatorio de convivencia y seguridad', '030', '031'),
  ('4', 'Programas para la reducción de la oferta de sustancias psicoactivas', 'OPA 4: Programas para la reducción de la oferta de sustancias psicoactivas', '030', '031'),
  ('5', 'Diseño de planes de seguridad con Policía', 'OPA 5: Diseño de planes de seguridad con Policía', '030', '031'),
  ('6', 'Estrategias para el mantenimiento y restablecimiento del  orden publico', 'OPA 6: Estrategias para el mantenimiento y restablecimiento del  orden publico', '030', '031'),
  ('7', 'Coordinación procesos electorales con la Registraduría', 'OPA 7: Coordinación procesos electorales con la Registraduría', '030', '031'),
  ('8', 'Información de seguridad ciudadana', 'OPA 8: Información de seguridad ciudadana', '030', '031'),
  ('9', 'Solicitud revisión de cámaras de seguridad, copia de videos de seguridad de las camaras publicas', 'OPA 9: Solicitud revisión de cámaras de seguridad, copia de videos de seguridad de las camaras publicas', '030', '031'),
  ('10', 'Paneles de control - alarmas de seguridad - botones de pánico', 'OPA 10: Paneles de control - alarmas de seguridad - botones de pánico', '030', '031'),
  ('11', 'Línea de emergencias 123', 'OPA 11: Línea de emergencias 123', '030', '031'),
  ('12', 'Temas relacionados con informes y plataforma SYGOB - CIPRET,  Funcionaria Claudia Córdoba', 'OPA 12: Temas relacionados con informes y plataforma SYGOB - CIPRET,  Funcionaria Claudia Córdoba', '030', '031'),
  ('13', 'Migrantes, enlace Migracion Colombia', 'OPA 13: Migrantes, enlace Migracion Colombia', '030', '031'),
  ('14', 'Solicitud de Evaluación del Estudio de Nivel de Riesgo de la persona en relación de hechos que sean de conocimiento en lo
que concierne a amenazas o situaciones de riesgo', 'OPA 14: Solicitud de Evaluación del Estudio de Nivel de Riesgo de la persona en relación de hechos que sean de conocimiento en lo
que concierne a amenazas o situaciones de riesgo', '030', '031'),
  ('1', 'Validada con la dependencia 28/10/2023', 'OPA 1: Validada con la dependencia 28/10/2023', '030', '032'),
  ('2', 'Protección conocimientos tradicionales', 'OPA 2: Protección conocimientos tradicionales', '030', '032'),
  ('3', 'Comunidades étnicas, comunidades indígenas, minorías étnicas', 'OPA 3: Comunidades étnicas, comunidades indígenas, minorías étnicas', '030', '032'),
  ('4', 'Censo de población comunidades', 'OPA 4: Censo de población comunidades', '030', '032'),
  ('5', 'Resguardos indígenas', 'OPA 5: Resguardos indígenas', '030', '032'),
  ('6', 'Autoridades tradicionales', 'OPA 6: Autoridades tradicionales', '030', '032'),
  ('7', 'Resolución de conflictos (comunidades indígenas y minorías étnicas)  las minorías étnicas puden también asignarse a la DDRC', 'OPA 7: Resolución de conflictos (comunidades indígenas y minorías étnicas)  las minorías étnicas puden también asignarse a la DDRC', '030', '032'),
  ('8', 'Libertad de culto, libertad religiosa, libertad de conciencia', 'OPA 8: Libertad de culto, libertad religiosa, libertad de conciencia', '030', '032'),
  ('9', 'Protección población LGTBI (Lesbiana, Gay, Transexual, Bisexual e Intersexual)', 'OPA 9: Protección población LGTBI (Lesbiana, Gay, Transexual, Bisexual e Intersexual)', '030', '032'),
  ('10', 'Censo de establecimiento de culto religioso', 'OPA 10: Censo de establecimiento de culto religioso', '030', '032'),
  ('11', 'Enlace victimas de conflicto armado', 'OPA 11: Enlace victimas de conflicto armado', '030', '032'),
  ('12', 'Población desplazada por la violencia', 'OPA 12: Población desplazada por la violencia', '030', '032'),
  ('1', 'Validada con la dependencia 28/10/2023', 'OPA 1: Validada con la dependencia 28/10/2023', '030', '033'),
  ('2', 'Políticas para promocionar los derechos humanos', 'OPA 2: Políticas para promocionar los derechos humanos', '030', '033'),
  ('3', 'Acceso al ciudadano a la justicia', 'OPA 3: Acceso al ciudadano a la justicia', '030', '033'),
  ('4', 'Garantía de derechos y libertades individuales', 'OPA 4: Garantía de derechos y libertades individuales', '030', '033'),
  ('5', 'Casa de justicia', 'OPA 5: Casa de justicia', '030', '033'),
  ('6', 'Resolución de conflictos en ámbitos escolares y comunitarios', 'OPA 6: Resolución de conflictos en ámbitos escolares y comunitarios', '030', '033'),
  ('7', 'Sistema de información gestión de la justicia', 'OPA 7: Sistema de información gestión de la justicia', '030', '033'),
  ('8', 'Inspecciones de policía', 'OPA 8: Inspecciones de policía', '030', '033'),
  ('9', 'Asuntos policivos', 'OPA 9: Asuntos policivos', '030', '033'),
  ('10', 'Rechazo a comparendos policivos', 'OPA 10: Rechazo a comparendos policivos', '030', '033'),
  ('11', 'Contravenciones', 'OPA 11: Contravenciones', '030', '033'),
  ('12', 'Comisarias de familia', 'OPA 12: Comisarias de familia', '030', '033'),
  ('13', 'Violencia intrafamiliar', 'OPA 13: Violencia intrafamiliar', '030', '033'),
  ('14', 'Protección de niños, niñas y adolescentes', 'OPA 14: Protección de niños, niñas y adolescentes', '030', '033'),
  ('15', 'Medidas de protección', 'OPA 15: Medidas de protección', '030', '033'),
  ('16', 'Orientación jurídica', 'OPA 16: Orientación jurídica', '030', '033'),
  ('17', 'Asistencia psicológica y social para la familia y menores a través de comisarias de familia', 'OPA 17: Asistencia psicológica y social para la familia y menores a través de comisarias de familia', '030', '033'),
  ('18', 'Código de infancia y adolescencia', 'OPA 18: Código de infancia y adolescencia', '030', '033'),
  ('19', 'Comportamientos contrarios a la convivencia: de seguridad, tranquilidad, ambiente y recursos naturales, derecho de reunión, protección a los bienes y privacidad, actividad económica, urbanismo, espacio público y libertad de circulación.', 'OPA 19: Comportamientos contrarios a la convivencia: de seguridad, tranquilidad, ambiente y recursos naturales, derecho de reunión, protección a los bienes y privacidad, actividad económica, urbanismo, espacio público y libertad de circulación.', '030', '033'),
  ('20', 'Autorización de ocupación de inmuebles', 'OPA 20: Autorización de ocupación de inmuebles', '030', '033'),
  ('21', 'Reporte de registro de defunción pasadas 48 horas', 'OPA 21: Reporte de registro de defunción pasadas 48 horas', '030', '033'),
  ('22', 'Entrega de inmuebles (a través de despachos comisorios)', 'OPA 22: Entrega de inmuebles (a través de despachos comisorios)', '030', '033'),
  ('23', 'SIM Sistema de Información Misional - ICBF', 'OPA 23: SIM Sistema de Información Misional - ICBF', '030', '033'),
  ('24', 'SRD Solicitud de restitución de derechos', 'OPA 24: SRD Solicitud de restitución de derechos', '030', '033'),
  ('25', 'PARD Proceso administrativo de restablecimiento de derechos', 'OPA 25: PARD Proceso administrativo de restablecimiento de derechos', '030', '033'),
  ('26', 'Ejecutar la orden de restitución, en casos de tierras comunales (A TRAVES DE DESPACHOS COMISORIOS)', 'OPA 26: Ejecutar la orden de restitución, en casos de tierras comunales (A TRAVES DE DESPACHOS COMISORIOS)', '030', '033'),
  ('27', 'Maltrato animal', 'OPA 27: Maltrato animal', '030', '033'),
  ('28', 'Los propietarios de los predios sean requeridos persuasivamente para que de manera voluntaria , realicen la poda de los árboles y en caso de renuencia a dicha recomendación, estos asuntos serán remitidos a esta Dirección ,para que sean asignados a la Inspección de Policía', 'OPA 28: Los propietarios de los predios sean requeridos persuasivamente para que de manera voluntaria , realicen la poda de los árboles y en caso de renuencia a dicha recomendación, estos asuntos serán remitidos a esta Dirección ,para que sean asignados a la Inspección de Policía', '030', '033'),
  ('29', 'Programa comunitario de actividad pedagogica - para reducir costo de multas', 'OPA 29: Programa comunitario de actividad pedagogica - para reducir costo de multas', '030', '033'),
  ('30', 'Secuestro de bien inmuebles (A TRAVES DE DESPACHOS COMISORIOS)', 'OPA 30: Secuestro de bien inmuebles (A TRAVES DE DESPACHOS COMISORIOS)', '030', '033'),
  ('31', 'Secuestro de la cuota parte del inmueble  (A TRAVES DE DESPACHOS COMISORIOS)', 'OPA 31: Secuestro de la cuota parte del inmueble  (A TRAVES DE DESPACHOS COMISORIOS)', '030', '033'),
  ('32', 'Daños al espacio publico (asfalto por obras)', 'OPA 32: Daños al espacio publico (asfalto por obras)', '030', '033'),
  ('33', 'Situaciones presentadas en cuanto a compra de proyecto de vivienda por incumplimientos siempre y cuando especifique que va con copia para la admnistracion municipal', 'OPA 33: Situaciones presentadas en cuanto a compra de proyecto de vivienda por incumplimientos siempre y cuando especifique que va con copia para la admnistracion municipal', '030', '033'),
  ('34', 'Abandono adulto mayor y vulnerabilidad(Derecho de petición)', 'OPA 34: Abandono adulto mayor y vulnerabilidad(Derecho de petición)', '030', '033'),
  ('35', 'Amparo de pobreza', 'OPA 35: Amparo de pobreza', '030', '033'),
  ('36', 'Actualización de claves del Sistema de Registro Nacional de Medidas Correctivas - RNMC', 'OPA 36: Actualización de claves del Sistema de Registro Nacional de Medidas Correctivas - RNMC', '030', '033'),
  ('37', 'Temas relacionados con informes y plataforma SYGOB - CIPRAT, Funcionaria Claudia Córdoba', 'OPA 37: Temas relacionados con informes y plataforma SYGOB - CIPRAT, Funcionaria Claudia Córdoba', '030', '033'),
  ('1', 'Temas relacionados con informes y plataforma SYGOB - CIPRAT, Funcionaria Claudia Córdoba', 'OPA 1: Temas relacionados con informes y plataforma SYGOB - CIPRAT, Funcionaria Claudia Córdoba', '030', '330'),
  ('1', 'Temas relacionados con informes y plataforma SYGOB - CIPRAT, Funcionaria Claudia Córdoba', 'OPA 1: Temas relacionados con informes y plataforma SYGOB - CIPRAT, Funcionaria Claudia Córdoba', '030', '331'),
  ('1', 'Temas relacionados con informes y plataforma SYGOB - CIPRAT, Funcionaria Claudia Córdoba', 'OPA 1: Temas relacionados con informes y plataforma SYGOB - CIPRAT, Funcionaria Claudia Córdoba', '030', '332'),
  ('1', 'Temas relacionados con informes y plataforma SYGOB - CIPRAT, Funcionaria Claudia Córdoba', 'OPA 1: Temas relacionados con informes y plataforma SYGOB - CIPRAT, Funcionaria Claudia Córdoba', '030', '333'),
  ('1', 'Temas relacionados con informes y plataforma SYGOB - CIPRAT, Funcionaria Claudia Córdoba', 'OPA 1: Temas relacionados con informes y plataforma SYGOB - CIPRAT, Funcionaria Claudia Córdoba', '030', '334'),
  ('1', 'Temas relacionados con informes y plataforma SYGOB - CIPRAT, Funcionaria Claudia Córdoba', 'OPA 1: Temas relacionados con informes y plataforma SYGOB - CIPRAT, Funcionaria Claudia Córdoba', '030', '335'),
  ('1', 'Temas relacionados con informes y plataforma SYGOB - CIPRAT, Funcionaria Claudia Córdoba', 'OPA 1: Temas relacionados con informes y plataforma SYGOB - CIPRAT, Funcionaria Claudia Córdoba', '030', '336'),
  ('1', 'Temas relacionados con informes y plataforma SYGOB - CIPRAT, Funcionaria Claudia Córdoba', 'OPA 1: Temas relacionados con informes y plataforma SYGOB - CIPRAT, Funcionaria Claudia Córdoba', '030', '337'),
  ('1', 'Temas relacionados con informes y plataforma SYGOB - CIPRAT, Funcionaria Claudia Córdoba', 'OPA 1: Temas relacionados con informes y plataforma SYGOB - CIPRAT, Funcionaria Claudia Córdoba', '030', '338'),
  ('1', 'Temas relacionados con informes y plataforma SYGOB - CIPRAT, Funcionaria Claudia Córdoba', 'OPA 1: Temas relacionados con informes y plataforma SYGOB - CIPRAT, Funcionaria Claudia Córdoba', '030', '339')
) AS v(code, name, description, dep_code, subdep_code)
JOIN dependencies d ON d.code = v.dep_code
LEFT JOIN subdependencies s ON s.dependency_id = d.id AND s.code = v.subdep_code
WHERE NOT EXISTS (
    SELECT 1 FROM opas o 
    WHERE o.code = v.code 
    AND o.dependency_id = d.id 
    AND (o.subdependency_id = s.id OR (o.subdependency_id IS NULL AND s.id IS NULL))
);

-- Verificar carga
SELECT 
    'Secretaria de Gobierno' as dependencia,
    COUNT(*) as opas_cargados
FROM opas o
JOIN dependencies d ON o.dependency_id = d.id
WHERE d.code = '030';

COMMIT;
