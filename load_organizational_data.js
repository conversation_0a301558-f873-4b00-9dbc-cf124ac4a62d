const fs = require('fs');

// Leer archivo JSON
const data = JSON.parse(fs.readFileSync('OPA-chia-optimo.json', 'utf8'));

// Función para escapar comillas simples en SQL
function escapeSql(str) {
  return str.replace(/'/g, "''");
}

// Extraer dependencias
const dependencies = [];
Object.entries(data.dependencias).forEach(([codigo, dep]) => {
  dependencies.push({
    code: codigo,
    name: dep.nombre,
    acronym: dep.sigla || null
  });
});

console.log('-- DEPENDENCIAS A INSERTAR:');
dependencies.forEach(dep => {
  const name = escapeSql(dep.name);
  const acronym = dep.acronym ? `'${escapeSql(dep.acronym)}'` : 'NULL';
  console.log(`INSERT INTO dependencies (code, name, acronym) VALUES ('${dep.code}', '${name}', ${acronym}) ON CONFLICT (code) DO NOTHING;`);
});

console.log('\n-- SUBDEPENDENCIAS A INSERTAR:');
Object.entries(data.dependencias).forEach(([codigo_dep, dep]) => {
  if (dep.subdependencias) {
    Object.entries(dep.subdependencias).forEach(([codigo_sub, sub]) => {
      const name = escapeSql(sub.nombre);
      const acronym = sub.sigla ? `'${escapeSql(sub.sigla)}'` : 'NULL';
      console.log(`INSERT INTO subdependencies (dependency_id, code, name, acronym) 
        SELECT d.id, '${codigo_sub}', '${name}', ${acronym}
        FROM dependencies d WHERE d.code = '${codigo_dep}';`);
    });
  }
});

console.log('\n-- ESTADÍSTICAS:');
console.log(`Total dependencias: ${dependencies.length}`);
let totalSubdependencias = 0;
Object.values(data.dependencias).forEach(dep => {
  if (dep.subdependencias) {
    totalSubdependencias += Object.keys(dep.subdependencias).length;
  }
});
console.log(`Total subdependencias: ${totalSubdependencias}`);
