#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script eficiente para cargar OPAs usando inserción directa por dependencia
Carga todos los OPAs de una dependencia a la vez
"""

import json
import sys
import re

def clean_text(text):
    """Limpia y escapa texto para SQL"""
    if not text:
        return ""
    
    # Reemplazar comillas simples para evitar errores SQL
    text = text.replace("'", "''")
    
    # Limpiar caracteres especiales problemáticos
    text = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', text)
    
    return text.strip()

def generate_dependency_sql(dep_code, dep_data):
    """Genera SQL para una dependencia completa"""
    
    dep_name = dep_data['nombre']
    all_opas = []
    
    # Recopilar todos los OPAs de la dependencia
    if 'subdependencias' in dep_data:
        for subdep_code, subdep_data in dep_data['subdependencias'].items():
            if 'OPA' in subdep_data and subdep_data['OPA']:
                for opa in subdep_data['OPA']:
                    opa_code = clean_text(opa['codigo_OPA'])
                    opa_name = clean_text(opa['OPA'])
                    opa_description = f"OPA {opa_code}: {opa_name}"
                    
                    all_opas.append({
                        'code': opa_code,
                        'name': opa_name[:500],
                        'description': opa_description[:1000],
                        'dep_code': dep_code,
                        'subdep_code': subdep_code
                    })
    
    if not all_opas:
        return None
    
    # Generar VALUES para todos los OPAs
    values_list = []
    for opa in all_opas:
        values_list.append(
            f"('{opa['code']}', '{opa['name']}', '{opa['description']}', '{opa['dep_code']}', '{opa['subdep_code']}')"
        )
    
    values_str = ',\n  '.join(values_list)
    
    sql = f"""-- CARGA OPAs PARA {dep_name} ({len(all_opas)} OPAs)
BEGIN;

INSERT INTO opas (code, name, description, dependency_id, subdependency_id, is_active)
SELECT v.code, v.name, v.description, d.id, s.id, true
FROM (VALUES
  {values_str}
) AS v(code, name, description, dep_code, subdep_code)
JOIN dependencies d ON d.code = v.dep_code
LEFT JOIN subdependencies s ON s.dependency_id = d.id AND s.code = v.subdep_code
WHERE NOT EXISTS (
    SELECT 1 FROM opas o 
    WHERE o.code = v.code 
    AND o.dependency_id = d.id 
    AND (o.subdependency_id = s.id OR (o.subdependency_id IS NULL AND s.id IS NULL))
);

-- Verificar carga
SELECT 
    '{dep_name}' as dependencia,
    COUNT(*) as opas_cargados
FROM opas o
JOIN dependencies d ON o.dependency_id = d.id
WHERE d.code = '{dep_code}';

COMMIT;
"""
    
    return sql, len(all_opas)

def main():
    """Función principal"""
    
    try:
        # Leer el archivo JSON
        with open('OPA-chia-optimo.json', 'r', encoding='utf-8') as file:
            data = json.load(file)
        
        print("🚀 Generando archivos SQL por dependencia...")
        
        total_opas = 0
        files_generated = 0
        
        # Procesar cada dependencia
        for dep_code, dep_data in data['dependencias'].items():
            dep_name = dep_data['nombre']
            
            # Generar SQL para la dependencia
            result = generate_dependency_sql(dep_code, dep_data)
            
            if result:
                sql, opa_count = result
                
                # Crear nombre de archivo seguro
                safe_name = re.sub(r'[^\w\s-]', '', dep_name).strip()
                safe_name = re.sub(r'[-\s]+', '_', safe_name)
                filename = f"load_opas_{dep_code}_{safe_name}.sql"
                
                # Guardar archivo SQL
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(sql)
                
                print(f"✅ {filename} - {opa_count} OPAs")
                total_opas += opa_count
                files_generated += 1
            else:
                print(f"⚠️  {dep_name} - Sin OPAs")
        
        # Generar script maestro
        master_script = generate_master_script(data, total_opas)
        with open('execute_all_opas.sql', 'w', encoding='utf-8') as f:
            f.write(master_script)
        
        print(f"\n🎉 Generación completada!")
        print(f"📊 Total OPAs: {total_opas}")
        print(f"📁 Archivos generados: {files_generated}")
        print(f"🚀 Script maestro: execute_all_opas.sql")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def generate_master_script(data, total_opas):
    """Genera script maestro con estadísticas"""
    
    script = f"""-- =====================================================
-- SCRIPT MAESTRO - CARGA COMPLETA DE OPAs
-- Total de OPAs: {total_opas}
-- =====================================================

-- Verificar estado inicial
SELECT 
    'ESTADO INICIAL' as estado,
    COUNT(*) as opas_existentes
FROM opas;

-- Estadísticas por dependencia ANTES de la carga
SELECT 
    d.name as dependencia,
    COUNT(o.id) as opas_actuales
FROM dependencies d
LEFT JOIN opas o ON o.dependency_id = d.id
GROUP BY d.id, d.name
ORDER BY d.name;

"""
    
    # Agregar comandos para ejecutar cada archivo
    for dep_code, dep_data in data['dependencias'].items():
        if 'subdependencias' in dep_data:
            has_opas = any(
                'OPA' in subdep_data and subdep_data['OPA'] 
                for subdep_data in dep_data['subdependencias'].values()
            )
            if has_opas:
                dep_name = dep_data['nombre']
                safe_name = re.sub(r'[^\w\s-]', '', dep_name).strip()
                safe_name = re.sub(r'[-\s]+', '_', safe_name)
                filename = f"load_opas_{dep_code}_{safe_name}.sql"
                
                script += f"""
-- Ejecutar: {filename}
-- \\i {filename}
"""
    
    script += f"""

-- VERIFICACIÓN FINAL
SELECT 
    'ESTADO FINAL' as estado,
    COUNT(*) as total_opas_cargados
FROM opas;

-- Estadísticas finales por dependencia
SELECT 
    d.name as dependencia,
    COUNT(o.id) as opas_cargados,
    ROUND(COUNT(o.id) * 100.0 / {total_opas}, 2) as porcentaje
FROM dependencies d
LEFT JOIN opas o ON o.dependency_id = d.id
WHERE o.id IS NOT NULL
GROUP BY d.id, d.name
ORDER BY opas_cargados DESC;

-- Resumen final
SELECT 
    COUNT(*) as total_opas,
    COUNT(DISTINCT dependency_id) as dependencias_con_opas,
    COUNT(DISTINCT subdependency_id) as subdependencias_con_opas,
    ROUND(AVG(LENGTH(name)), 2) as longitud_promedio_nombre
FROM opas;
"""
    
    return script

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
