-- =====================================================
-- INTERFACES INFORMATIVAS PARA TRÁMITES MUNICIPALES DE CHÍA
-- Consultas SQL para mostrar información completa de trámites y OPAs
-- =====================================================

-- 1. CONSULTA GENERAL - TODOS LOS PROCEDIMIENTOS
-- Muestra información completa de trámites y OPAs con enlaces, tiempos y costos
CREATE OR REPLACE VIEW vista_procedimientos_completa AS
SELECT 
    type as tipo_procedimiento,
    name as nombre,
    description as descripcion,
    dependency_name as dependencia,
    subdependency_name as subdependencia,
    CASE 
        WHEN cost IS NOT NULL AND cost > 0 THEN CONCAT('$', CAST(cost AS TEXT))
        WHEN cost = 0 THEN 'Gratuito'
        ELSE 'Sin costo definido'
    END as costo,
    COALESCE(response_time, 'No especificado') as tiempo_respuesta,
    CASE 
        WHEN online_available = true THEN 'Disponible en línea'
        WHEN online_available = false THEN 'Solo presencial'
        ELSE 'No especificado'
    END as modalidad,
    category as categoria,
    CASE 
        WHEN is_active = true THEN 'Activo'
        ELSE 'Inactivo'
    END as estado,
    created_at as fecha_creacion
FROM all_procedures
ORDER BY dependency_name, type, name;

-- 2. BÚSQUEDA POR DEPENDENCIA ESPECÍFICA
-- Ejemplo: Secretaría de Hacienda
SELECT 
    'SECRETARÍA DE HACIENDA - PROCEDIMIENTOS' as titulo,
    tipo_procedimiento,
    nombre,
    costo,
    tiempo_respuesta,
    modalidad
FROM vista_procedimientos_completa
WHERE dependencia ILIKE '%hacienda%'
ORDER BY tipo_procedimiento, nombre;

-- 3. TRÁMITES CON COSTO - INFORMACIÓN DETALLADA
SELECT 
    'TRÁMITES CON COSTO' as categoria,
    nombre,
    dependencia,
    costo,
    tiempo_respuesta,
    descripcion
FROM vista_procedimientos_completa
WHERE tipo_procedimiento = 'TRAMITE' 
    AND costo NOT IN ('Gratuito', 'Sin costo definido')
ORDER BY CAST(REPLACE(REPLACE(costo, '$', ''), ',', '') AS NUMERIC) DESC;

-- 4. TRÁMITES GRATUITOS MÁS SOLICITADOS
SELECT 
    'TRÁMITES GRATUITOS' as categoria,
    nombre,
    dependencia,
    tiempo_respuesta,
    modalidad
FROM vista_procedimientos_completa
WHERE tipo_procedimiento = 'TRAMITE' 
    AND costo = 'Gratuito'
ORDER BY dependencia, nombre;

-- 5. PROCEDIMIENTOS POR TIEMPO DE RESPUESTA
-- Trámites más rápidos (menos de 5 días)
SELECT 
    'TRÁMITES RÁPIDOS (MENOS DE 5 DÍAS)' as categoria,
    nombre,
    dependencia,
    tiempo_respuesta,
    costo
FROM vista_procedimientos_completa
WHERE tipo_procedimiento = 'TRAMITE'
    AND (tiempo_respuesta ILIKE '%1 día%' 
         OR tiempo_respuesta ILIKE '%2 día%'
         OR tiempo_respuesta ILIKE '%3 día%'
         OR tiempo_respuesta ILIKE '%4 día%'
         OR tiempo_respuesta ILIKE '%1 hora%')
ORDER BY tiempo_respuesta, nombre;

-- 6. ESTADÍSTICAS POR DEPENDENCIA
SELECT 
    dependencia,
    COUNT(*) as total_procedimientos,
    SUM(CASE WHEN tipo_procedimiento = 'TRAMITE' THEN 1 ELSE 0 END) as tramites,
    SUM(CASE WHEN tipo_procedimiento = 'OPA' THEN 1 ELSE 0 END) as opas,
    SUM(CASE WHEN costo = 'Gratuito' THEN 1 ELSE 0 END) as gratuitos,
    SUM(CASE WHEN costo NOT IN ('Gratuito', 'Sin costo definido') THEN 1 ELSE 0 END) as con_costo,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM vista_procedimientos_completa), 2) as porcentaje_total
FROM vista_procedimientos_completa
GROUP BY dependencia
ORDER BY total_procedimientos DESC;

-- 7. BÚSQUEDA POR PALABRA CLAVE
-- Función para buscar procedimientos por palabra clave
CREATE OR REPLACE FUNCTION buscar_procedimientos(palabra_clave TEXT)
RETURNS TABLE (
    tipo_procedimiento TEXT,
    nombre TEXT,
    dependencia TEXT,
    costo TEXT,
    tiempo_respuesta TEXT,
    descripcion TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        vp.tipo_procedimiento,
        vp.nombre,
        vp.dependencia,
        vp.costo,
        vp.tiempo_respuesta,
        vp.descripcion
    FROM vista_procedimientos_completa vp
    WHERE vp.nombre ILIKE '%' || palabra_clave || '%'
        OR vp.descripcion ILIKE '%' || palabra_clave || '%'
        OR vp.dependencia ILIKE '%' || palabra_clave || '%'
    ORDER BY vp.dependencia, vp.tipo_procedimiento, vp.nombre;
END;
$$ LANGUAGE plpgsql;

-- Ejemplo de uso de búsqueda:
-- SELECT * FROM buscar_procedimientos('licencia');
-- SELECT * FROM buscar_procedimientos('impuesto');
-- SELECT * FROM buscar_procedimientos('certificado');

-- 8. PROCEDIMIENTOS DISPONIBLES EN LÍNEA
SELECT 
    'PROCEDIMIENTOS DISPONIBLES EN LÍNEA' as categoria,
    tipo_procedimiento,
    nombre,
    dependencia,
    costo,
    tiempo_respuesta
FROM vista_procedimientos_completa
WHERE modalidad = 'Disponible en línea'
ORDER BY dependencia, tipo_procedimiento, nombre;

-- 9. INFORMACIÓN DE CONTACTO POR DEPENDENCIA
SELECT DISTINCT
    dependencia,
    COUNT(*) as total_procedimientos,
    'Información de contacto disponible en la dependencia correspondiente' as contacto
FROM vista_procedimientos_completa
GROUP BY dependencia
ORDER BY total_procedimientos DESC;

-- 10. RESUMEN EJECUTIVO DEL SISTEMA
SELECT 
    'RESUMEN EJECUTIVO - SISTEMA DE TRÁMITES CHÍA' as titulo,
    COUNT(*) as total_procedimientos,
    SUM(CASE WHEN tipo_procedimiento = 'TRAMITE' THEN 1 ELSE 0 END) as total_tramites,
    SUM(CASE WHEN tipo_procedimiento = 'OPA' THEN 1 ELSE 0 END) as total_opas,
    COUNT(DISTINCT dependencia) as dependencias_activas,
    SUM(CASE WHEN costo = 'Gratuito' THEN 1 ELSE 0 END) as procedimientos_gratuitos,
    SUM(CASE WHEN modalidad = 'Disponible en línea' THEN 1 ELSE 0 END) as disponibles_online,
    ROUND(
        SUM(CASE WHEN costo = 'Gratuito' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2
    ) as porcentaje_gratuitos
FROM vista_procedimientos_completa;

-- =====================================================
-- CONSULTAS ESPECÍFICAS PARA CIUDADANOS
-- =====================================================

-- 11. TRÁMITES MÁS COMUNES PARA CIUDADANOS
SELECT 
    'TRÁMITES MÁS COMUNES PARA CIUDADANOS' as categoria,
    nombre,
    dependencia,
    costo,
    tiempo_respuesta,
    modalidad
FROM vista_procedimientos_completa
WHERE tipo_procedimiento = 'TRAMITE'
    AND (nombre ILIKE '%certificado%'
         OR nombre ILIKE '%licencia%'
         OR nombre ILIKE '%permiso%'
         OR nombre ILIKE '%registro%'
         OR nombre ILIKE '%impuesto%')
ORDER BY dependencia, nombre;

-- 12. PROCEDIMIENTOS POR CATEGORÍA DE CIUDADANO
-- Empresarios y comerciantes
SELECT 
    'PROCEDIMIENTOS PARA EMPRESARIOS' as categoria,
    nombre,
    dependencia,
    costo,
    tiempo_respuesta
FROM vista_procedimientos_completa
WHERE nombre ILIKE '%comercio%'
    OR nombre ILIKE '%empresa%'
    OR nombre ILIKE '%industria%'
    OR nombre ILIKE '%establecimiento%'
    OR nombre ILIKE '%negocio%'
ORDER BY dependencia, nombre;

-- Propietarios de vivienda
SELECT 
    'PROCEDIMIENTOS PARA PROPIETARIOS' as categoria,
    nombre,
    dependencia,
    costo,
    tiempo_respuesta
FROM vista_procedimientos_completa
WHERE nombre ILIKE '%predial%'
    OR nombre ILIKE '%propiedad%'
    OR nombre ILIKE '%vivienda%'
    OR nombre ILIKE '%construcción%'
    OR nombre ILIKE '%urbanística%'
ORDER BY dependencia, nombre;
