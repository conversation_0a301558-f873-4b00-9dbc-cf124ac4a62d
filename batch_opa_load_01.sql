-- LOTE 1 DE CARGA DE OPAs
BEGIN;


-- load_opas_010_Secretaria_de_Planeacion.sql (45 OPAs)
INSERT INTO opas (code, name, description, dependency_id, subdependency_id, is_active)
SELECT v.code, v.name, v.description, d.id, s.id, true
FROM (VALUES
  ('1', 'Plan anticorrupcion', 'OPA 1: Plan anticorrupcion', '010', '010'),
  ('2', 'Información referente a patrimonio de predios en el municipio', 'OPA 2: Información referente a patrimonio de predios en el municipio', '010', '010'),
  ('1', 'Documentación y procedimientos relacionados con el SISBEN (Encuesta, inclusion en la base, retiro)', 'OPA 1: Documentación y procedimientos relacionados con el SISBEN (Encuesta, inclusion en la base, retiro)', '010', '011'),
  ('2', 'Solicitud de cartografias del municipio', 'OPA 2: Solicitud de cartografias del municipio', '010', '011'),
  ('3', 'Certificado de nomenclatura', 'OPA 3: Certificado de nomenclatura', '010', '011'),
  ('4', 'Georreferenciación', 'OPA 4: Georreferenciación', '010', '011'),
  ('5', 'Documentación y procedimientos relacionados con Estratificación', 'OPA 5: Documentación y procedimientos relacionados con Estratificación', '010', '011'),
  ('1', 'Plan operativo anual de inversiones', 'OPA 1: Plan operativo anual de inversiones', '010', '012'),
  ('2', 'Información respecto a las politicas publicas del municipio', 'OPA 2: Información respecto a las politicas publicas del municipio', '010', '012'),
  ('3', 'Regalias', 'OPA 3: Regalias', '010', '012'),
  ('4', 'Informacion del plan de desarrollo general (lo especifico puede dirigirse a la dependencia requerida)', 'OPA 4: Informacion del plan de desarrollo general (lo especifico puede dirigirse a la dependencia requerida)', '010', '012'),
  ('1', 'Concepto de uso de suelos', 'OPA 1: Concepto de uso de suelos', '010', '013'),
  ('2', 'Conceptos de no riesgo', 'OPA 2: Conceptos de no riesgo', '010', '013'),
  ('3', 'Conceptos de reserva vial', 'OPA 3: Conceptos de reserva vial', '010', '013'),
  ('4', 'Certificado de riesgos de predios', 'OPA 4: Certificado de riesgos de predios', '010', '013'),
  ('5', 'Concepto de norma urbanistica', 'OPA 5: Concepto de norma urbanistica', '010', '013'),
  ('6', 'Certificado de desafectación de Plusvalía, Participación en Plusvalia, Cancelación anotación en certificado de tradición', 'OPA 6: Certificado de desafectación de Plusvalía, Participación en Plusvalia, Cancelación anotación en certificado de tradición', '010', '013'),
  ('7', 'Inquietudes respecto al Plan de Ordenamiento Territorial (documentación, cartografía)', 'OPA 7: Inquietudes respecto al Plan de Ordenamiento Territorial (documentación, cartografía)', '010', '013'),
  ('8', 'Formulación, radicación o ajustes a proyectos de plan parcial', 'OPA 8: Formulación, radicación o ajustes a proyectos de plan parcial', '010', '013'),
  ('9', 'Legalización urbanística de asentamientos humanos', 'OPA 9: Legalización urbanística de asentamientos humanos', '010', '013'),
  ('10', 'Concepto- Certificado de paramentacion', 'OPA 10: Concepto- Certificado de paramentacion', '010', '013'),
  ('1', 'Permiso de rompimiento de vías o intervención de espacio publico', 'OPA 1: Permiso de rompimiento de vías o intervención de espacio publico', '010', '014'),
  ('2', 'Prorroga de intervención de espacio público', 'OPA 2: Prorroga de intervención de espacio público', '010', '014'),
  ('3', 'Todo lo relacionado con Licencias Urbanísticas', 'OPA 3: Todo lo relacionado con Licencias Urbanísticas', '010', '014'),
  ('4', 'Correción, revalidación y prorroga de licencia urbanistica', 'OPA 4: Correción, revalidación y prorroga de licencia urbanistica', '010', '014'),
  ('5', 'Radicación Documentos para el inicio de actividades de enajenación de ventas de vivienda', 'OPA 5: Radicación Documentos para el inicio de actividades de enajenación de ventas de vivienda', '010', '014'),
  ('6', 'Registro de enajenación', 'OPA 6: Registro de enajenación', '010', '014'),
  ('7', 'Matricula del arrendador', 'OPA 7: Matricula del arrendador', '010', '014'),
  ('8', 'Ajuste de cotas y areas', 'OPA 8: Ajuste de cotas y areas', '010', '014'),
  ('9', 'Aprobacion de planos de propiedad horizontal', 'OPA 9: Aprobacion de planos de propiedad horizontal', '010', '014'),
  ('10', 'Aprobacion de piscinas', 'OPA 10: Aprobacion de piscinas', '010', '014'),
  ('11', 'Autorizacion para el movimiento de tierras', 'OPA 11: Autorizacion para el movimiento de tierras', '010', '014'),
  ('12', 'Copia certificada de planos (del 2015 en adelante)', 'OPA 12: Copia certificada de planos (del 2015 en adelante)', '010', '014'),
  ('13', 'Licencia de localizacion de equipamiento de espacio publico', 'OPA 13: Licencia de localizacion de equipamiento de espacio publico', '010', '014'),
  ('14', 'Cancelación matricula del arrendador', 'OPA 14: Cancelación matricula del arrendador', '010', '014'),
  ('15', 'Control de constructoras e inmobiliarias', 'OPA 15: Control de constructoras e inmobiliarias', '010', '014'),
  ('16', 'Estados Financieros constructoras', 'OPA 16: Estados Financieros constructoras', '010', '014'),
  ('17', 'Correción, revalidación y prorroga de licencia urbanistica', 'OPA 17: Correción, revalidación y prorroga de licencia urbanistica', '010', '014'),
  ('18', 'Constancia ejecutoria (2024 en adelante)', 'OPA 18: Constancia ejecutoria (2024 en adelante)', '010', '014'),
  ('1', 'Todo lo concerniente con servicios públicos domiciliarios (energía eléctrica, telefonía pública básica conmutada, telefonía móvil rural, y distribución de gas combustible ley 142)', 'OPA 1: Todo lo concerniente con servicios públicos domiciliarios (energía eléctrica, telefonía pública básica conmutada, telefonía móvil rural, y distribución de gas combustible ley 142)', '010', '015'),
  ('2', 'Normas, regulacion  del centro de acopio y residuos aprovechables', 'OPA 2: Normas, regulacion  del centro de acopio y residuos aprovechables', '010', '015'),
  ('3', 'Traslado a empresas de energía de casos de arboles que se cruzan con red electrica y generan riesgo', 'OPA 3: Traslado a empresas de energía de casos de arboles que se cruzan con red electrica y generan riesgo', '010', '015'),
  ('4', 'Alumbrado publico  (Remitir facturas de alumbrado público)', 'OPA 4: Alumbrado publico  (Remitir facturas de alumbrado público)', '010', '015'),
  ('5', 'Solicitudes de paz y salvo de los permisos de intervención. Estos deberán radicarse como derecho de petición.', 'OPA 5: Solicitudes de paz y salvo de los permisos de intervención. Estos deberán radicarse como derecho de petición.', '010', '015'),
  ('6', 'Infraestructura para antenas de telecomunicaciones: Estos deben radicarse como Autorización como infraestructura de Telecomunicaciones.', 'OPA 6: Infraestructura para antenas de telecomunicaciones: Estos deben radicarse como Autorización como infraestructura de Telecomunicaciones.', '010', '015')
) AS v(code, name, description, dep_code, subdep_code)
JOIN dependencies d ON d.code = v.dep_code
LEFT JOIN subdependencies s ON s.dependency_id = d.id AND s.code = v.subdep_code
WHERE NOT EXISTS (
    SELECT 1 FROM opas o 
    WHERE o.code = v.code 
    AND o.dependency_id = d.id 
    AND (o.subdependency_id = s.id OR (o.subdependency_id IS NULL AND s.id IS NULL))
);


-- load_opas_020_Secretaria_General.sql (81 OPAs)
INSERT INTO opas (code, name, description, dependency_id, subdependency_id, is_active)
SELECT v.code, v.name, v.description, d.id, s.id, true
FROM (VALUES
  ('1', 'Comunicaciones de los sindicatos', 'OPA 1: Comunicaciones de los sindicatos', '020', '020'),
  ('1', 'Empleo publico', 'OPA 1: Empleo publico', '020', '021'),
  ('2', 'Cargas laborales', 'OPA 2: Cargas laborales', '020', '021'),
  ('3', 'Empleos provisionales', 'OPA 3: Empleos provisionales', '020', '021'),
  ('4', 'Listas de elegibles, concursos de meritos', 'OPA 4: Listas de elegibles, concursos de meritos', '020', '021'),
  ('5', 'Pre pensionados', 'OPA 5: Pre pensionados', '020', '021'),
  ('6', 'Calamidad de funcionario', 'OPA 6: Calamidad de funcionario', '020', '021'),
  ('7', 'Renuncia de funcionarios', 'OPA 7: Renuncia de funcionarios', '020', '021'),
  ('8', 'Horario flexible, teletrabajo, trabajo en casa,', 'OPA 8: Horario flexible, teletrabajo, trabajo en casa,', '020', '021'),
  ('9', 'Manual de funciones', 'OPA 9: Manual de funciones', '020', '021'),
  ('10', 'Escalas salariales - empleados de la alcaldía, Planta de personal de la alcaldía', 'OPA 10: Escalas salariales - empleados de la alcaldía, Planta de personal de la alcaldía', '020', '021'),
  ('11', 'Competencias de los empleados de la alcaldía', 'OPA 11: Competencias de los empleados de la alcaldía', '020', '021'),
  ('12', 'Inducción y capacitación de los empleados de la alcaldía', 'OPA 12: Inducción y capacitación de los empleados de la alcaldía', '020', '021'),
  ('13', 'Bienestar social de los empleados de la alcaldía', 'OPA 13: Bienestar social de los empleados de la alcaldía', '020', '021'),
  ('14', 'Encargos de funcionarios, traslados de funcionarios', 'OPA 14: Encargos de funcionarios, traslados de funcionarios', '020', '021'),
  ('15', 'Licencias de funcionarios, permisos de funcionarios, formato de ausencias de funcionarios', 'OPA 15: Licencias de funcionarios, permisos de funcionarios, formato de ausencias de funcionarios', '020', '021'),
  ('16', 'Documentos hojas de vida', 'OPA 16: Documentos hojas de vida', '020', '021'),
  ('17', 'Historia laboral', 'OPA 17: Historia laboral', '020', '021'),
  ('18', 'Solicitud de certificación laboral', 'OPA 18: Solicitud de certificación laboral', '020', '021'),
  ('19', 'Apoyos educativos para funcionarios', 'OPA 19: Apoyos educativos para funcionarios', '020', '021'),
  ('20', 'Salud y seguridad en el trabajo para funcionarios,', 'OPA 20: Salud y seguridad en el trabajo para funcionarios,', '020', '021'),
  ('21', 'Brigadistas', 'OPA 21: Brigadistas', '020', '021'),
  ('22', 'Dotación de ley para funcionarios de la alcaldia', 'OPA 22: Dotación de ley para funcionarios de la alcaldia', '020', '021'),
  ('23', 'Exámenes ocupacionales', 'OPA 23: Exámenes ocupacionales', '020', '021'),
  ('24', 'Recomendaciones medico laborales,  perdida de capacidad laboral', 'OPA 24: Recomendaciones medico laborales,  perdida de capacidad laboral', '020', '021'),
  ('25', 'Solicitud de cesantías', 'OPA 25: Solicitud de cesantías', '020', '021'),
  ('26', 'Incapacidades de funcionarios( MAYOR A 3 DIAS ES NOVEDADES DE NOMINA)(INFERIOR A 1 DIA SITUACIONES ADMINISTRATIVAS)', 'OPA 26: Incapacidades de funcionarios( MAYOR A 3 DIAS ES NOVEDADES DE NOMINA)(INFERIOR A 1 DIA SITUACIONES ADMINISTRATIVAS)', '020', '021'),
  ('27', 'Prestaciones sociales de funcionarios públicos', 'OPA 27: Prestaciones sociales de funcionarios públicos', '020', '021'),
  ('28', 'Cuotas partes pensionales', 'OPA 28: Cuotas partes pensionales', '020', '021'),
  ('29', 'Solicitud de vacaciones, indemnización de vacaciones, suspensión de vacaciones', 'OPA 29: Solicitud de vacaciones, indemnización de vacaciones, suspensión de vacaciones', '020', '021'),
  ('30', 'Entrega de citación jurados de votación', 'OPA 30: Entrega de citación jurados de votación', '020', '021'),
  ('31', 'Solictud cambio de extinguidores', 'OPA 31: Solictud cambio de extinguidores', '020', '021'),
  ('32', 'Novedades de ingreso o retiro de sindicatos', 'OPA 32: Novedades de ingreso o retiro de sindicatos', '020', '021'),
  ('33', 'Certificados CETIL', 'OPA 33: Certificados CETIL', '020', '021'),
  ('34', 'Comité de convivencia laboral', 'OPA 34: Comité de convivencia laboral', '020', '021'),
  ('35', 'Compensatorio de un día', 'OPA 35: Compensatorio de un día', '020', '021'),
  ('36', 'Evaluación de desempeño empleados de la alcaldía', 'OPA 36: Evaluación de desempeño empleados de la alcaldía', '020', '021'),
  ('37', 'Solicitud de pasantia (estudiantes) independientemente de la dependencia, practicas universitarias, tecnicas o tecnologicas', 'OPA 37: Solicitud de pasantia (estudiantes) independientemente de la dependencia, practicas universitarias, tecnicas o tecnologicas', '020', '021'),
  ('38', 'Notificación accidente de trabajo, accidente de trabajo', 'OPA 38: Notificación accidente de trabajo, accidente de trabajo', '020', '021'),
  ('39', 'Novedades de nómina, libranzas, descuentos de nomina, aclaraciones', 'OPA 39: Novedades de nómina, libranzas, descuentos de nomina, aclaraciones', '020', '021'),
  ('40', 'Certificado de existencia y dependencia economica para el beneficio tributario', 'OPA 40: Certificado de existencia y dependencia economica para el beneficio tributario', '020', '021'),
  ('41', 'Solictud de información para prácticas - pasantias', 'OPA 41: Solictud de información para prácticas - pasantias', '020', '021'),
  ('1', 'Plan anual de adquisiciones', 'OPA 1: Plan anual de adquisiciones', '020', '022'),
  ('2', 'Manejo de inventarios de la alcaldia', 'OPA 2: Manejo de inventarios de la alcaldia', '020', '022'),
  ('3', 'Almacén', 'OPA 3: Almacén', '020', '022'),
  ('4', 'Bienes de consumo', 'OPA 4: Bienes de consumo', '020', '022'),
  ('5', 'Papelería', 'OPA 5: Papelería', '020', '022'),
  ('6', 'Aseguramiento de bienes - pólizas', 'OPA 6: Aseguramiento de bienes - pólizas', '020', '022'),
  ('7', 'Comité de bajas', 'OPA 7: Comité de bajas', '020', '022'),
  ('8', 'Parque automotor', 'OPA 8: Parque automotor', '020', '022'),
  ('9', 'Servicios Públicos', 'OPA 9: Servicios Públicos', '020', '022'),
  ('10', 'Aseo', 'OPA 10: Aseo', '020', '022'),
  ('11', 'Vigilancia', 'OPA 11: Vigilancia', '020', '022'),
  ('12', 'Guardas de seguridad', 'OPA 12: Guardas de seguridad', '020', '022'),
  ('13', 'Producción documental, gestión documental', 'OPA 13: Producción documental, gestión documental', '020', '022'),
  ('14', 'Conservación y administración de archivos', 'OPA 14: Conservación y administración de archivos', '020', '022'),
  ('15', 'Solicitud de copias de archivos (contratos', 'OPA 15: Solicitud de copias de archivos (contratos', '020', '022'),
  ('16', 'Conductores', 'OPA 16: Conductores', '020', '022'),
  ('17', 'Copia de planos (anterior a 2015)', 'OPA 17: Copia de planos (anterior a 2015)', '020', '022'),
  ('18', 'Solicitud de vehículos oficiales (buses, camionetas, camiones, busetas, minivan), servicios de transporte', 'OPA 18: Solicitud de vehículos oficiales (buses, camionetas, camiones, busetas, minivan), servicios de transporte', '020', '022'),
  ('19', 'Perifoneo', 'OPA 19: Perifoneo', '020', '022'),
  ('20', 'Centro administrativo municipal - CAM', 'OPA 20: Centro administrativo municipal - CAM', '020', '022'),
  ('21', 'Constancia ejecutoria (214 hacias atras)', 'OPA 21: Constancia ejecutoria (214 hacias atras)', '020', '022'),
  ('1', 'Canales de atención', 'OPA 1: Canales de atención', '020', '023'),
  ('2', 'Atención telefónica', 'OPA 2: Atención telefónica', '020', '023'),
  ('3', 'Atención virtual', 'OPA 3: Atención virtual', '020', '023'),
  ('4', 'Atención presencial', 'OPA 4: Atención presencial', '020', '023'),
  ('5', 'Punto de atención y orientación PACO', 'OPA 5: Punto de atención y orientación PACO', '020', '023'),
  ('6', 'PQRSDF', 'OPA 6: PQRSDF', '020', '023'),
  ('7', 'Trazabilidad de correspondencia', 'OPA 7: Trazabilidad de correspondencia', '020', '023'),
  ('8', 'Racionalización de trámites', 'OPA 8: Racionalización de trámites', '020', '023'),
  ('9', 'Protocolo de atención al ciudadano', 'OPA 9: Protocolo de atención al ciudadano', '020', '023'),
  ('10', 'boletas', 'OPA 10: boletas', '020', '023'),
  ('11', 'Citaciones', 'OPA 11: Citaciones', '020', '023'),
  ('12', 'Servicios de mensajería, correo certificado', 'OPA 12: Servicios de mensajería, correo certificado', '020', '023'),
  ('13', 'sistema de radicación', 'OPA 13: sistema de radicación', '020', '023'),
  ('1', 'Investigaciones procesos disciplinarios', 'OPA 1: Investigaciones procesos disciplinarios', '020', '024'),
  ('2', 'Quejas de funcionarios o ex funcionarios', 'OPA 2: Quejas de funcionarios o ex funcionarios', '020', '024'),
  ('3', 'Remisión de expedientes', 'OPA 3: Remisión de expedientes', '020', '024'),
  ('4', 'Solicitud de expediente', 'OPA 4: Solicitud de expediente', '020', '024'),
  ('5', 'Solicitud de pruebas', 'OPA 5: Solicitud de pruebas', '020', '024')
) AS v(code, name, description, dep_code, subdep_code)
JOIN dependencies d ON d.code = v.dep_code
LEFT JOIN subdependencies s ON s.dependency_id = d.id AND s.code = v.subdep_code
WHERE NOT EXISTS (
    SELECT 1 FROM opas o 
    WHERE o.code = v.code 
    AND o.dependency_id = d.id 
    AND (o.subdependency_id = s.id OR (o.subdependency_id IS NULL AND s.id IS NULL))
);


-- load_opas_030_Secretaria_de_Gobierno.sql (93 OPAs)
INSERT INTO opas (code, name, description, dependency_id, subdependency_id, is_active)
SELECT v.code, v.name, v.description, d.id, s.id, true
FROM (VALUES
  ('1', 'Recuperación y defensa del espacio publico', 'OPA 1: Recuperación y defensa del espacio publico', '030', '030'),
  ('2', 'Ventas ambulantes', 'OPA 2: Ventas ambulantes', '030', '030'),
  ('3', 'Saneamiento de bienes constitutivos como patrimonio inmobiliario', 'OPA 3: Saneamiento de bienes constitutivos como patrimonio inmobiliario', '030', '030'),
  ('4', 'Programación de gestión de riesgos', 'OPA 4: Programación de gestión de riesgos', '030', '030'),
  ('5', 'Cultura de prevención de emergencias y desastres', 'OPA 5: Cultura de prevención de emergencias y desastres', '030', '030'),
  ('6', 'Sistemas de alerta y monitoreo de fenomenos naturales', 'OPA 6: Sistemas de alerta y monitoreo de fenomenos naturales', '030', '030'),
  ('7', 'Mitigación de riesgos', 'OPA 7: Mitigación de riesgos', '030', '030'),
  ('8', 'Red de comunicaciones de emergencias', 'OPA 8: Red de comunicaciones de emergencias', '030', '030'),
  ('9', 'Protección al consumidor', 'OPA 9: Protección al consumidor', '030', '030'),
  ('10', 'Control de precios, pesas y medidas', 'OPA 10: Control de precios, pesas y medidas', '030', '030'),
  ('11', 'Rifas juegos y espectaculos', 'OPA 11: Rifas juegos y espectaculos', '030', '030'),
  ('12', 'Supervisión delegado sorteos y concursos', 'OPA 12: Supervisión delegado sorteos y concursos', '030', '030'),
  ('13', 'Coordinación con el concejo municipal', 'OPA 13: Coordinación con el concejo municipal', '030', '030'),
  ('14', 'Proyectos de integración regional', 'OPA 14: Proyectos de integración regional', '030', '030'),
  ('15', 'Comité de conocimientos y protección de riesgos para eventos masivos y no masivos', 'OPA 15: Comité de conocimientos y protección de riesgos para eventos masivos y no masivos', '030', '030'),
  ('16', 'Restitución de inmueble', 'OPA 16: Restitución de inmueble', '030', '030'),
  ('17', 'Permiso para la realización de espectáculos públicos', 'OPA 17: Permiso para la realización de espectáculos públicos', '030', '030'),
  ('18', 'Todas las peticiones enviadas por la policia nacional se remiten directamente a gobierno independientemente del tema', 'OPA 18: Todas las peticiones enviadas por la policia nacional se remiten directamente a gobierno independientemente del tema', '030', '030'),
  ('19', 'Temas realcionados con el INPEC o carceles del municipio (PPL)', 'OPA 19: Temas realcionados con el INPEC o carceles del municipio (PPL)', '030', '030'),
  ('20', 'Estacion de policia de Chia', 'OPA 20: Estacion de policia de Chia', '030', '030'),
  ('1', 'Prevención de delitos, contravenciones, problemas de convivencia y seguridad ciudadana', 'OPA 1: Prevención de delitos, contravenciones, problemas de convivencia y seguridad ciudadana', '030', '031'),
  ('2', 'Estudios sobre desplazamiento por violencia en el municipio', 'OPA 2: Estudios sobre desplazamiento por violencia en el municipio', '030', '031'),
  ('3', 'Observatorio de convivencia y seguridad', 'OPA 3: Observatorio de convivencia y seguridad', '030', '031'),
  ('4', 'Programas para la reducción de la oferta de sustancias psicoactivas', 'OPA 4: Programas para la reducción de la oferta de sustancias psicoactivas', '030', '031'),
  ('5', 'Diseño de planes de seguridad con Policía', 'OPA 5: Diseño de planes de seguridad con Policía', '030', '031'),
  ('6', 'Estrategias para el mantenimiento y restablecimiento del  orden publico', 'OPA 6: Estrategias para el mantenimiento y restablecimiento del  orden publico', '030', '031'),
  ('7', 'Coordinación procesos electorales con la Registraduría', 'OPA 7: Coordinación procesos electorales con la Registraduría', '030', '031'),
  ('8', 'Información de seguridad ciudadana', 'OPA 8: Información de seguridad ciudadana', '030', '031'),
  ('9', 'Solicitud revisión de cámaras de seguridad, copia de videos de seguridad de las camaras publicas', 'OPA 9: Solicitud revisión de cámaras de seguridad, copia de videos de seguridad de las camaras publicas', '030', '031'),
  ('10', 'Paneles de control - alarmas de seguridad - botones de pánico', 'OPA 10: Paneles de control - alarmas de seguridad - botones de pánico', '030', '031'),
  ('11', 'Línea de emergencias 123', 'OPA 11: Línea de emergencias 123', '030', '031'),
  ('12', 'Temas relacionados con informes y plataforma SYGOB - CIPRET,  Funcionaria Claudia Córdoba', 'OPA 12: Temas relacionados con informes y plataforma SYGOB - CIPRET,  Funcionaria Claudia Córdoba', '030', '031'),
  ('13', 'Migrantes, enlace Migracion Colombia', 'OPA 13: Migrantes, enlace Migracion Colombia', '030', '031'),
  ('14', 'Solicitud de Evaluación del Estudio de Nivel de Riesgo de la persona en relación de hechos que sean de conocimiento en lo
que concierne a amenazas o situaciones de riesgo', 'OPA 14: Solicitud de Evaluación del Estudio de Nivel de Riesgo de la persona en relación de hechos que sean de conocimiento en lo
que concierne a amenazas o situaciones de riesgo', '030', '031'),
  ('1', 'Validada con la dependencia 28/10/2023', 'OPA 1: Validada con la dependencia 28/10/2023', '030', '032'),
  ('2', 'Protección conocimientos tradicionales', 'OPA 2: Protección conocimientos tradicionales', '030', '032'),
  ('3', 'Comunidades étnicas, comunidades indígenas, minorías étnicas', 'OPA 3: Comunidades étnicas, comunidades indígenas, minorías étnicas', '030', '032'),
  ('4', 'Censo de población comunidades', 'OPA 4: Censo de población comunidades', '030', '032'),
  ('5', 'Resguardos indígenas', 'OPA 5: Resguardos indígenas', '030', '032'),
  ('6', 'Autoridades tradicionales', 'OPA 6: Autoridades tradicionales', '030', '032'),
  ('7', 'Resolución de conflictos (comunidades indígenas y minorías étnicas)  las minorías étnicas puden también asignarse a la DDRC', 'OPA 7: Resolución de conflictos (comunidades indígenas y minorías étnicas)  las minorías étnicas puden también asignarse a la DDRC', '030', '032'),
  ('8', 'Libertad de culto, libertad religiosa, libertad de conciencia', 'OPA 8: Libertad de culto, libertad religiosa, libertad de conciencia', '030', '032'),
  ('9', 'Protección población LGTBI (Lesbiana, Gay, Transexual, Bisexual e Intersexual)', 'OPA 9: Protección población LGTBI (Lesbiana, Gay, Transexual, Bisexual e Intersexual)', '030', '032'),
  ('10', 'Censo de establecimiento de culto religioso', 'OPA 10: Censo de establecimiento de culto religioso', '030', '032'),
  ('11', 'Enlace victimas de conflicto armado', 'OPA 11: Enlace victimas de conflicto armado', '030', '032'),
  ('12', 'Población desplazada por la violencia', 'OPA 12: Población desplazada por la violencia', '030', '032'),
  ('1', 'Validada con la dependencia 28/10/2023', 'OPA 1: Validada con la dependencia 28/10/2023', '030', '033'),
  ('2', 'Políticas para promocionar los derechos humanos', 'OPA 2: Políticas para promocionar los derechos humanos', '030', '033'),
  ('3', 'Acceso al ciudadano a la justicia', 'OPA 3: Acceso al ciudadano a la justicia', '030', '033'),
  ('4', 'Garantía de derechos y libertades individuales', 'OPA 4: Garantía de derechos y libertades individuales', '030', '033'),
  ('5', 'Casa de justicia', 'OPA 5: Casa de justicia', '030', '033'),
  ('6', 'Resolución de conflictos en ámbitos escolares y comunitarios', 'OPA 6: Resolución de conflictos en ámbitos escolares y comunitarios', '030', '033'),
  ('7', 'Sistema de información gestión de la justicia', 'OPA 7: Sistema de información gestión de la justicia', '030', '033'),
  ('8', 'Inspecciones de policía', 'OPA 8: Inspecciones de policía', '030', '033'),
  ('9', 'Asuntos policivos', 'OPA 9: Asuntos policivos', '030', '033'),
  ('10', 'Rechazo a comparendos policivos', 'OPA 10: Rechazo a comparendos policivos', '030', '033'),
  ('11', 'Contravenciones', 'OPA 11: Contravenciones', '030', '033'),
  ('12', 'Comisarias de familia', 'OPA 12: Comisarias de familia', '030', '033'),
  ('13', 'Violencia intrafamiliar', 'OPA 13: Violencia intrafamiliar', '030', '033'),
  ('14', 'Protección de niños, niñas y adolescentes', 'OPA 14: Protección de niños, niñas y adolescentes', '030', '033'),
  ('15', 'Medidas de protección', 'OPA 15: Medidas de protección', '030', '033'),
  ('16', 'Orientación jurídica', 'OPA 16: Orientación jurídica', '030', '033'),
  ('17', 'Asistencia psicológica y social para la familia y menores a través de comisarias de familia', 'OPA 17: Asistencia psicológica y social para la familia y menores a través de comisarias de familia', '030', '033'),
  ('18', 'Código de infancia y adolescencia', 'OPA 18: Código de infancia y adolescencia', '030', '033'),
  ('19', 'Comportamientos contrarios a la convivencia: de seguridad, tranquilidad, ambiente y recursos naturales, derecho de reunión, protección a los bienes y privacidad, actividad económica, urbanismo, espacio público y libertad de circulación.', 'OPA 19: Comportamientos contrarios a la convivencia: de seguridad, tranquilidad, ambiente y recursos naturales, derecho de reunión, protección a los bienes y privacidad, actividad económica, urbanismo, espacio público y libertad de circulación.', '030', '033'),
  ('20', 'Autorización de ocupación de inmuebles', 'OPA 20: Autorización de ocupación de inmuebles', '030', '033'),
  ('21', 'Reporte de registro de defunción pasadas 48 horas', 'OPA 21: Reporte de registro de defunción pasadas 48 horas', '030', '033'),
  ('22', 'Entrega de inmuebles (a través de despachos comisorios)', 'OPA 22: Entrega de inmuebles (a través de despachos comisorios)', '030', '033'),
  ('23', 'SIM Sistema de Información Misional - ICBF', 'OPA 23: SIM Sistema de Información Misional - ICBF', '030', '033'),
  ('24', 'SRD Solicitud de restitución de derechos', 'OPA 24: SRD Solicitud de restitución de derechos', '030', '033'),
  ('25', 'PARD Proceso administrativo de restablecimiento de derechos', 'OPA 25: PARD Proceso administrativo de restablecimiento de derechos', '030', '033'),
  ('26', 'Ejecutar la orden de restitución, en casos de tierras comunales (A TRAVES DE DESPACHOS COMISORIOS)', 'OPA 26: Ejecutar la orden de restitución, en casos de tierras comunales (A TRAVES DE DESPACHOS COMISORIOS)', '030', '033'),
  ('27', 'Maltrato animal', 'OPA 27: Maltrato animal', '030', '033'),
  ('28', 'Los propietarios de los predios sean requeridos persuasivamente para que de manera voluntaria , realicen la poda de los árboles y en caso de renuencia a dicha recomendación, estos asuntos serán remitidos a esta Dirección ,para que sean asignados a la Inspección de Policía', 'OPA 28: Los propietarios de los predios sean requeridos persuasivamente para que de manera voluntaria , realicen la poda de los árboles y en caso de renuencia a dicha recomendación, estos asuntos serán remitidos a esta Dirección ,para que sean asignados a la Inspección de Policía', '030', '033'),
  ('29', 'Programa comunitario de actividad pedagogica - para reducir costo de multas', 'OPA 29: Programa comunitario de actividad pedagogica - para reducir costo de multas', '030', '033'),
  ('30', 'Secuestro de bien inmuebles (A TRAVES DE DESPACHOS COMISORIOS)', 'OPA 30: Secuestro de bien inmuebles (A TRAVES DE DESPACHOS COMISORIOS)', '030', '033'),
  ('31', 'Secuestro de la cuota parte del inmueble  (A TRAVES DE DESPACHOS COMISORIOS)', 'OPA 31: Secuestro de la cuota parte del inmueble  (A TRAVES DE DESPACHOS COMISORIOS)', '030', '033'),
  ('32', 'Daños al espacio publico (asfalto por obras)', 'OPA 32: Daños al espacio publico (asfalto por obras)', '030', '033'),
  ('33', 'Situaciones presentadas en cuanto a compra de proyecto de vivienda por incumplimientos siempre y cuando especifique que va con copia para la admnistracion municipal', 'OPA 33: Situaciones presentadas en cuanto a compra de proyecto de vivienda por incumplimientos siempre y cuando especifique que va con copia para la admnistracion municipal', '030', '033'),
  ('34', 'Abandono adulto mayor y vulnerabilidad(Derecho de petición)', 'OPA 34: Abandono adulto mayor y vulnerabilidad(Derecho de petición)', '030', '033'),
  ('35', 'Amparo de pobreza', 'OPA 35: Amparo de pobreza', '030', '033'),
  ('36', 'Actualización de claves del Sistema de Registro Nacional de Medidas Correctivas - RNMC', 'OPA 36: Actualización de claves del Sistema de Registro Nacional de Medidas Correctivas - RNMC', '030', '033'),
  ('37', 'Temas relacionados con informes y plataforma SYGOB - CIPRAT, Funcionaria Claudia Córdoba', 'OPA 37: Temas relacionados con informes y plataforma SYGOB - CIPRAT, Funcionaria Claudia Córdoba', '030', '033'),
  ('1', 'Temas relacionados con informes y plataforma SYGOB - CIPRAT, Funcionaria Claudia Córdoba', 'OPA 1: Temas relacionados con informes y plataforma SYGOB - CIPRAT, Funcionaria Claudia Córdoba', '030', '330'),
  ('1', 'Temas relacionados con informes y plataforma SYGOB - CIPRAT, Funcionaria Claudia Córdoba', 'OPA 1: Temas relacionados con informes y plataforma SYGOB - CIPRAT, Funcionaria Claudia Córdoba', '030', '331'),
  ('1', 'Temas relacionados con informes y plataforma SYGOB - CIPRAT, Funcionaria Claudia Córdoba', 'OPA 1: Temas relacionados con informes y plataforma SYGOB - CIPRAT, Funcionaria Claudia Córdoba', '030', '332'),
  ('1', 'Temas relacionados con informes y plataforma SYGOB - CIPRAT, Funcionaria Claudia Córdoba', 'OPA 1: Temas relacionados con informes y plataforma SYGOB - CIPRAT, Funcionaria Claudia Córdoba', '030', '333'),
  ('1', 'Temas relacionados con informes y plataforma SYGOB - CIPRAT, Funcionaria Claudia Córdoba', 'OPA 1: Temas relacionados con informes y plataforma SYGOB - CIPRAT, Funcionaria Claudia Córdoba', '030', '334'),
  ('1', 'Temas relacionados con informes y plataforma SYGOB - CIPRAT, Funcionaria Claudia Córdoba', 'OPA 1: Temas relacionados con informes y plataforma SYGOB - CIPRAT, Funcionaria Claudia Córdoba', '030', '335'),
  ('1', 'Temas relacionados con informes y plataforma SYGOB - CIPRAT, Funcionaria Claudia Córdoba', 'OPA 1: Temas relacionados con informes y plataforma SYGOB - CIPRAT, Funcionaria Claudia Córdoba', '030', '336'),
  ('1', 'Temas relacionados con informes y plataforma SYGOB - CIPRAT, Funcionaria Claudia Córdoba', 'OPA 1: Temas relacionados con informes y plataforma SYGOB - CIPRAT, Funcionaria Claudia Córdoba', '030', '337'),
  ('1', 'Temas relacionados con informes y plataforma SYGOB - CIPRAT, Funcionaria Claudia Córdoba', 'OPA 1: Temas relacionados con informes y plataforma SYGOB - CIPRAT, Funcionaria Claudia Córdoba', '030', '338'),
  ('1', 'Temas relacionados con informes y plataforma SYGOB - CIPRAT, Funcionaria Claudia Córdoba', 'OPA 1: Temas relacionados con informes y plataforma SYGOB - CIPRAT, Funcionaria Claudia Córdoba', '030', '339')
) AS v(code, name, description, dep_code, subdep_code)
JOIN dependencies d ON d.code = v.dep_code
LEFT JOIN subdependencies s ON s.dependency_id = d.id AND s.code = v.subdep_code
WHERE NOT EXISTS (
    SELECT 1 FROM opas o 
    WHERE o.code = v.code 
    AND o.dependency_id = d.id 
    AND (o.subdependency_id = s.id OR (o.subdependency_id IS NULL AND s.id IS NULL))
);


-- Verificar carga del lote 1
SELECT 
    'LOTE 1 COMPLETADO' as estado,
    COUNT(*) as total_opas_en_lote
FROM opas o
JOIN dependencies d ON o.dependency_id = d.id
WHERE d.code IN ('010', '020', '030');

COMMIT;
